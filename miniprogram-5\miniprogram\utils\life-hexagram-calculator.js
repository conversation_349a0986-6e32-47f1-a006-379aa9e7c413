// 终身卦计算模块
// 基于出生时间的梅花易数起卦算法，用于双重验证分析

// 导入卦象数据
const { HEXAGRAM_NAMES, TRIGRAM_NAMES } = require('./hexagram-data.js');

// 八卦数字对应表
const TRIGRAM_NUMBERS = {
  1: '乾', 2: '兑', 3: '离', 4: '震',
  5: '巽', 6: '坎', 7: '艮', 8: '坤'
};

// 八卦属性表
const TRIGRAM_ATTRIBUTES = {
  '乾': { element: '金', nature: '天', direction: '西北', season: '秋冬之交' },
  '兑': { element: '金', nature: '泽', direction: '西', season: '秋' },
  '离': { element: '火', nature: '火', direction: '南', season: '夏' },
  '震': { element: '木', nature: '雷', direction: '东', season: '春' },
  '巽': { element: '木', nature: '风', direction: '东南', season: '春夏之交' },
  '坎': { element: '水', nature: '水', direction: '北', season: '冬' },
  '艮': { element: '土', nature: '山', direction: '东北', season: '冬春之交' },
  '坤': { element: '土', nature: '地', direction: '西南', season: '夏秋之交' }
};

// 六十四卦卦名表（简化版，实际应该有完整的64卦）
const HEXAGRAM_NAMES_SIMPLE = {
  11: '乾为天', 12: '天泽履', 13: '天火同人', 14: '天雷无妄',
  15: '天风姤', 16: '天水讼', 17: '天山遁', 18: '天地否',
  21: '泽天夬', 22: '兑为泽', 23: '泽火革', 24: '泽雷随',
  25: '泽风大过', 26: '泽水困', 27: '泽山咸', 28: '泽地萃',
  31: '火天大有', 32: '火泽睽', 33: '离为火', 34: '火雷噬嗑',
  35: '火风鼎', 36: '火水未济', 37: '火山旅', 38: '火地晋',
  41: '雷天大壮', 42: '雷泽归妹', 43: '雷火丰', 44: '震为雷',
  45: '雷风恒', 46: '雷水解', 47: '雷山小过', 48: '雷地豫',
  51: '风天小畜', 52: '风泽中孚', 53: '风火家人', 54: '风雷益',
  55: '巽为风', 56: '风水涣', 57: '风山渐', 58: '风地观',
  61: '水天需', 62: '水泽节', 63: '水火既济', 64: '水雷屯',
  65: '水风井', 66: '坎为水', 67: '水山蹇', 68: '水地比',
  71: '山天大畜', 72: '山泽损', 73: '山火贲', 74: '山雷颐',
  75: '山风蛊', 76: '山水蒙', 77: '艮为山', 78: '山地剥',
  81: '地天泰', 82: '地泽临', 83: '地火明夷', 84: '地雷复',
  85: '地风升', 86: '地水师', 87: '地山谦', 88: '坤为地'
};

/**
 * 计算终身卦（基于出生时间）
 * @param {Object} birthInfo - 出生信息
 * @returns {Object} 终身卦信息
 */
function calculateLifeHexagram(birthInfo) {
  const { year, month, day, hour, minute = 0 } = birthInfo;
  
  console.log('🔮 开始计算终身卦:', { year, month, day, hour, minute });

  // 梅花易数时间起卦法
  const yearNum = year;
  const monthNum = month;
  const dayNum = day;
  const hourNum = hour;
  const minuteNum = minute;

  // 计算上卦数
  const upperNum = (yearNum + monthNum + dayNum) % 8;
  const upperIndex = upperNum === 0 ? 8 : upperNum;
  const upperTrigram = TRIGRAM_NUMBERS[upperIndex];

  // 计算下卦数
  const lowerNum = (yearNum + monthNum + dayNum + hourNum) % 8;
  const lowerIndex = lowerNum === 0 ? 8 : lowerNum;
  const lowerTrigram = TRIGRAM_NUMBERS[lowerIndex];

  // 计算动爻
  const changingLineNum = (yearNum + monthNum + dayNum + hourNum + minuteNum) % 6;
  const changingLine = changingLineNum === 0 ? 6 : changingLineNum;

  // 获取本卦
  const originalHexagramKey = upperIndex * 10 + lowerIndex;
  const originalHexagramName = HEXAGRAM_NAMES_SIMPLE[originalHexagramKey] || `${upperTrigram}${lowerTrigram}`;

  // 计算变卦
  const changedHexagram = calculateChangedHexagram(upperIndex, lowerIndex, changingLine);

  // 分析卦象含义
  const analysis = analyzeLifeHexagram(originalHexagramName, upperTrigram, lowerTrigram, changingLine, changedHexagram);

  const result = {
    birthInfo: birthInfo,
    calculation: {
      upperNum: upperIndex,
      lowerNum: lowerIndex,
      changingLine: changingLine,
      formula: `上卦: (${yearNum}+${monthNum}+${dayNum})%8=${upperIndex}, 下卦: (${yearNum}+${monthNum}+${dayNum}+${hourNum})%8=${lowerIndex}, 动爻: (${yearNum}+${monthNum}+${dayNum}+${hourNum}+${minuteNum})%6=${changingLine}`
    },
    originalHexagram: {
      name: originalHexagramName,
      upperTrigram: upperTrigram,
      lowerTrigram: lowerTrigram,
      upperAttributes: TRIGRAM_ATTRIBUTES[upperTrigram],
      lowerAttributes: TRIGRAM_ATTRIBUTES[lowerTrigram],
      key: originalHexagramKey
    },
    changingLine: changingLine,
    changedHexagram: changedHexagram,
    analysis: analysis,
    timestamp: new Date().toISOString()
  };

  console.log('🔮 终身卦计算完成:', result.originalHexagram.name);
  return result;
}

/**
 * 计算变卦
 * @param {number} upperIndex - 上卦数
 * @param {number} lowerIndex - 下卦数  
 * @param {number} changingLine - 动爻位置
 * @returns {Object} 变卦信息
 */
function calculateChangedHexagram(upperIndex, lowerIndex, changingLine) {
  // 根据动爻位置计算变卦
  let changedUpperIndex = upperIndex;
  let changedLowerIndex = lowerIndex;

  // 动爻在下卦（1、2、3爻）
  if (changingLine <= 3) {
    // 下卦变化逻辑（简化）
    changedLowerIndex = (lowerIndex % 8) + 1;
    if (changedLowerIndex > 8) changedLowerIndex = 1;
  } else {
    // 上卦变化逻辑（4、5、6爻）
    changedUpperIndex = (upperIndex % 8) + 1;
    if (changedUpperIndex > 8) changedUpperIndex = 1;
  }

  const changedUpperTrigram = TRIGRAM_NUMBERS[changedUpperIndex];
  const changedLowerTrigram = TRIGRAM_NUMBERS[changedLowerIndex];
  const changedHexagramKey = changedUpperIndex * 10 + changedLowerIndex;
  const changedHexagramName = HEXAGRAM_NAMES_SIMPLE[changedHexagramKey] || `${changedUpperTrigram}${changedLowerTrigram}`;

  return {
    name: changedHexagramName,
    upperTrigram: changedUpperTrigram,
    lowerTrigram: changedLowerTrigram,
    upperAttributes: TRIGRAM_ATTRIBUTES[changedUpperTrigram],
    lowerAttributes: TRIGRAM_ATTRIBUTES[changedLowerTrigram],
    key: changedHexagramKey
  };
}

/**
 * 分析终身卦含义
 * @param {string} hexagramName - 卦名
 * @param {string} upperTrigram - 上卦
 * @param {string} lowerTrigram - 下卦
 * @param {number} changingLine - 动爻
 * @param {Object} changedHexagram - 变卦
 * @returns {Object} 分析结果
 */
function analyzeLifeHexagram(hexagramName, upperTrigram, lowerTrigram, changingLine, changedHexagram) {
  const upperAttr = TRIGRAM_ATTRIBUTES[upperTrigram];
  const lowerAttr = TRIGRAM_ATTRIBUTES[lowerTrigram];

  // 基础性格分析
  const personality = analyzePersonality(upperTrigram, lowerTrigram);
  
  // 五行分析
  const elementAnalysis = analyzeElements(upperAttr.element, lowerAttr.element);
  
  // 动爻分析
  const changingLineAnalysis = analyzeChangingLine(changingLine, hexagramName);
  
  // 变卦趋势分析
  const trendAnalysis = analyzeTrend(hexagramName, changedHexagram.name);

  return {
    hexagramName: hexagramName,
    personality: personality,
    elementAnalysis: elementAnalysis,
    changingLineAnalysis: changingLineAnalysis,
    trendAnalysis: trendAnalysis,
    lifeTheme: `${upperAttr.nature}${lowerAttr.nature}之象，主${personality.mainTrait}`,
    keyInsights: [
      `上卦${upperTrigram}主外在表现，性格偏${personality.upperTrait}`,
      `下卦${lowerTrigram}主内在本质，本性偏${personality.lowerTrait}`,
      `${changingLine}爻动，${changingLineAnalysis.meaning}`,
      `变卦${changedHexagram.name}，${trendAnalysis.direction}`
    ]
  };
}

/**
 * 分析性格特征
 */
function analyzePersonality(upperTrigram, lowerTrigram) {
  const personalityMap = {
    '乾': '刚健进取', '兑': '开朗活泼', '离': '聪明热情', '震': '积极主动',
    '巽': '温和谦逊', '坎': '智慧深沉', '艮': '稳重踏实', '坤': '包容温厚'
  };

  return {
    upperTrait: personalityMap[upperTrigram],
    lowerTrait: personalityMap[lowerTrigram],
    mainTrait: `${personalityMap[upperTrigram]}而${personalityMap[lowerTrigram]}`
  };
}

/**
 * 分析五行关系
 */
function analyzeElements(upperElement, lowerElement) {
  const elementRelations = {
    '金金': '金旺，主决断力强，但易过刚',
    '金木': '金克木，主有压力，需要调和',
    '金水': '金生水，主智慧流畅，发展顺利',
    '金火': '火克金，主有阻力，需要坚持',
    '金土': '土生金，主有贵人，基础稳固',
    '木木': '木旺，主生机勃勃，但易冲动',
    '木水': '水生木，主滋养成长，发展良好',
    '木火': '木生火，主热情奔放，创造力强',
    '木土': '木克土，主有冲突，需要包容',
    '木金': '金克木，主受制约，需要突破',
    '水水': '水旺，主智慧深邃，但易多变',
    '水火': '水火不容，主内心矛盾，需要平衡',
    '水土': '土克水，主受阻碍，需要疏通',
    '水金': '金生水，主思维敏捷，贵人相助',
    '水木': '水生木，主成长顺利，前景光明',
    '火火': '火旺，主热情高涨，但易急躁',
    '火土': '火生土，主踏实发展，成果丰硕',
    '火金': '火克金，主有冲突，需要调节',
    '火木': '木生火，主创意无限，发展迅速',
    '火水': '水火不容，主波动较大，需要稳定',
    '土土': '土旺，主稳重可靠，但易固执',
    '土金': '土生金，主厚德载物，收获丰富',
    '土木': '木克土，主变化较多，需要适应',
    '土水': '土克水，主控制力强，但需要灵活',
    '土火': '火生土，主温暖踏实，发展稳定'
  };

  const key = upperElement + lowerElement;
  return {
    relationship: key,
    analysis: elementRelations[key] || '五行关系需要进一步分析'
  };
}

/**
 * 分析动爻含义
 */
function analyzeChangingLine(changingLine, hexagramName) {
  const lineAnalysis = {
    1: { position: '初爻', meaning: '起始阶段，基础变化，影响根本' },
    2: { position: '二爻', meaning: '发展阶段，人事变化，影响人际' },
    3: { position: '三爻', meaning: '转折阶段，环境变化，影响处境' },
    4: { position: '四爻', meaning: '上升阶段，地位变化，影响事业' },
    5: { position: '五爻', meaning: '成功阶段，权力变化，影响成就' },
    6: { position: '上爻', meaning: '极限阶段，结果变化，影响结局' }
  };

  return lineAnalysis[changingLine] || { position: '未知', meaning: '需要进一步分析' };
}

/**
 * 分析变化趋势
 */
function analyzeTrend(originalName, changedName) {
  // 简化的趋势分析
  return {
    from: originalName,
    to: changedName,
    direction: `从${originalName}向${changedName}发展，表示人生轨迹的重要转变`
  };
}

/**
 * 获取终身卦的命运主题
 * @param {Object} lifeHexagram - 终身卦信息
 * @returns {string} 命运主题
 */
function getLifeTheme(lifeHexagram) {
  if (!lifeHexagram || !lifeHexagram.analysis) {
    return '命运主题待分析';
  }
  
  return lifeHexagram.analysis.lifeTheme || '命运主题待分析';
}

/**
 * 验证终身卦与八字的一致性
 * @param {Object} lifeHexagram - 终身卦
 * @param {Object} bazi - 八字信息
 * @returns {Object} 一致性分析
 */
function verifyConsistency(lifeHexagram, bazi) {
  if (!lifeHexagram || !bazi) {
    return { consistency: 0, analysis: '数据不完整，无法验证' };
  }

  // 五行一致性检查
  const hexagramElements = [
    lifeHexagram.originalHexagram.upperAttributes.element,
    lifeHexagram.originalHexagram.lowerAttributes.element
  ];

  // 简化的一致性分析
  let consistencyScore = 50; // 基础分
  let analysisPoints = [];

  // 检查五行匹配度
  if (bazi.dayMasterElement) {
    const dayMasterElement = bazi.dayMasterElement;
    if (hexagramElements.includes(dayMasterElement)) {
      consistencyScore += 20;
      analysisPoints.push(`日主${dayMasterElement}与卦象五行相符`);
    }
  }

  // 检查性格特征匹配度
  if (bazi.pattern && lifeHexagram.analysis.personality) {
    consistencyScore += 15;
    analysisPoints.push('性格特征与八字格局基本一致');
  }

  return {
    consistency: Math.min(100, consistencyScore),
    analysis: analysisPoints.join('；') || '需要更详细的对比分析',
    hexagramElements: hexagramElements,
    recommendation: consistencyScore > 70 ? '双重验证一致性较高' : '建议进一步核实分析'
  };
}

module.exports = {
  calculateLifeHexagram,
  calculateChangedHexagram,
  analyzeLifeHexagram,
  getLifeTheme,
  verifyConsistency,
  TRIGRAM_NUMBERS,
  TRIGRAM_ATTRIBUTES,
  HEXAGRAM_NAMES_SIMPLE
};
