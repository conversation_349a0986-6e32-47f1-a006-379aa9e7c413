// 知识库模块 - Node.js兼容版本
// 用于测试和验证知识库功能

const fs = require('fs');
const path = require('path');

// 模拟微信小程序的数据库接口
class MockDatabase {
  constructor() {
    this.collections = new Map();
    this.initialized = false;
  }

  collection(name) {
    if (!this.collections.has(name)) {
      this.collections.set(name, new MockCollection(name));
    }
    return this.collections.get(name);
  }
}

class MockCollection {
  constructor(name) {
    this.name = name;
    this.data = [];
    this.loadData();
  }

  loadData() {
    try {
      // 尝试从本地文件加载数据
      const dataPath = path.join(__dirname, `../../../knowledge-base-${this.name}.json`);
      if (fs.existsSync(dataPath)) {
        const rawData = fs.readFileSync(dataPath, 'utf8');
        this.data = JSON.parse(rawData);
        console.log(`📚 加载知识库 ${this.name}: ${this.data.length} 条记录`);
      } else {
        console.log(`⚠️ 知识库文件不存在: ${dataPath}`);
      }
    } catch (error) {
      console.error(`❌ 加载知识库 ${this.name} 失败:`, error.message);
    }
  }

  where(conditions) {
    return new MockQuery(this.data, conditions);
  }

  get() {
    return Promise.resolve({
      data: this.data
    });
  }
}

class MockQuery {
  constructor(data, conditions) {
    this.data = data;
    this.conditions = conditions;
    this.limitCount = 100;
  }

  limit(count) {
    this.limitCount = count;
    return this;
  }

  get() {
    let filteredData = this.data;

    // 应用过滤条件
    if (this.conditions) {
      Object.keys(this.conditions).forEach(key => {
        const value = this.conditions[key];
        if (typeof value === 'object' && value.$regex) {
          const regex = new RegExp(value.$regex, value.$options || 'i');
          filteredData = filteredData.filter(item => 
            item[key] && regex.test(item[key])
          );
        } else {
          filteredData = filteredData.filter(item => item[key] === value);
        }
      });
    }

    // 应用限制
    filteredData = filteredData.slice(0, this.limitCount);

    return Promise.resolve({
      data: filteredData
    });
  }
}

// 创建模拟数据库实例
const mockDb = new MockDatabase();

// 知识库配置
const KNOWLEDGE_CONFIG = {
  collections: {
    bazi: 'bazi_knowledge',
    yijing: 'yijing_knowledge', 
    meihua: 'meihua_knowledge',
    ziwei: 'ziwei_knowledge'
  },
  searchLimit: 20,
  semanticThreshold: 0.6
};

/**
 * 初始化数据库（Node.js版本）
 */
function initDatabase() {
  console.log('🔧 初始化知识库（Node.js模式）...');
  return Promise.resolve(mockDb);
}

/**
 * 搜索知识库内容
 * @param {string} query - 搜索关键词
 * @param {string} category - 分类
 * @returns {Promise<Array>} 搜索结果
 */
async function searchKnowledge(query, category = '') {
  try {
    console.log(`🔍 搜索知识库: "${query}" (分类: ${category})`);
    
    const db = await initDatabase();
    let results = [];

    // 确定搜索的集合
    const collections = category ? [category] : Object.values(KNOWLEDGE_CONFIG.collections);
    
    for (const collectionName of collections) {
      try {
        const collection = db.collection(collectionName);
        
        // 构建搜索条件
        const searchConditions = {
          $or: [
            { title: { $regex: query, $options: 'i' } },
            { content: { $regex: query, $options: 'i' } },
            { keywords: { $regex: query, $options: 'i' } }
          ]
        };

        // 由于模拟版本不支持复杂查询，简化搜索
        const allData = await collection.get();
        const filtered = allData.data.filter(item => {
          const searchText = `${item.title || ''} ${item.content || ''} ${item.keywords || ''}`.toLowerCase();
          return searchText.includes(query.toLowerCase());
        });

        results = results.concat(filtered.slice(0, KNOWLEDGE_CONFIG.searchLimit));
        
      } catch (error) {
        console.error(`❌ 搜索集合 ${collectionName} 失败:`, error.message);
      }
    }

    console.log(`✅ 搜索完成，找到 ${results.length} 条结果`);
    return results;

  } catch (error) {
    console.error('❌ 搜索知识库失败:', error);
    return [];
  }
}

/**
 * 根据问题类型获取相关知识
 * @param {string} questionType - 问题类型
 * @param {string} specificQuery - 具体查询内容
 * @param {object} context - 上下文信息
 * @returns {Promise<Array>} 相关知识内容
 */
async function getRelevantKnowledge(questionType, specificQuery = '', context = {}) {
  try {
    console.log(`🎯 获取相关知识: ${questionType} - ${specificQuery}`);
    
    // 问题类型到分类的映射
    const categoryMap = {
      '财运': 'bazi_knowledge',
      '事业': 'bazi_knowledge',
      '婚姻': 'bazi_knowledge',
      '健康': 'bazi_knowledge',
      '学业': 'bazi_knowledge',
      '卦象': 'yijing_knowledge',
      '六爻': 'yijing_knowledge',
      '梅花': 'meihua_knowledge',
      '紫微': 'ziwei_knowledge'
    };

    const category = categoryMap[questionType] || '';
    
    // 构建搜索关键词
    const keywords = [questionType, specificQuery].filter(k => k).join(' ');
    
    // 搜索相关知识
    const results = await searchKnowledge(keywords, category);
    
    // 添加相关性评分
    const scoredResults = results.map(item => ({
      ...item,
      semanticScore: Math.floor(Math.random() * 40) + 60, // 模拟评分 60-100
      relevanceReason: `与${questionType}相关`
    }));

    console.log(`✅ 获取到 ${scoredResults.length} 条相关知识`);
    return scoredResults;

  } catch (error) {
    console.error('❌ 获取相关知识失败:', error);
    return [];
  }
}

/**
 * 获取知识库统计信息
 */
async function getKnowledgeStats() {
  try {
    const db = await initDatabase();
    const stats = {};
    
    for (const [key, collectionName] of Object.entries(KNOWLEDGE_CONFIG.collections)) {
      try {
        const collection = db.collection(collectionName);
        const result = await collection.get();
        stats[key] = result.data.length;
      } catch (error) {
        stats[key] = 0;
      }
    }
    
    return stats;
  } catch (error) {
    console.error('❌ 获取知识库统计失败:', error);
    return {};
  }
}

module.exports = {
  initDatabase,
  searchKnowledge,
  getRelevantKnowledge,
  getKnowledgeStats,
  KNOWLEDGE_CONFIG
};
