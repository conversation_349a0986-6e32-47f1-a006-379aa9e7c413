// 云函数：knowledge-search
// 用于搜索知识库内容
const cloud = require('wx-server-sdk');

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
});

const db = cloud.database();
const _ = db.command;

// 添加详细的日志记录函数
function logInfo(message, data = null) {
  console.log(`[INFO] ${new Date().toISOString()} - ${message}`, data || '');
}

function logError(message, error = null) {
  console.error(`[ERROR] ${new Date().toISOString()} - ${message}`, error || '');
}

function logWarning(message, data = null) {
  console.warn(`[WARNING] ${new Date().toISOString()} - ${message}`, data || '');
}

/**
 * 云函数入口函数
 */
exports.main = async (event, context) => {
  const { query, category, limit = 20, action = 'search' } = event;

  logInfo('知识库搜索云函数被调用', { action, query, category, limit });

  try {
    // 如果是统计请求
    if (action === 'count') {
      logInfo('执行数据库统计查询');

      const countResult = await db.collection('knowledge_base').count();
      logInfo('统计查询完成', { total: countResult.total });

      // 只返回基本信息，不返回完整内容
      const sampleResult = await db.collection('knowledge_base')
        .field({ title: true, category: true, file_size: true, created_at: true })
        .limit(3)
        .get();
      logInfo('示例数据查询完成', { sampleCount: sampleResult.data.length });

      return {
        success: true,
        total: countResult.total,
        sample: sampleResult.data.map(item => ({
          id: item._id,
          title: item.title,
          category: item.category,
          file_size: item.file_size,
          created_at: item.created_at
        })),
        timestamp: new Date().toISOString()
      };
    }

    // 如果是AI语义检索请求
    if (action === 'semantic_search') {
      logInfo('执行AI语义检索', { query, category });
      return await performSemanticSearch(query, category, limit);
    }

    // 构建查询条件
    let whereCondition = {};
    
    // 分类筛选
    if (category && category !== 'all') {
      whereCondition.category = category;
    }
    
    // 关键词搜索
    if (query && query.trim()) {
      const searchRegex = new RegExp(query.trim(), 'i');
      whereCondition = {
        ...whereCondition,
        $or: [
          { title: searchRegex },
          { author: searchRegex },
          { content: searchRegex },
          { keywords: searchRegex }
        ]
      };
    }
    
    // 执行查询 - 先不包含content字段避免1MB限制
    const result = await db.collection('knowledge_base')
      .where(whereCondition)
      .field({
        title: true,
        file_path: true,
        category: true,
        author: true,
        dynasty: true,
        file_size: true,
        batch_info: true,
        created_at: true,
        keywords: true,
        _id: true
        // 不包含content字段，避免数据过大
      })
      .limit(limit)
      .orderBy('created_at', 'desc')
      .get();
    
    // 如果有搜索关键词，计算相关度并排序
    if (query && query.trim()) {
      const searchTerms = query.toLowerCase().split(/\s+/);

      // 为每个结果获取内容片段并计算相关度
      result.data = await Promise.all(result.data.map(async (item) => {
        let score = 0;
        const title = (item.title || '').toLowerCase();
        const author = (item.author || '').toLowerCase();

        // 计算基础匹配分数
        searchTerms.forEach(term => {
          // 标题匹配权重最高
          if (title.includes(term)) score += 10;
          // 作者匹配权重中等
          if (author.includes(term)) score += 5;
        });

        // 单独获取内容进行匹配和片段提取
        let contentSnippet = '';
        let matchedSnippets = [];

        try {
          const contentResult = await db.collection('knowledge_base')
            .doc(item._id)
            .field({ content: true })
            .get();

          const content = contentResult.data.content || '';
          const contentLower = content.toLowerCase();

          // 计算内容匹配分数
          searchTerms.forEach(term => {
            const contentMatches = (contentLower.match(new RegExp(term, 'g')) || []).length;
            score += contentMatches;
          });

          // 提取匹配的内容片段
          const contentLines = content.split('\n');
          searchTerms.forEach(term => {
            const matchingLines = contentLines.filter(line =>
              line.toLowerCase().includes(term) && line.trim().length > 10
            );
            matchedSnippets.push(...matchingLines.slice(0, 2));
          });

          // 截断内容片段
          contentSnippet = content.length > 500
            ? content.substring(0, 500) + '...[查看更多]'
            : content;

        } catch (err) {
          logWarning('获取内容片段失败', { id: item._id, error: err.message });
          contentSnippet = '[内容获取失败]';
        }

        return {
          ...item,
          content: contentSnippet,
          score,
          relevance: Math.min(score / searchTerms.length, 100),
          matchedSnippets: matchedSnippets.slice(0, 3)
        };
      }));

      // 按相关度排序
      result.data.sort((a, b) => b.score - a.score);
    } else {
      // 如果没有搜索关键词，为每个结果添加简短的内容预览
      result.data = await Promise.all(result.data.map(async (item) => {
        try {
          const contentResult = await db.collection('knowledge_base')
            .doc(item._id)
            .field({ content: true })
            .get();

          const content = contentResult.data.content || '';
          const contentSnippet = content.length > 300
            ? content.substring(0, 300) + '...[查看更多]'
            : content;

          return {
            ...item,
            content: contentSnippet
          };
        } catch (err) {
          logWarning('获取内容预览失败', { id: item._id, error: err.message });
          return {
            ...item,
            content: '[内容获取失败]'
          };
        }
      }));
    }
    
    logInfo('搜索查询完成', { resultCount: result.data.length });

    return {
      success: true,
      data: result.data,
      total: result.data.length,
      timestamp: new Date().toISOString()
    };

  } catch (error) {
    logError('知识库搜索失败', {
      message: error.message,
      stack: error.stack,
      event: event
    });

    return {
      success: false,
      error: error.message,
      errorCode: error.errCode || 'UNKNOWN_ERROR',
      data: [],
      timestamp: new Date().toISOString()
    };
  }
};

/**
 * 执行AI语义检索
 * @param {string} query - 语义查询内容
 * @param {string} category - 分类筛选
 * @param {number} limit - 返回数量限制
 * @returns {Promise<Object>} 语义检索结果
 */
async function performSemanticSearch(query, category, limit) {
  try {
    logInfo('开始AI语义检索', { query, category, limit });

    // 构建查询条件
    let whereCondition = {};
    if (category && category !== 'all') {
      whereCondition.category = category;
    }

    // 获取候选文档（先获取较多数量用于语义分析）
    const candidateResult = await db.collection('knowledge_base')
      .where(whereCondition)
      .field({
        title: true,
        file_path: true,
        category: true,
        author: true,
        dynasty: true,
        file_size: true,
        batch_info: true,
        created_at: true,
        keywords: true,
        _id: true
      })
      .limit(Math.min(limit * 5, 100)) // 获取更多候选文档
      .orderBy('created_at', 'desc')
      .get();

    if (!candidateResult.data || candidateResult.data.length === 0) {
      return {
        success: true,
        data: [],
        total: 0,
        timestamp: new Date().toISOString()
      };
    }

    // 使用DeepSeek API进行语义相关性分析
    const semanticResults = await analyzeSemanticRelevance(query, candidateResult.data);

    // 获取最相关文档的内容片段
    const finalResults = await Promise.all(semanticResults.slice(0, limit).map(async (item) => {
      try {
        const contentResult = await db.collection('knowledge_base')
          .doc(item._id)
          .field({ content: true })
          .get();

        const content = contentResult.data.content || '';
        const contentSnippet = extractRelevantSnippet(content, query);

        return {
          ...item,
          content: contentSnippet,
          semanticScore: item.semanticScore || 0
        };
      } catch (err) {
        logWarning('获取语义检索内容失败', { id: item._id, error: err.message });
        return {
          ...item,
          content: '[内容获取失败]',
          semanticScore: item.semanticScore || 0
        };
      }
    }));

    logInfo('AI语义检索完成', { resultCount: finalResults.length });

    return {
      success: true,
      data: finalResults,
      total: finalResults.length,
      timestamp: new Date().toISOString()
    };

  } catch (error) {
    logError('AI语义检索失败', error);
    return {
      success: false,
      error: error.message || 'AI语义检索失败',
      timestamp: new Date().toISOString()
    };
  }
}

/**
 * 使用DeepSeek API分析语义相关性
 * @param {string} query - 查询内容
 * @param {Array} candidates - 候选文档
 * @returns {Promise<Array>} 按相关性排序的结果
 */
async function analyzeSemanticRelevance(query, candidates) {
  try {
    // 构建语义分析提示词
    const analysisPrompt = buildSemanticAnalysisPrompt(query, candidates);

    // 调用DeepSeek API（这里需要配置API密钥）
    const DEEPSEEK_API_KEY = '***********************************';
    const DEEPSEEK_API_URL = 'https://api.deepseek.com/v1/chat/completions';

    const response = await new Promise((resolve, reject) => {
      const https = require('https');
      const postData = JSON.stringify({
        model: 'deepseek-reasoner',
        messages: [
          {
            role: 'system',
            content: '你是一位精通中国传统文化和古籍的专家，能够准确判断古籍内容与用户问题的相关性。请按照相关性从高到低排序，返回JSON格式的结果。'
          },
          {
            role: 'user',
            content: analysisPrompt
          }
        ],
        temperature: 0.2,
        max_tokens: 65536
      });

      const options = {
        hostname: 'api.deepseek.com',
        port: 443,
        path: '/v1/chat/completions',
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${DEEPSEEK_API_KEY}`,
          'Content-Length': Buffer.byteLength(postData)
        }
      };

      const req = https.request(options, (res) => {
        let data = '';
        res.on('data', (chunk) => {
          data += chunk;
        });
        res.on('end', () => {
          try {
            const result = JSON.parse(data);
            resolve(result);
          } catch (e) {
            reject(e);
          }
        });
      });

      req.on('error', (e) => {
        reject(e);
      });

      req.write(postData);
      req.end();
    });

    if (response.choices && response.choices.length > 0) {
      const analysisResult = response.choices[0].message.content;
      return parseSemanticAnalysisResult(analysisResult, candidates);
    } else {
      logWarning('DeepSeek API返回结果为空，使用传统排序');
      return candidates.map((item, index) => ({ ...item, semanticScore: candidates.length - index }));
    }

  } catch (error) {
    logWarning('语义相关性分析失败，使用传统排序', error);
    // 降级到传统排序
    return candidates.map((item, index) => ({ ...item, semanticScore: candidates.length - index }));
  }
}

/**
 * 构建语义分析提示词
 * @param {string} query - 查询内容
 * @param {Array} candidates - 候选文档
 * @returns {string} 分析提示词
 */
function buildSemanticAnalysisPrompt(query, candidates) {
  let prompt = `用户问题：${query}\n\n请分析以下古籍文档与用户问题的相关性，按相关性从高到低排序：\n\n`;

  candidates.forEach((doc, index) => {
    prompt += `文档${index + 1}：\n`;
    prompt += `- ID: ${doc._id}\n`;
    prompt += `- 标题: ${doc.title || '无标题'}\n`;
    prompt += `- 作者: ${doc.author || '未知'}\n`;
    prompt += `- 朝代: ${doc.dynasty || '未知'}\n`;
    prompt += `- 分类: ${doc.category || '其他'}\n\n`;
  });

  prompt += `请返回JSON格式的排序结果，包含每个文档的ID和相关性分数（0-100）：\n`;
  prompt += `格式：[{"id": "文档ID", "score": 分数, "reason": "相关性原因"}, ...]\n`;
  prompt += `只返回JSON，不要其他解释文字。`;

  return prompt;
}

/**
 * 解析语义分析结果
 * @param {string} analysisResult - AI分析结果
 * @param {Array} candidates - 候选文档
 * @returns {Array} 排序后的结果
 */
function parseSemanticAnalysisResult(analysisResult, candidates) {
  try {
    // 尝试解析JSON结果
    const jsonMatch = analysisResult.match(/\[[\s\S]*\]/);
    if (!jsonMatch) {
      throw new Error('无法找到JSON格式的结果');
    }

    const rankings = JSON.parse(jsonMatch[0]);

    // 创建ID到候选文档的映射
    const candidateMap = new Map();
    candidates.forEach(doc => {
      candidateMap.set(doc._id, doc);
    });

    // 按AI分析结果排序
    const sortedResults = rankings
      .filter(ranking => candidateMap.has(ranking.id))
      .map(ranking => ({
        ...candidateMap.get(ranking.id),
        semanticScore: ranking.score || 0,
        relevanceReason: ranking.reason || ''
      }))
      .sort((a, b) => b.semanticScore - a.semanticScore);

    // 添加未被AI分析的文档（分数较低）
    const analyzedIds = new Set(rankings.map(r => r.id));
    const unanalyzedDocs = candidates
      .filter(doc => !analyzedIds.has(doc._id))
      .map(doc => ({ ...doc, semanticScore: 10, relevanceReason: '未分析' }));

    return [...sortedResults, ...unanalyzedDocs];

  } catch (error) {
    logWarning('解析语义分析结果失败', error);
    // 降级到传统排序
    return candidates.map((item, index) => ({
      ...item,
      semanticScore: candidates.length - index,
      relevanceReason: '解析失败，使用传统排序'
    }));
  }
}

/**
 * 提取相关内容片段
 * @param {string} content - 完整内容
 * @param {string} query - 查询关键词
 * @returns {string} 相关片段
 */
function extractRelevantSnippet(content, query) {
  if (!content || !query) {
    return content.length > 300 ? content.substring(0, 300) + '...[查看更多]' : content;
  }

  const queryTerms = query.split(/\s+/).filter(term => term.length > 1);
  const lines = content.split('\n');
  const relevantLines = [];

  // 查找包含查询词的行
  for (const line of lines) {
    const lineText = line.trim();
    if (lineText.length < 10) continue;

    for (const term of queryTerms) {
      if (lineText.includes(term)) {
        relevantLines.push(lineText);
        break;
      }
    }

    if (relevantLines.length >= 3) break;
  }

  if (relevantLines.length > 0) {
    const snippet = relevantLines.join('\n');
    return snippet.length > 500 ? snippet.substring(0, 500) + '...[查看更多]' : snippet;
  }

  // 如果没有找到相关行，返回开头部分
  return content.length > 300 ? content.substring(0, 300) + '...[查看更多]' : content;
}
