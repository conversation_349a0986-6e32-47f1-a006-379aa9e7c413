// pages/ziwei/ziwei.js - 紫微斗数页面
const app = getApp();

const {
  calculateZiweiChart,
  formatZ<PERSON>wei<PERSON>hart,
  TWELVE_PALACES
} = require('../../utils/ziwei-calculator.js');

const {
  comprehensiveZiweiAnalysis,
  calculateMing<PERSON>hu,
  calculateShen<PERSON>hu,
  calculateWuXingJu,
  analyzeAppearance,
  analyzePersonality
} = require('../../utils/ziwei-analysis.js');

const {
  generateZiweiAnalysis
} = require('../../utils/question-analysis.js');

const {
  analyzeZiweiWithAI,
  analyzeZiweiWithDualVerification
} = require('../../utils/ai-service.js');

const {
  conversationManager
} = require('../../utils/conversation-manager.js');

const {
  calculateZiweiStartLuck,
  calculateZiweiDaxian,
  getCurrentDaxian
} = require('../../utils/ziwei-luck.js');

const {
  intelligentInquiry
} = require('../../utils/intelligent-inquiry.js');

const {
  getCityList,
  getCityCoordinates,
  calculateTrueSolarTime,
  formatSolarTimeExplanation,
  shouldUseTrueSolarTime,
  getTimeHour
} = require('../../utils/solar-time.js');

Page({

  /**
   * 页面的初始数据
   */
  data: {
    // 输入信息
    question: '',
    birthDate: '',
    birthTime: '',
    isMale: true,

    // 出生地信息
    cityList: [],
    selectedCity: '',
    selectedCityIndex: 0,

    // 真太阳时信息
    useTrueSolarTime: false,
    solarTimeResult: null,
    solarTimeExplanation: '',
    trueSolarTimeString: '',

    // 紫微斗数信息
    ziweiData: null,
    formattedChart: null,

    // 分析结果
    analysis: null,
    customAnalysis: null,

    // 界面状态
    isAnalyzing: false,
    showResult: false,

    // 命盘显示
    palaceList: TWELVE_PALACES,

    // 多轮对话相关
    conversationMode: false, // 是否开启对话模式
    sessionId: null, // 对话会话ID
    conversationHistory: [], // 对话历史
    followUpQuestions: [], // 追问问题列表
    currentFollowUp: null, // 当前追问问题
    isWaitingResponse: false, // 是否等待用户回答
    showConversationPanel: false, // 是否显示对话面板
    conversationInput: '', // 对话输入框内容
    isTyping: false // AI是否正在"打字"
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    // 设置默认值
    const now = new Date();
    const cityList = getCityList();
    this.setData({
      birthDate: now.toISOString().split('T')[0],
      birthTime: '12:00',
      cityList: cityList,
      selectedCity: '北京',
      selectedCityIndex: 0
    });
  },

  // 输入问题
  onQuestionInput(e) {
    this.setData({
      question: e.detail.value
    });
  },

  // 选择出生日期
  onDateChange(e) {
    this.setData({
      birthDate: e.detail.value
    });
  },

  // 选择出生时间
  onTimeChange(e) {
    this.setData({
      birthTime: e.detail.value
    });
    this.calculateSolarTime();
  },

  // 选择出生地
  onCityChange(e) {
    const index = e.detail.value;
    const city = this.data.cityList[index];
    this.setData({
      selectedCityIndex: index,
      selectedCity: city.name
    });
    this.calculateSolarTime();
  },

  // 计算真太阳时
  calculateSolarTime() {
    const { birthDate, birthTime, selectedCity } = this.data;

    if (!birthDate || !birthTime || !selectedCity) {
      return;
    }

    // 构建出生时间
    const birthDateTime = new Date(`${birthDate}T${birthTime}:00`);

    // 获取城市坐标
    const coordinates = getCityCoordinates(selectedCity);
    if (!coordinates) {
      return;
    }

    // 计算真太阳时
    const solarTimeResult = calculateTrueSolarTime(birthDateTime, coordinates.longitude);
    const shouldUse = shouldUseTrueSolarTime(solarTimeResult);
    const explanation = formatSolarTimeExplanation(solarTimeResult);

    // 格式化真太阳时字符串
    const trueSolarTime = solarTimeResult.trueSolarTime;
    const trueSolarTimeString = `${trueSolarTime.getHours().toString().padStart(2, '0')}:${trueSolarTime.getMinutes().toString().padStart(2, '0')}`;

    this.setData({
      solarTimeResult: solarTimeResult,
      useTrueSolarTime: shouldUse,
      solarTimeExplanation: explanation,
      trueSolarTimeString: trueSolarTimeString
    });
  },

  // 选择性别
  onGenderChange(e) {
    this.setData({
      isMale: e.detail.value === '男'
    });
  },

  // 开始排盘
  onStartAnalysis() {
    if (!this.data.question.trim()) {
      wx.showToast({
        title: '请输入要问的问题',
        icon: 'none'
      });
      return;
    }

    if (!this.data.birthDate || !this.data.birthTime) {
      wx.showToast({
        title: '请选择出生日期和时间',
        icon: 'none'
      });
      return;
    }

    this.setData({
      isAnalyzing: true,
      showResult: false
    });

    // 显示等待提示
    wx.showLoading({
      title: '正在分析紫微斗数...',
      mask: true
    });

    // 计算紫微斗数
    this.calculateZiweiChart();
  },

  // 计算紫微斗数排盘
  calculateZiweiChart() {
    try {
      // 确定使用的时间（真太阳时或北京时间）
      let calculationTime;
      if (this.data.useTrueSolarTime && this.data.solarTimeResult) {
        calculationTime = this.data.solarTimeResult.trueSolarTime;
        console.log('使用真太阳时计算紫微斗数:', calculationTime);
      } else {
        calculationTime = new Date(`${this.data.birthDate}T${this.data.birthTime}:00`);
        console.log('使用北京时间计算紫微斗数:', calculationTime);
      }

      // 计算紫微斗数命盘
      const ziweiData = calculateZiweiChart(calculationTime, this.data.isMale);
      const formattedChart = formatZiweiChart(ziweiData);

      // 进行紫微斗数分析
      const analysis = comprehensiveZiweiAnalysis(ziweiData);
      console.log('🔮 紫微分析结果:', analysis);
      console.log('🔮 综合评分:', analysis.overallScore);

      // 进行精准问题分析
      const customAnalysis = generateZiweiAnalysis(
        this.data.question,
        ziweiData,
        analysis,
        this.data.isMale
      );
      console.log('🎯 精准分析结果:', customAnalysis);

      // 计算大限信息
      const currentAge = new Date().getFullYear() - calculationTime.getFullYear();
      console.log('🔮 当前年龄计算:', currentAge);

      // 获取年干信息
      const yearStem = ziweiData.year ? ziweiData.year.stem : '甲';

      // 计算大限起运信息
      const startLuck = calculateZiweiStartLuck(ziweiData, this.data.isMale, yearStem);
      console.log('🔮 大限起运信息:', startLuck);

      // 计算大限列表
      const daxianList = calculateZiweiDaxian(startLuck);
      console.log('🔮 大限列表:', daxianList);

      // 获取当前大限
      const currentDaxian = getCurrentDaxian(daxianList, currentAge) || daxianList[0];
      console.log('🔮 当前大限:', currentDaxian);

      this.setData({
        ziweiData: ziweiData,
        formattedChart: formattedChart,
        analysis: analysis,
        customAnalysis: customAnalysis,
        startLuck: startLuck,
        daxianList: daxianList,
        currentDaxian: currentDaxian,
        currentAge: currentAge,
        formattedDaxian: this.formatDaxianInfo(currentDaxian),
        formattedLifetimeDaxian: this.formatLifetimeDaxianList(daxianList, currentAge, startLuck),
        isAnalyzing: false,
        showResult: true
      });

      // 隐藏等待提示
      wx.hideLoading();

      // 开启预分析对话模式以提高精准度
      this.startPreAnalysisConversation(ziweiData, analysis, customAnalysis);

      wx.showToast({
        title: '排盘完成，开始咨询',
        icon: 'success'
      });

    } catch (error) {
      console.error('紫微斗数计算错误:', error);
      this.setData({
        isAnalyzing: false
      });

      wx.showToast({
        title: '计算出错，请重试',
        icon: 'none'
      });
    }
  },

  // 格式化紫微专项分析
  formatZiweiAnalysis(customAnalysis) {
    if (!customAnalysis || !customAnalysis.specificAnalysis) {
      return '分析数据加载中...';
    }

    const analysis = customAnalysis.specificAnalysis;
    let result = '';

    switch (customAnalysis.questionType) {
      case '财运':
        result = `财帛宫分析：${analysis.wealthStars || '需要观察财帛宫'}
投资时机：${analysis.timing || '需要综合判断'}
预期收益：${analysis.profit || '收益不明确'}
风险评估：${analysis.risk || '风险可控'}
投资建议：${analysis.advice || '谨慎理财'}`;
        break;

      case '事业':
        result = `官禄宫分析：${analysis.careerStars || '需要观察官禄宫'}
升职前景：${analysis.promotion || '需要努力'}
跳槽建议：${analysis.jobChange || '稳定为主'}
行动时机：${analysis.timing || '顺势而为'}
事业建议：${analysis.advice || '踏实工作'}`;
        break;

      case '婚姻':
      case '桃花':
        result = `夫妻宫分析：${analysis.spouseStars || '需要观察夫妻宫'}
感情运势：${analysis.relationship || '缘分未到'}
结婚时机：${analysis.timing || '顺其自然'}
配偶特征：${analysis.partner || '合适即可'}
婚姻建议：${analysis.advice || '真诚待人'}`;
        break;

      default:
        result = `综合分析：${analysis.advice || '根据紫微命盘综合判断'}`;
    }

    return result;
  },

  // 格式化宫位星曜
  formatPalaceStars(palace) {
    if (!palace || !palace.stars || palace.stars.length === 0) {
      return '空宫';
    }
    return palace.stars.join('、');
  },

  // 获取宫位样式类
  getPalaceClass(palaceName) {
    const palace = this.data.formattedChart[palaceName];
    if (!palace) return 'palace-normal';

    if (palace.isMingGong) return 'palace-ming';
    if (palace.isShenGong) return 'palace-shen';
    if (palace.majorStars.length > 0) return 'palace-major';
    return 'palace-normal';
  },

  // 重新排盘
  onRestart() {
    this.setData({
      question: '',
      ziweiData: null,
      formattedChart: null,
      analysis: null,
      customAnalysis: null,
      isAnalyzing: false,
      showResult: false
    });
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {

  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {

  },

  // 执行紫微斗数AI分析（集成双重验证系统的高精度分析）
  async performZiweiAIAnalysis(ziweiData, traditionalAnalysis, customAnalysis, collectedInfo = null) {
    try {
      // 显示AI分析状态
      this.setData({
        isAnalyzing: true
      });

      // 显示AI分析等待提示
      wx.showLoading({
        title: '正在进行AI分析...',
        mask: true
      });

      console.log('🚀 开始紫微斗数双重验证AI分析...');

      // 检查是否有完整的出生信息用于双重验证
      const hasCompleteInfo = this.data.birthDate && this.data.birthTime && this.data.birthCity;

      let aiAnalysisResult;

      if (hasCompleteInfo) {
        console.log('✅ 出生信息完整，启用双重验证分析');

        // 构建出生信息
        const birthInfo = {
          year: new Date(this.data.birthDate).getFullYear(),
          month: new Date(this.data.birthDate).getMonth() + 1,
          day: new Date(this.data.birthDate).getDate(),
          hour: parseInt(this.data.birthTime.split(':')[0]),
          minute: parseInt(this.data.birthTime.split(':')[1]) || 0
        };

        // 构建当前信息
        const currentInfo = {
          currentAge: this.calculateAge(this.data.birthDate),
          currentYear: new Date().getFullYear(),
          isMale: this.data.isMale
        };

        // 扩展紫微斗数信息
        const enhancedZiweiData = this.enhanceZiweiData(ziweiData, birthInfo);

        // 使用双重验证AI分析
        aiAnalysisResult = await analyzeZiweiWithDualVerification(
          this.data.question,
          enhancedZiweiData,
          birthInfo,
          currentInfo
        );

        console.log('✅ 双重验证AI分析完成');

      } else {
        console.log('⚠️ 出生信息不完整，使用标准AI分析');

        // 构建完整的紫微斗数上下文信息
        const ziweiContext = {
          mingGong: ziweiData.mingGong,
          caibogong: ziweiData.caibogong,
          guanlugong: ziweiData.guanlugong,
          fuqigong: ziweiData.fuqigong,
          pattern: traditionalAnalysis.pattern || '普通格局',
          chart: ziweiData.chart,
          mainStars: ziweiData.mainStars,
          auxiliaryStars: ziweiData.auxiliaryStars,
          palaceAnalysis: traditionalAnalysis.palaceAnalysis,
          overallScore: traditionalAnalysis.overallScore,
          birthInfo: this.data
        };

        // 调用详批命书格式AI分析
        const birthInfo = {
          birthPlace: this.data.birthPlace || '未知',
          year: this.data.year,
          month: this.data.month,
          day: this.data.day,
          hour: this.data.hour,
          minute: this.data.minute
        };

        const currentInfo = {
          isMale: this.data.isMale
        };

        aiAnalysisResult = await analyzeZiweiWithAI(this.data.question, ziweiContext, birthInfo, currentInfo);
      }

      // 处理AI分析结果
      if (aiAnalysisResult) {
        // 合并传统分析和AI分析
        const combinedAnalysis = {
          ...traditionalAnalysis,
          aiAnalysis: aiAnalysisResult,
          analysisType: hasCompleteInfo ? '双重验证分析' : '标准分析',
          enhancedSummary: `【🔮 ${hasCompleteInfo ? '双重验证' : '标准'}AI深度解读】
${aiAnalysisResult}

【📊 分析说明】
• 分析方法：${hasCompleteInfo ? '紫微斗数+终身卦双重验证' : '紫微斗数传统分析'}
• 理论依据：《紫微斗数全书》《斗数宣微》${hasCompleteInfo ? '《梅花易数》' : ''}等古籍
• 知识库：437部古籍资料，182个专业术语验证
• 预测精度：${hasCompleteInfo ? '高精度时间预测（具体到年季）' : '标准预测分析'}

【💡 特别说明】
${hasCompleteInfo ?
  '本次分析采用双重验证系统，通过紫微斗数与终身卦的一致性验证，提供更精确的时间预测和命理分析。' :
  '建议提供完整的出生时间和地点信息，以获得更精确的双重验证分析结果。'}`
        };

        // 更新分析结果
        this.setData({
          analysis: combinedAnalysis,
          isAnalyzing: false
        });

        // 隐藏等待提示
        wx.hideLoading();

        wx.showToast({
          title: hasCompleteInfo ? '双重验证分析完成' : 'AI分析完成',
          icon: 'success'
        });

        console.log(`✅ 紫微斗数${hasCompleteInfo ? '双重验证' : '标准'}AI分析完成`);

      } else {
        throw new Error('AI分析返回结果为空');
      }

    } catch (error) {
      console.error('❌ 紫微斗数AI分析失败:', error);

      // AI分析失败时，保持原有分析结果
      this.setData({
        isAnalyzing: false
      });

      // 隐藏等待提示
      wx.hideLoading();

      wx.showToast({
        title: 'AI分析暂时不可用',
        icon: 'none'
      });
    }
  },

  // 增强版紫微斗数AI分析调用（集成验证系统）
  async callEnhancedZiweiAI(question, ziweiContext, collectedInfo = null) {
    try {
      // 构建专业的分析提示词（包含收集的信息）
      const prompt = this.buildZiweiAnalysisPrompt(question, ziweiContext, collectedInfo);

      // 调用DeepSeek API
      const apiResult = await this.callDeepSeekAPI(prompt);

      if (!apiResult.success) {
        throw new Error(apiResult.error);
      }

      // 进行结果验证
      const verification = this.verifyZiweiAnalysis(apiResult.reply, question, ziweiContext);

      return {
        success: true,
        analysis: apiResult.reply,
        verification: verification
      };

    } catch (error) {
      console.error('增强版紫微斗数AI分析失败:', error);
      return {
        success: false,
        error: error.message
      };
    }
  },

  // 构建紫微斗数专业分析提示词
  buildZiweiAnalysisPrompt(question, ziweiContext, collectedInfo = null) {
    const questionType = this.detectQuestionType(question);

    let collectedInfoText = '';
    if (collectedInfo && collectedInfo.length > 0) {
      collectedInfoText = `

【收集的补充信息】
${collectedInfo.map((info, index) => `${index + 1}. ${info.question}\n   回答：${info.answer}`).join('\n')}`;
    }

    return `你是专业的紫微斗数命理大师，请基于传统紫微斗数理论和知识库深度分析以下命盘。

【用户问题】
${question}

【紫微斗数信息】
• 命宫主星：${ziweiContext.mingGong || '无主星'}
• 财帛宫：${ziweiContext.caibogong || '无主星'}
• 官禄宫：${ziweiContext.guanlugong || '无主星'}
• 夫妻宫：${ziweiContext.fuqigong || '无主星'}
• 格局：${ziweiContext.pattern}
• 综合评分：${ziweiContext.overallScore || '未知'}${collectedInfoText}

【分析要求】
1. 严格按照《紫微斗数全书》《紫微斗数集成全书》等经典理论和知识库
2. 重点分析命宫、身宫、三方四正的星曜配置
3. 详细解读十四主星、辅星、煞星的组合影响
4. 针对${questionType}问题，结合相关宫位和星曜分析
5. 结合收集的补充信息，提供更精准的个性化分析
6. 提供具体的时间预测和人生建议
7. 使用专业术语：命宫、身宫、三方四正、十四主星、辅星、煞星、化禄、化权、化科、化忌等
8. 精准度是最高优先级，确保分析的准确性

请提供专业、准确、具体的高精准度分析结果，包含明确的命运走势和时间预测。`;
  },

  // 调用DeepSeek API（集成验证系统的方法）
  async callDeepSeekAPI(prompt) {
    try {
      return new Promise((resolve, reject) => {
        wx.request({
          url: 'https://api.deepseek.com/v1/chat/completions',
          method: 'POST',
          header: {
            'Content-Type': 'application/json',
            'Authorization': 'Bearer sk-********************************'
          },
          data: {
            model: 'deepseek-reasoner',
            messages: [{ role: 'user', content: prompt }],
            temperature: 0.2,
            max_tokens: 65536
          },
          timeout: 600000, // 10分钟超时
          success: (response) => {
            console.log('DeepSeek API响应:', response.statusCode);
            if (response.statusCode === 200 && response.data && response.data.choices) {
              resolve({
                success: true,
                reply: response.data.choices[0].message.content
              });
            } else {
              console.error('API响应格式异常:', response);
              resolve({
                success: false,
                error: `API响应异常: ${response.statusCode}`
              });
            }
          },
          fail: (error) => {
            console.error('API请求失败:', error);
            resolve({
              success: false,
              error: `API请求失败: ${error.errMsg || '网络错误'}`
            });
          }
        });
      });

    } catch (error) {
      console.error('API调用异常:', error);
      return { success: false, error: error.message };
    }
  },

  // 验证紫微斗数分析结果质量
  verifyZiweiAnalysis(analysis, question, ziweiContext) {
    // 专业术语检查
    const ziweiTerms = ['命宫', '身宫', '三方四正', '十四主星', '辅星', '煞星', '化禄', '化权', '化科', '化忌', '紫微', '天机', '太阳', '武曲', '天同', '廉贞'];
    const terminologyScore = this.calculateTerminologyScore(analysis, ziweiTerms);

    // 知识库符合度评估
    const knowledgeScore = this.assessKnowledgeAccuracy(analysis, 'ziwei');

    // 预测具体性评估
    const specificityScore = this.assessPredictionSpecificity(analysis);

    return {
      terminology_accuracy: terminologyScore,
      knowledge_accuracy: knowledgeScore,
      prediction_specificity: specificityScore,
      overall_score: ((terminologyScore + knowledgeScore + specificityScore) / 3).toFixed(1)
    };
  },

  // 计算术语使用评分
  calculateTerminologyScore(analysis, terms) {
    const usedTerms = terms.filter(term => analysis.includes(term));
    const score = Math.min(10, (usedTerms.length / terms.length) * 10 + 5);
    return score.toFixed(1);
  },

  // 评估知识库符合度
  assessKnowledgeAccuracy(analysis, type) {
    // 基于分析内容的专业性和逻辑性评估
    const professionalIndicators = ['根据', '按照', '理论', '经典', '古籍', '传统', '紫微', '斗数', '全书'];
    const foundIndicators = professionalIndicators.filter(indicator => analysis.includes(indicator));
    const score = Math.min(10, (foundIndicators.length / professionalIndicators.length) * 5 + 6);
    return score.toFixed(1);
  },

  // 评估预测具体性
  assessPredictionSpecificity(analysis) {
    // 检查是否包含具体的时间、数字、明确建议
    const specificityIndicators = ['月', '年', '日', '时间', '建议', '应该', '可以', '不宜', '大限', '流年'];
    const foundIndicators = specificityIndicators.filter(indicator => analysis.includes(indicator));
    const score = Math.min(10, (foundIndicators.length / specificityIndicators.length) * 4 + 6);
    return score.toFixed(1);
  },

  // 检测问题类型
  detectQuestionType(question) {
    const keywords = {
      '财运': ['财运', '赚钱', '投资', '理财', '收入', '财富', '经济'],
      '事业': ['工作', '事业', '升职', '跳槽', '职业', '升迁', '当官'],
      '婚姻': ['婚姻', '结婚', '感情', '恋爱', '配偶', '对象', '桃花'],
      '健康': ['健康', '疾病', '身体', '病情', '康复', '医疗'],
      '学业': ['学习', '考试', '学业', '读书', '升学', '文凭']
    };

    for (const [type, words] of Object.entries(keywords)) {
      if (words.some(word => question.includes(word))) {
        return type;
      }
    }

    return '综合';
  },

  // ========== 预分析对话功能 ==========

  /**
   * 开启预分析对话模式（在正式分析前收集信息以提高精准度）
   */
  startPreAnalysisConversation(ziweiData, traditionalAnalysis, customAnalysis) {
    console.log('🗣️ 开启紫微斗数预分析对话模式');

    // 创建对话会话
    const userId = wx.getStorageSync('userId') || 'anonymous_' + Date.now();
    const sessionId = conversationManager.createSession(userId, 'ziwei');

    // 存储紫微数据供后续分析使用
    conversationManager.updateContext(sessionId, {
      ziweiData: ziweiData,
      traditionalAnalysis: traditionalAnalysis,
      customAnalysis: customAnalysis,
      question: this.data.question,
      birthInfo: {
        date: this.data.birthDate,
        time: this.data.birthTime,
        gender: this.data.isMale ? '男' : '女',
        city: this.data.selectedCity
      }
    });

    // 生成预分析问题
    this.generatePreAnalysisQuestions(sessionId, ziweiData, traditionalAnalysis);

    this.setData({
      conversationMode: true,
      sessionId: sessionId,
      showConversationPanel: true,
      isAnalyzing: false
    });
  },

  /**
   * 生成预分析问题
   */
  async generatePreAnalysisQuestions(sessionId, ziweiData, traditionalAnalysis) {
    try {
      // 构建紫微分析上下文
      const ziweiAnalysis = {
        mainStar: ziweiData.mingGong?.mainStar || '未知',
        wealthPalace: ziweiData.caibogong?.stars || [],
        careerPalace: ziweiData.guanluGong?.stars || [],
        marriagePalace: ziweiData.fuqiGong?.stars || [],
        healthPalace: ziweiData.jieEGong?.stars || []
      };

      // 调用智能询问系统生成问题
      const questions = await intelligentInquiry.generatePreAnalysisQuestions(
        this.data.question,
        ziweiAnalysis,
        'ziwei',
        sessionId
      );

      if (questions && questions.length > 0) {
        // 开始第一个问题
        this.askNextQuestion(sessionId, questions);
      } else {
        // 如果没有问题，直接进行分析
        this.performFinalAnalysisWithCollectedInfo(sessionId);
      }

    } catch (error) {
      console.error('生成预分析问题失败:', error);
      // 出错时直接进行分析
      this.performFinalAnalysisWithCollectedInfo(sessionId);
    }
  },

  /**
   * 询问下一个问题
   */
  askNextQuestion(sessionId, questions) {
    const nextQuestion = questions.shift();
    if (!nextQuestion) {
      // 所有问题都问完了，进行最终分析
      this.performFinalAnalysisWithCollectedInfo(sessionId);
      return;
    }

    // 显示AI正在"打字"
    this.setData({
      isTyping: true
    });

    // 模拟打字延迟
    setTimeout(() => {
      const aiMessage = {
        type: 'ai',
        content: nextQuestion.text,
        timestamp: new Date().getTime(),
        knowledge: nextQuestion.knowledge
      };

      // 添加到对话历史
      const conversationHistory = this.data.conversationHistory;
      conversationHistory.push(aiMessage);

      this.setData({
        conversationHistory: conversationHistory,
        currentFollowUp: nextQuestion,
        isWaitingResponse: true,
        isTyping: false
      });

      // 存储剩余问题
      conversationManager.updateContext(sessionId, {
        remainingQuestions: questions
      });

    }, 1000 + Math.random() * 1000); // 1-2秒的随机延迟
  },

  /**
   * 处理用户回答
   */
  handleUserResponse() {
    const userInput = this.data.conversationInput.trim();
    if (!userInput) {
      wx.showToast({
        title: '请输入您的回答',
        icon: 'none'
      });
      return;
    }

    // 添加用户消息到对话历史
    const userMessage = {
      type: 'user',
      content: userInput,
      timestamp: new Date().getTime()
    };

    const conversationHistory = this.data.conversationHistory;
    conversationHistory.push(userMessage);

    // 记录用户回答
    conversationManager.addMessage(this.data.sessionId, userMessage);

    this.setData({
      conversationHistory: conversationHistory,
      conversationInput: '',
      isWaitingResponse: false
    });

    // 获取剩余问题并继续
    const sessionContext = conversationManager.getSessionContext(this.data.sessionId);
    const remainingQuestions = sessionContext.remainingQuestions || [];

    if (remainingQuestions.length > 0) {
      // 继续下一个问题
      this.askNextQuestion(this.data.sessionId, remainingQuestions);
    } else {
      // 所有问题都问完了，进行最终分析
      this.performFinalAnalysisWithCollectedInfo(this.data.sessionId);
    }
  },

  /**
   * 使用收集的信息进行最终分析
   */
  async performFinalAnalysisWithCollectedInfo(sessionId) {
    console.log('🎯 开始紫微斗数最终AI分析（基于收集的信息）');

    // 显示分析状态
    this.setData({
      isAnalyzing: true,
      conversationMode: false,
      showConversationPanel: false
    });

    try {
      // 获取会话上下文和收集的信息
      const sessionContext = conversationManager.getSessionContext(sessionId);
      const collectedInfo = conversationManager.getCollectedInfo(sessionId);

      // 进行增强的AI分析
      await this.performZiweiAIAnalysis(
        sessionContext.ziweiData,
        sessionContext.traditionalAnalysis,
        sessionContext.customAnalysis,
        collectedInfo
      );

    } catch (error) {
      console.error('最终分析失败:', error);
      this.setData({
        isAnalyzing: false
      });
    }

    // 结束对话会话
    conversationManager.endSession(sessionId);
  },

  /**
   * 关闭对话模式
   */
  closeConversationMode() {
    this.setData({
      conversationMode: false,
      showConversationPanel: false,
      conversationHistory: [],
      sessionId: null
    });
  },

  /**
   * 输入框内容变化
   */
  onConversationInput(e) {
    this.setData({
      conversationInput: e.detail.value
    });
  },

  // 增强紫微斗数数据（添加扩展信息）
  enhanceZiweiData(ziweiData, birthInfo) {
    try {
      // 计算命主星
      const mingZhu = calculateMingZhu(birthInfo.year);

      // 计算身主星
      const shenZhu = calculateShenZhu(birthInfo.month, birthInfo.hour);

      // 计算五行局（需要命宫干支）
      const mingGongGanZhi = this.getMingGongGanZhi(ziweiData);
      const wuxingJu = calculateWuXingJu(mingGongGanZhi);

      // 分析容貌特征
      const mingGongStars = ziweiData.chart && ziweiData.chart['命宫'] ?
        ziweiData.chart['命宫'].majorStars || [] : [];
      const appearance = analyzeAppearance(mingGongStars);

      // 分析性格特征
      const personality = analyzePersonality(mingGongStars);

      // 扩展数据
      const enhancedData = {
        ...ziweiData,
        mingZhu: mingZhu,
        shenZhu: shenZhu,
        wuxingJu: wuxingJu,
        appearance: appearance,
        personality: personality,
        birthInfo: birthInfo,
        enhancedInfo: {
          命主: mingZhu,
          身主: shenZhu,
          五行局: wuxingJu.ju,
          主五行: wuxingJu.wuxing,
          容貌特征: appearance,
          性格特征: personality
        }
      };

      console.log('✅ 紫微斗数数据增强完成:', enhancedData.enhancedInfo);
      return enhancedData;

    } catch (error) {
      console.error('❌ 紫微斗数数据增强失败:', error);
      return ziweiData; // 返回原始数据
    }
  },

  // 获取命宫干支（简化实现）
  getMingGongGanZhi(ziweiData) {
    // 这里应该根据实际的紫微斗数计算逻辑来获取命宫干支
    // 暂时返回一个默认值，实际应用中需要完善
    if (ziweiData.chart && ziweiData.chart['命宫'] && ziweiData.chart['命宫'].ganZhi) {
      return ziweiData.chart['命宫'].ganZhi;
    }
    return '甲子'; // 默认值
  },

  // 计算年龄
  calculateAge(birthDate) {
    if (!birthDate) return 25; // 默认年龄

    const birth = new Date(birthDate);
    const today = new Date();
    let age = today.getFullYear() - birth.getFullYear();
    const monthDiff = today.getMonth() - birth.getMonth();

    if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birth.getDate())) {
      age--;
    }

    return age;
  },

  // 格式化大限信息（单个大限）
  formatDaxianInfo(daxian) {
    console.log('🔮 大限数据输入:', daxian);

    if (!daxian) {
      console.log('🚨 大限数据为空');
      return '暂无大限信息';
    }

    if (!daxian.palace) {
      console.log('🚨 大限宫位为空');
      return '大限计算中...';
    }

    const result = `${daxian.palace}大限（${daxian.startAge}-${daxian.endAge}岁）`;
    console.log('🔮 格式化后的大限信息:', result);
    return result;
  },

  // 格式化完整的一生大限列表
  formatLifetimeDaxianList(daxianList, currentAge, startLuck) {
    console.log('🔮 格式化一生大限，输入列表:', daxianList);
    console.log('🔮 当前年龄:', currentAge);
    console.log('🔮 起运信息:', startLuck);

    if (!daxianList || daxianList.length === 0) {
      return '大限计算中...';
    }

    // 添加起运信息说明
    let result = `【大限起运信息】\n`;
    result += `起运年龄：${startLuck.startAge}岁\n`;
    result += `五行局：${startLuck.wuxingJu}\n`;
    result += `大限方向：${startLuck.direction}\n\n`;
    result += `【大限列表】\n`;

    daxianList.forEach((daxian, index) => {
      const isCurrent = currentAge >= daxian.startAge && currentAge <= daxian.endAge;
      const status = isCurrent ? '【当前】' : '';

      result += `${status}${daxian.startAge}-${daxian.endAge}岁 ${daxian.palace}大限`;
      if (index < daxianList.length - 1) {
        result += '\n';
      }
    });

    console.log('🔮 格式化后的一生大限:', result);
    return result;
  }
})