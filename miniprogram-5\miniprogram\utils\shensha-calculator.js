// 神煞计算工具
// 用于计算八字中的各种神煞

// 天乙贵人表（以日干为主）
const TIANYI_GUIREN = {
  '甲': ['丑', '未'], '乙': ['子', '申'],
  '丙': ['亥', '酉'], '丁': ['亥', '酉'],
  '戊': ['丑', '未'], '己': ['子', '申'],
  '庚': ['丑', '未'], '辛': ['寅', '午'],
  '壬': ['卯', '巳'], '癸': ['卯', '巳']
};

// 文昌贵人表
const WENCHANG_GUIREN = {
  '甲': '巳', '乙': '午', '丙': '申', '丁': '酉',
  '戊': '申', '己': '酉', '庚': '亥', '辛': '子',
  '壬': '寅', '癸': '卯'
};

// 桃花星表（以年支或日支查）
const TAOHUA_STAR = {
  '子': '酉', '丑': '午', '寅': '卯', '卯': '子',
  '辰': '酉', '巳': '午', '午': '卯', '未': '子',
  '申': '酉', '酉': '午', '戌': '卯', '亥': '子'
};

// 驿马星表
const YIMA_STAR = {
  '申': '寅', '子': '寅', '辰': '寅',
  '寅': '申', '午': '申', '戌': '申',
  '巳': '亥', '酉': '亥', '丑': '亥',
  '亥': '巳', '卯': '巳', '未': '巳'
};

// 华盖星表
const HUAGAI_STAR = {
  '寅': '戌', '午': '戌', '戌': '戌',
  '申': '辰', '子': '辰', '辰': '辰',
  '巳': '丑', '酉': '丑', '丑': '丑',
  '亥': '未', '卯': '未', '未': '未'
};

// 羊刃表（以日干查）
const YANGREN = {
  '甲': '卯', '乙': '寅', '丙': '午', '丁': '巳',
  '戊': '午', '己': '巳', '庚': '酉', '辛': '申',
  '壬': '子', '癸': '亥'
};

// 空亡表（以日柱查）
const KONGWANG_TABLE = {
  '甲子': ['戌', '亥'], '甲戌': ['申', '酉'], '甲申': ['午', '未'],
  '甲午': ['辰', '巳'], '甲辰': ['寅', '卯'], '甲寅': ['子', '丑'],
  '乙丑': ['戌', '亥'], '乙亥': ['申', '酉'], '乙酉': ['午', '未'],
  '乙未': ['辰', '巳'], '乙巳': ['寅', '卯'], '乙卯': ['子', '丑'],
  '丙寅': ['戌', '亥'], '丙子': ['申', '酉'], '丙戌': ['午', '未'],
  '丙申': ['辰', '巳'], '丙午': ['寅', '卯'], '丙辰': ['子', '丑'],
  '丁卯': ['戌', '亥'], '丁丑': ['申', '酉'], '丁亥': ['午', '未'],
  '丁酉': ['辰', '巳'], '丁未': ['寅', '卯'], '丁巳': ['子', '丑'],
  '戊辰': ['戌', '亥'], '戊寅': ['申', '酉'], '戊子': ['午', '未'],
  '戊戌': ['辰', '巳'], '戊申': ['寅', '卯'], '戊午': ['子', '丑'],
  '己巳': ['戌', '亥'], '己卯': ['申', '酉'], '己丑': ['午', '未'],
  '己亥': ['辰', '巳'], '己酉': ['寅', '卯'], '己未': ['子', '丑'],
  '庚午': ['戌', '亥'], '庚辰': ['申', '酉'], '庚寅': ['午', '未'],
  '庚子': ['辰', '巳'], '庚戌': ['寅', '卯'], '庚申': ['子', '丑'],
  '辛未': ['戌', '亥'], '辛巳': ['申', '酉'], '辛卯': ['午', '未'],
  '辛丑': ['辰', '巳'], '辛亥': ['寅', '卯'], '辛酉': ['子', '丑'],
  '壬申': ['戌', '亥'], '壬午': ['申', '酉'], '壬辰': ['午', '未'],
  '壬寅': ['辰', '巳'], '壬子': ['寅', '卯'], '壬戌': ['子', '丑'],
  '癸酉': ['戌', '亥'], '癸未': ['申', '酉'], '癸巳': ['午', '未'],
  '癸卯': ['辰', '巳'], '癸丑': ['寅', '卯'], '癸亥': ['子', '丑']
};

/**
 * 计算八字神煞
 * @param {Object} bazi - 八字对象
 * @returns {Object} 神煞信息
 */
function calculateShensha(bazi) {
  const shensha = {
    tianyi: [],      // 天乙贵人
    wenchang: [],    // 文昌贵人
    taohua: [],      // 桃花星
    yima: [],        // 驿马星
    huagai: [],      // 华盖星
    yangren: [],     // 羊刃
    kongwang: [],    // 空亡
    other: []        // 其他神煞
  };

  const dayGan = bazi.day.stem;
  const dayZhi = bazi.day.branch;
  const yearZhi = bazi.year.branch;
  const dayGanzhi = dayGan + dayZhi;
  
  const allZhi = [bazi.year.branch, bazi.month.branch, bazi.day.branch, bazi.hour.branch];

  // 计算天乙贵人
  const tianyiList = TIANYI_GUIREN[dayGan];
  if (tianyiList) {
    tianyiList.forEach(zhi => {
      if (allZhi.includes(zhi)) {
        shensha.tianyi.push({
          name: '天乙贵人',
          position: zhi,
          description: '逢凶化吉，遇难呈祥'
        });
      }
    });
  }

  // 计算文昌贵人
  const wenchangZhi = WENCHANG_GUIREN[dayGan];
  if (wenchangZhi && allZhi.includes(wenchangZhi)) {
    shensha.wenchang.push({
      name: '文昌贵人',
      position: wenchangZhi,
      description: '主聪明好学，文思敏捷'
    });
  }

  // 计算桃花星
  const taohuaZhi = TAOHUA_STAR[yearZhi] || TAOHUA_STAR[dayZhi];
  if (taohuaZhi && allZhi.includes(taohuaZhi)) {
    shensha.taohua.push({
      name: '桃花星',
      position: taohuaZhi,
      description: '主异性缘佳，感情丰富'
    });
  }

  // 计算驿马星
  const yimaZhi = YIMA_STAR[yearZhi] || YIMA_STAR[dayZhi];
  if (yimaZhi && allZhi.includes(yimaZhi)) {
    shensha.yima.push({
      name: '驿马星',
      position: yimaZhi,
      description: '主奔波劳碌，变动较多'
    });
  }

  // 计算华盖星
  const huagaiZhi = HUAGAI_STAR[yearZhi] || HUAGAI_STAR[dayZhi];
  if (huagaiZhi && allZhi.includes(huagaiZhi)) {
    shensha.huagai.push({
      name: '华盖星',
      position: huagaiZhi,
      description: '主艺术天赋，孤高清雅'
    });
  }

  // 计算羊刃
  const yangrenZhi = YANGREN[dayGan];
  if (yangrenZhi && allZhi.includes(yangrenZhi)) {
    shensha.yangren.push({
      name: '羊刃',
      position: yangrenZhi,
      description: '主性格刚烈，易有血光'
    });
  }

  // 计算空亡
  const kongwangList = KONGWANG_TABLE[dayGanzhi];
  if (kongwangList) {
    kongwangList.forEach(zhi => {
      if (allZhi.includes(zhi)) {
        shensha.kongwang.push({
          name: '空亡',
          position: zhi,
          description: '主虚空不实，多有变化'
        });
      }
    });
  }

  return shensha;
}

/**
 * 格式化神煞显示
 * @param {Object} shensha - 神煞对象
 * @returns {Array} 格式化的神煞列表
 */
function formatShensha(shensha) {
  const result = [];
  
  Object.keys(shensha).forEach(category => {
    shensha[category].forEach(item => {
      result.push({
        name: item.name,
        position: item.position,
        description: item.description,
        category: category
      });
    });
  });

  return result.length > 0 ? result : [{ name: '无特殊神煞', description: '八字平和' }];
}

/**
 * 分析神煞吉凶
 * @param {Object} shensha - 神煞对象
 * @returns {Object} 神煞分析结果
 */
function analyzeShensha(shensha) {
  const analysis = {
    lucky: [],      // 吉神
    unlucky: [],    // 凶神
    neutral: [],    // 中性
    summary: ''     // 总结
  };

  // 分类神煞
  const luckyTypes = ['tianyi', 'wenchang'];
  const unluckyTypes = ['yangren', 'kongwang'];
  const neutralTypes = ['taohua', 'yima', 'huagai'];

  Object.keys(shensha).forEach(category => {
    shensha[category].forEach(item => {
      if (luckyTypes.includes(category)) {
        analysis.lucky.push(item);
      } else if (unluckyTypes.includes(category)) {
        analysis.unlucky.push(item);
      } else if (neutralTypes.includes(category)) {
        analysis.neutral.push(item);
      }
    });
  });

  // 生成总结
  const luckyCount = analysis.lucky.length;
  const unluckyCount = analysis.unlucky.length;
  
  if (luckyCount > unluckyCount) {
    analysis.summary = '吉神较多，整体运势较好';
  } else if (unluckyCount > luckyCount) {
    analysis.summary = '需要注意化解凶神影响';
  } else {
    analysis.summary = '神煞平衡，需要综合分析';
  }

  return analysis;
}

module.exports = {
  calculateShensha,
  formatShensha,
  analyzeShensha,
  TIANYI_GUIREN,
  WENCHANG_GUIREN,
  TAOHUA_STAR,
  YIMA_STAR,
  HUAGAI_STAR,
  YANGREN,
  KONGWANG_TABLE
};
