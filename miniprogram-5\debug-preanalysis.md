# 预分析功能调试指南

## 🎯 已修复的关键问题

### 1. TypeError: Cannot read property 'includes' of undefined
**问题位置：** `intelligent-inquiry.js:503`
**根本原因：** `getHexagramSpecificQuestions`函数中尝试访问undefined的`hexagramName.includes()`
**修复方案：**
- 添加了安全的属性访问：`hexagramAnalysis?.liuyaoInfo?.hexagramName`
- 添加了空值检查和早期返回
- 添加了详细的调试日志

### 2. 参数顺序错误
**问题位置：** `yijing.js:1642-1646`
**根本原因：** `generatePreAnalysisQuestions`调用时参数顺序错误
**修复方案：**
- 修正参数顺序：`(question, analysisData, moduleType, sessionId)`
- 明确指定moduleType为'yijing'

### 3. 卦象匹配不完整
**问题：** 只有少数几个卦象有对应问题
**修复方案：**
- 添加了更多卦象匹配（乾、坤、屯、蒙、需等）
- 添加了通用后备问题，确保总是有问题返回

## 🔍 调试日志说明

现在预分析系统会输出详细的调试信息：

```
🤖 为会话 xxx 生成预分析询问问题...
📝 问题: 我的财运如何？
📊 分析数据: {hexagram对象}
🔍 开始分析用户问题: "我的财运如何？"
✅ 识别到问题类型: finance, 匹配关键词: 财运
🔧 生成知识库问题 - 问题类型: finance, 模块类型: yijing
📚 模块问题库: ["yijing", "meihua", "bazi", "ziwei"]
🎯 找到的模块问题: ["career", "finance", "relationship"]
❓ 返回的问题数量: 3
🎲 生成命盘特定问题 - 模块: yijing, 问题类型: finance
🔮 生成卦象特定问题 - 分析数据: {hexagram对象}
📖 提取的卦象名称: 乾为天
✅ 生成的卦象问题: [{问题对象}]
```

## 🧪 测试步骤

### 测试1：基础财运问题
1. 输入问题："我的财运如何？"
2. 完成起卦（任意方式）
3. 检查控制台日志
4. 验证：应该出现预分析对话

### 测试2：事业问题
1. 输入问题："我什么时候能升职？"
2. 完成起卦
3. 验证：应该识别为career类型并生成相应问题

### 测试3：通用问题
1. 输入问题："测试"
2. 完成起卦
3. 验证：应该生成通用后备问题

## 📊 预期结果

修复后，预分析功能应该：
1. ✅ 不再出现"Cannot read property 'includes' of undefined"错误
2. ✅ 正确识别问题类型（财运、事业、感情等）
3. ✅ 根据卦象名称生成特定问题
4. ✅ 总是返回至少一个问题（通过后备机制）
5. ✅ 显示详细的调试日志便于排查

## 🔧 如果仍然失败

1. **检查控制台日志**：查看具体在哪一步失败
2. **验证数据结构**：确认hexagram对象包含liuyaoInfo
3. **检查网络**：确认不是网络问题导致的
4. **重新编译**：清除缓存并重新编译

## 📝 已知限制

1. **AI增强分析**：仍然会因为fetch错误而失败，但不影响预分析功能
2. **云函数依赖**：基础预分析不依赖云函数，应该能正常工作
3. **问题生成**：目前支持主要卦象，其他卦象会使用通用问题

## 🎯 下一步优化

1. 添加更多卦象的特定问题
2. 根据动爻情况生成更精准的问题
3. 结合六亲关系生成问题
4. 优化问题的优先级排序
