<svg width="48" height="48" viewBox="0 0 48 48" xmlns="http://www.w3.org/2000/svg">
  <!-- 太极背景圆 -->
  <circle cx="24" cy="24" r="22" fill="#d4af37" stroke="#1a1a1a" stroke-width="2"/>
  
  <!-- 太极阴阳分界 -->
  <path d="M 24 2 A 22 22 0 0 1 24 46 A 11 11 0 0 1 24 24 A 11 11 0 0 0 24 2 Z" fill="#1a1a1a"/>
  
  <!-- 阴中阳点 -->
  <circle cx="24" cy="13" r="4" fill="#d4af37"/>
  
  <!-- 阳中阴点 -->
  <circle cx="24" cy="35" r="4" fill="#1a1a1a"/>
  
  <!-- 人物轮廓 -->
  <g stroke="#1a1a1a" stroke-width="2" fill="none">
    <!-- 头部 -->
    <circle cx="24" cy="18" r="4"/>
    <!-- 身体 -->
    <path d="M 24 22 L 24 32"/>
    <!-- 手臂 -->
    <path d="M 20 26 L 28 26"/>
    <!-- 腿部 -->
    <path d="M 24 32 L 20 38"/>
    <path d="M 24 32 L 28 38"/>
  </g>
</svg>
