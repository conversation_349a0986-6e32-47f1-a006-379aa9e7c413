// 天干地支时间计算工具
// 用于六爻占卜的精确时间计算

// 天干
const HEAVENLY_STEMS = ['甲', '乙', '丙', '丁', '戊', '己', '庚', '辛', '壬', '癸'];

// 地支
const EARTHLY_BRANCHES = ['子', '丑', '寅', '卯', '辰', '巳', '午', '未', '申', '酉', '戌', '亥'];

// 地支对应时辰
const HOUR_BRANCHES = {
  23: '子', 1: '丑', 3: '寅', 5: '卯', 7: '辰', 9: '巳',
  11: '午', 13: '未', 15: '申', 17: '酉', 19: '戌', 21: '亥'
};

// 月份对应地支（农历）
const MONTH_BRANCHES = ['', '寅', '卯', '辰', '巳', '午', '未', '申', '酉', '戌', '亥', '子', '丑'];

// 节气数据（2025年精确数据）
const SOLAR_TERMS_2025 = {
  1: { name: '小寒', day: 5, hour: 10, minute: 7 },
  2: { name: '立春', day: 3, hour: 22, minute: 10 },
  3: { name: '惊蛰', day: 5, hour: 10, minute: 22 },
  4: { name: '清明', day: 4, hour: 21, minute: 2 },
  5: { name: '立夏', day: 5, hour: 8, minute: 10 },
  6: { name: '芒种', day: 5, hour: 18, minute: 10 },
  7: { name: '小暑', day: 7, hour: 4, minute: 5 },
  8: { name: '立秋', day: 7, hour: 14, minute: 31 },
  9: { name: '白露', day: 7, hour: 11, minute: 11 },
  10: { name: '寒露', day: 8, hour: 9, minute: 15 },
  11: { name: '立冬', day: 7, hour: 12, minute: 35 },
  12: { name: '大雪', day: 7, hour: 5, minute: 56 }
};

// 农历月份名称
const LUNAR_MONTHS = ['', '正月', '二月', '三月', '四月', '五月', '六月', '七月', '八月', '九月', '十月', '冬月', '腊月'];

/**
 * 计算完整的天干地支时间信息
 * @param {Date} date - 日期时间对象
 * @returns {Object} 完整的时间信息
 */
function calculateGanzhiTime(date = new Date()) {
  const year = date.getFullYear();
  const month = date.getMonth() + 1;
  const day = date.getDate();
  const hour = date.getHours();
  const minute = date.getMinutes();
  
  // 计算年干支
  const yearGanzhi = calculateYearGanzhi(year);
  
  // 计算月干支
  const monthGanzhi = calculateMonthGanzhi(year, month, day);
  
  // 计算日干支
  const dayGanzhi = calculateDayGanzhi(year, month, day);
  
  // 计算时干支
  const hourGanzhi = calculateHourGanzhi(dayGanzhi.stem, hour);
  
  // 计算农历信息（简化版）
  const lunarInfo = calculateLunarInfo(year, month, day);
  
  // 获取当前节气
  const currentSolarTerm = getCurrentSolarTerm(month, day);
  
  return {
    // 公历信息
    gregorian: {
      year: year,
      month: month,
      day: day,
      hour: hour,
      minute: minute,
      formatted: `${year}年${month}月${day}日${hour}时${minute}分`
    },
    
    // 天干地支信息
    ganzhi: {
      year: yearGanzhi,
      month: monthGanzhi,
      day: dayGanzhi,
      hour: hourGanzhi,
      formatted: `${yearGanzhi.stem}${yearGanzhi.branch}年${monthGanzhi.stem}${monthGanzhi.branch}月${dayGanzhi.stem}${dayGanzhi.branch}日${hourGanzhi.stem}${hourGanzhi.branch}时`
    },
    
    // 农历信息（简化）
    lunar: lunarInfo,
    
    // 节气信息
    solarTerm: currentSolarTerm,
    
    // 完整格式化字符串
    fullFormatted: `公历${year}年${month}月${day}日${hour}时${minute}分（农历${lunarInfo.formatted}，${yearGanzhi.stem}${yearGanzhi.branch}年${monthGanzhi.stem}${monthGanzhi.branch}月${dayGanzhi.stem}${dayGanzhi.branch}日${hourGanzhi.stem}${hourGanzhi.branch}时，${currentSolarTerm}节气）`
  };
}

/**
 * 计算年干支
 */
function calculateYearGanzhi(year) {
  // 以1984年甲子年为基准
  const baseYear = 1984;
  const yearDiff = year - baseYear;
  
  const stemIndex = yearDiff % 10;
  const branchIndex = yearDiff % 12;
  
  return {
    stem: HEAVENLY_STEMS[stemIndex < 0 ? stemIndex + 10 : stemIndex],
    branch: EARTHLY_BRANCHES[branchIndex < 0 ? branchIndex + 12 : branchIndex]
  };
}

/**
 * 计算月干支
 */
function calculateMonthGanzhi(year, month, day) {
  // 根据节气确定真正的月份
  const actualMonth = getActualMonth(month, day);

  // 月支固定：正月寅，二月卯...
  const branchIndex = (actualMonth + 1) % 12;
  const branch = EARTHLY_BRANCHES[branchIndex];

  // 月干根据年干确定：甲己之年丙作首
  const yearGanzhi = calculateYearGanzhi(year);
  const yearStemIndex = HEAVENLY_STEMS.indexOf(yearGanzhi.stem);

  let monthStemIndex;
  switch (yearStemIndex) {
    case 0: case 5: // 甲、己年
      monthStemIndex = (2 + actualMonth - 1) % 10; // 丙作首
      break;
    case 1: case 6: // 乙、庚年
      monthStemIndex = (4 + actualMonth - 1) % 10; // 戊为头
      break;
    case 2: case 7: // 丙、辛年
      monthStemIndex = (6 + actualMonth - 1) % 10; // 从庚起
      break;
    case 3: case 8: // 丁、壬年
      monthStemIndex = (8 + actualMonth - 1) % 10; // 壬寅
      break;
    case 4: case 9: // 戊、癸年
      monthStemIndex = (0 + actualMonth - 1) % 10; // 甲寅
      break;
    default:
      monthStemIndex = 0;
  }

  return {
    stem: HEAVENLY_STEMS[monthStemIndex],
    branch: branch
  };
}

/**
 * 计算日干支（简化算法）
 */
function calculateDayGanzhi(year, month, day) {
  // 以2000年1月1日庚辰日为基准
  const baseDate = new Date(2000, 0, 1);
  const currentDate = new Date(year, month - 1, day);
  const daysDiff = Math.floor((currentDate - baseDate) / (1000 * 60 * 60 * 24));
  
  // 2000年1月1日是庚辰日，庚=6，辰=4
  const baseStemIndex = 6;
  const baseBranchIndex = 4;
  
  const stemIndex = (baseStemIndex + daysDiff) % 10;
  const branchIndex = (baseBranchIndex + daysDiff) % 12;
  
  return {
    stem: HEAVENLY_STEMS[stemIndex < 0 ? stemIndex + 10 : stemIndex],
    branch: EARTHLY_BRANCHES[branchIndex < 0 ? branchIndex + 12 : branchIndex]
  };
}

/**
 * 计算时干支
 */
function calculateHourGanzhi(dayStem, hour) {
  // 确定时辰地支
  const hourBranch = getHourBranch(hour);
  const branchIndex = EARTHLY_BRANCHES.indexOf(hourBranch);
  
  // 时干计算：甲己还加甲，乙庚丙作初...
  const dayStemIndex = HEAVENLY_STEMS.indexOf(dayStem);
  let hourStemIndex;
  
  switch (dayStemIndex % 5) {
    case 0: case 5: // 甲、己日
      hourStemIndex = (0 + branchIndex) % 10; // 甲子时开始
      break;
    case 1: case 6: // 乙、庚日
      hourStemIndex = (2 + branchIndex) % 10; // 丙子时开始
      break;
    case 2: case 7: // 丙、辛日
      hourStemIndex = (4 + branchIndex) % 10; // 戊子时开始
      break;
    case 3: case 8: // 丁、壬日
      hourStemIndex = (6 + branchIndex) % 10; // 庚子时开始
      break;
    case 4: case 9: // 戊、癸日
      hourStemIndex = (8 + branchIndex) % 10; // 壬子时开始
      break;
  }
  
  return {
    stem: HEAVENLY_STEMS[hourStemIndex],
    branch: hourBranch
  };
}

/**
 * 获取时辰地支
 */
function getHourBranch(hour) {
  if (hour >= 23 || hour < 1) return '子';
  if (hour >= 1 && hour < 3) return '丑';
  if (hour >= 3 && hour < 5) return '寅';
  if (hour >= 5 && hour < 7) return '卯';
  if (hour >= 7 && hour < 9) return '辰';
  if (hour >= 9 && hour < 11) return '巳';
  if (hour >= 11 && hour < 13) return '午';
  if (hour >= 13 && hour < 15) return '未';
  if (hour >= 15 && hour < 17) return '申';
  if (hour >= 17 && hour < 19) return '酉';
  if (hour >= 19 && hour < 21) return '戌';
  if (hour >= 21 && hour < 23) return '亥';
  return '子';
}

/**
 * 根据节气确定实际月份
 */
function getActualMonth(month, day) {
  // 简化处理：如果还没过节气，则算上个月
  const solarTerm = SOLAR_TERMS_2025[month];
  if (solarTerm && day < solarTerm.day) {
    return month === 1 ? 12 : month - 1;
  }
  return month;
}

/**
 * 计算农历信息（简化版）
 */
function calculateLunarInfo(year, month, day) {
  // 这里应该使用精确的农历转换算法
  // 为了演示，使用简化计算
  const lunarMonth = month; // 简化处理
  const lunarDay = day; // 简化处理
  
  return {
    year: year,
    month: lunarMonth,
    day: lunarDay,
    monthName: LUNAR_MONTHS[lunarMonth] || '未知月',
    formatted: `${LUNAR_MONTHS[lunarMonth] || '未知月'}${lunarDay}日`
  };
}

/**
 * 获取当前节气
 */
function getCurrentSolarTerm(month, day) {
  const solarTerm = SOLAR_TERMS_2025[month];
  if (solarTerm) {
    return day >= solarTerm.day ? solarTerm.name : SOLAR_TERMS_2025[month === 1 ? 12 : month - 1]?.name || '未知';
  }
  return '未知';
}

module.exports = {
  calculateGanzhiTime,
  HEAVENLY_STEMS,
  EARTHLY_BRANCHES,
  LUNAR_MONTHS,
  SOLAR_TERMS_2025
};
