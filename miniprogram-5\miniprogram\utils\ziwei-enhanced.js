// 紫微斗数增强排盘算法
// 基于古籍精确公式优化

/**
 * 精确的紫微星定位算法
 * 基于古籍公式：(生日 + X) ÷ 五行局 = Y
 */
function calculateZiweiPositionEnhanced(birthDay, wuxingJu) {
  console.log(`🎯 计算紫微星位置：生日${birthDay}，五行局${wuxingJu}`);
  
  // 寻找最小的X使得(birthDay + X)能被wuxingJu整除
  let X = 0;
  while ((birthDay + X) % wuxingJu !== 0) {
    X++;
  }
  
  // 计算商数Y
  const Y = (birthDay + X) / wuxingJu;
  
  console.log(`🎯 计算结果：X=${X}, Y=${Y}`);
  
  // 将Y转换为宫位（寅=1开始）
  let basePosition = ((Y - 1) % 12);
  
  // 根据X的奇偶性调整位置
  let finalPosition;
  if (X === 0) {
    // X为0，紫微在Y所在宫位
    finalPosition = basePosition;
  } else if (X % 2 === 0) {
    // X为偶数，顺时针数X格
    finalPosition = (basePosition + X) % 12;
  } else {
    // X为奇数，逆时针数X格
    finalPosition = (basePosition - X + 12) % 12;
  }
  
  console.log(`🎯 紫微星最终位置：${finalPosition}（${getGongName(finalPosition)}）`);
  return finalPosition;
}

/**
 * 精确的十四主星安星算法
 * 基于古籍口诀优化
 */
function placeFourteenMajorStarsEnhanced(ziweiPosition) {
  const stars = {};
  
  // 紫微星系（逆行安星）
  // 口诀：紫微天机逆行旁，隔一阳武天同当，又隔二位廉贞地，空三复见紫微郎
  stars['紫微'] = ziweiPosition;
  stars['天机'] = (ziweiPosition - 1 + 12) % 12;  // 逆行一宫
  stars['太阳'] = (ziweiPosition - 3 + 12) % 12;  // 隔一宫（逆行三宫）
  stars['武曲'] = (ziweiPosition - 4 + 12) % 12;  // 逆行四宫
  stars['天同'] = (ziweiPosition - 5 + 12) % 12;  // 逆行五宫
  stars['廉贞'] = (ziweiPosition - 8 + 12) % 12;  // 隔二位（逆行八宫）
  
  // 天府星系（顺行安星）
  // 先确定天府位置：紫微天府斜对飞，寅申同宫巳亥对
  const tianfuPosition = calculateTianfuPosition(ziweiPosition);
  stars['天府'] = tianfuPosition;
  
  // 口诀：天府太阴顺流行，贪狼巨门相梁杀，又隔四位破军位，空一便是天府星
  stars['太阴'] = (tianfuPosition + 1) % 12;  // 顺行一宫
  stars['贪狼'] = (tianfuPosition + 2) % 12;  // 顺行二宫
  stars['巨门'] = (tianfuPosition + 3) % 12;  // 顺行三宫
  stars['天相'] = (tianfuPosition + 4) % 12;  // 顺行四宫
  stars['天梁'] = (tianfuPosition + 5) % 12;  // 顺行五宫
  stars['七杀'] = (tianfuPosition + 6) % 12;  // 顺行六宫
  stars['破军'] = (tianfuPosition + 9) % 12;  // 隔四位（顺行九宫）
  
  return stars;
}

/**
 * 计算天府星位置
 * 基于紫微天府斜对关系
 */
function calculateTianfuPosition(ziweiPosition) {
  // 紫微天府斜对飞，寅申同宫巳亥对
  const ziweiGong = getGongName(ziweiPosition);
  
  // 特殊情况：寅申同宫
  if (ziweiGong === '寅' || ziweiGong === '申') {
    return ziweiPosition; // 同宫
  }
  
  // 其他情况：斜对关系
  const duiGongMap = {
    '子': '辰', '丑': '巳', '寅': '午', '卯': '未',
    '辰': '申', '巳': '酉', '午': '戌', '未': '亥',
    '申': '子', '酉': '丑', '戌': '寅', '亥': '卯'
  };
  
  const tianfuGong = duiGongMap[ziweiGong];
  return getGongIndex(tianfuGong);
}

/**
 * 星曜庙旺利陷判断系统
 */
function analyzeStarStrength(starName, gongPosition) {
  const strengthMap = {
    '紫微': {
      '庙': ['子', '午'],
      '旺': ['寅', '卯', '辰', '巳', '申', '酉'],
      '利': ['丑', '未', '戌', '亥'],
      '陷': []
    },
    '天机': {
      '庙': ['卯'],
      '旺': ['寅', '辰', '巳', '午'],
      '利': ['子', '丑', '未', '申', '酉', '戌', '亥'],
      '陷': []
    },
    '太阳': {
      '庙': ['巳', '午'],
      '旺': ['寅', '卯', '辰', '未'],
      '利': ['子', '丑', '申'],
      '陷': ['酉', '戌', '亥']
    },
    '武曲': {
      '庙': ['巳'],
      '旺': ['子', '丑', '寅', '午', '申', '酉'],
      '利': ['卯', '辰', '未', '戌', '亥'],
      '陷': []
    },
    '天同': {
      '庙': ['子', '午'],
      '旺': ['寅', '卯', '辰', '巳', '未', '申'],
      '利': ['丑', '酉', '戌', '亥'],
      '陷': []
    },
    '廉贞': {
      '庙': ['寅', '午'],
      '旺': ['卯', '辰', '巳', '未', '申'],
      '利': ['子', '丑', '酉', '戌', '亥'],
      '陷': []
    },
    '天府': {
      '庙': ['寅', '卯', '辰', '巳', '午', '未', '申', '酉', '戌', '亥'],
      '旺': [],
      '利': ['子', '丑'],
      '陷': []
    },
    '太阴': {
      '庙': ['亥', '子'],
      '旺': ['丑', '寅', '卯', '辰', '酉', '戌'],
      '利': ['巳', '申'],
      '陷': ['午', '未']
    },
    '贪狼': {
      '庙': ['亥', '子'],
      '旺': ['寅', '卯', '辰', '巳', '午', '未'],
      '利': ['丑', '申', '酉', '戌'],
      '陷': []
    },
    '巨门': {
      '庙': ['子'],
      '旺': ['丑', '寅', '巳', '午', '未', '申'],
      '利': ['卯', '辰', '酉', '戌', '亥'],
      '陷': []
    },
    '天相': {
      '庙': ['子', '丑', '寅', '卯', '辰', '巳', '午', '未', '申', '酉'],
      '旺': [],
      '利': ['戌', '亥'],
      '陷': []
    },
    '天梁': {
      '庙': ['午'],
      '旺': ['寅', '卯', '辰', '巳', '未', '申', '酉'],
      '利': ['子', '丑', '戌', '亥'],
      '陷': []
    },
    '七杀': {
      '庙': ['子', '午'],
      '旺': ['寅', '卯', '辰', '巳', '未', '申'],
      '利': ['丑', '酉', '戌', '亥'],
      '陷': []
    },
    '破军': {
      '庙': ['子', '午'],
      '旺': ['寅', '卯', '辰', '巳', '未', '申'],
      '利': ['丑', '酉', '戌', '亥'],
      '陷': []
    }
  };
  
  const gongName = getGongName(gongPosition);
  const starData = strengthMap[starName];
  
  if (!starData) return '平';
  
  if (starData['庙'].includes(gongName)) return '庙';
  if (starData['旺'].includes(gongName)) return '旺';
  if (starData['利'].includes(gongName)) return '利';
  if (starData['陷'].includes(gongName)) return '陷';
  
  return '平';
}

/**
 * 完整格局分析系统
 */
function analyzeZiweiPatternsEnhanced(chart) {
  const patterns = [];
  const mingGong = chart['命宫'];
  const majorStars = mingGong.majorStars || [];
  
  // 1. 紫府格局
  if (majorStars.includes('紫微') && majorStars.includes('天府')) {
    patterns.push({
      name: '紫府同宫',
      level: '上格',
      description: '帝王之星与财库之星同宫，主富贵双全，一生安稳'
    });
  }
  
  // 2. 紫贪格局
  if (majorStars.includes('紫微') && majorStars.includes('贪狼')) {
    patterns.push({
      name: '紫贪同宫',
      level: '中上格',
      description: '帝王星与桃花星同宫，主聪明多才，异性缘佳'
    });
  }
  
  // 3. 武府格局
  if (majorStars.includes('武曲') && majorStars.includes('天府')) {
    patterns.push({
      name: '武府同宫',
      level: '上格',
      description: '财星与库星同宫，主财运亨通，善于理财'
    });
  }
  
  // 4. 杀破狼格局
  const spwStars = ['七杀', '破军', '贪狼'];
  const hasSpw = spwStars.some(star => majorStars.includes(star));
  if (hasSpw) {
    patterns.push({
      name: '杀破狼格局',
      level: '变动格',
      description: '主变动开创，一生多变化，利于开拓事业'
    });
  }
  
  // 5. 机月同梁格局
  const jytlStars = ['天机', '太阴', '天同', '天梁'];
  const hasJytl = jytlStars.filter(star => majorStars.includes(star)).length >= 2;
  if (hasJytl) {
    patterns.push({
      name: '机月同梁格局',
      level: '清贵格',
      description: '主清高贵气，利文职教育，品格高尚'
    });
  }
  
  // 6. 空宫格局
  if (majorStars.length === 0) {
    const duiGong = chart[getOppositeGong(mingGong.name)];
    if (duiGong && duiGong.majorStars && duiGong.majorStars.length > 0) {
      patterns.push({
        name: '空宫借星',
        level: '借星格',
        description: `命宫空宫，借对宫${duiGong.majorStars.join('、')}星照命`
      });
    }
  }
  
  return patterns;
}

/**
 * 辅助函数
 */
function getGongName(index) {
  const gongNames = ['子', '丑', '寅', '卯', '辰', '巳', '午', '未', '申', '酉', '戌', '亥'];
  return gongNames[index];
}

function getGongIndex(gongName) {
  const gongNames = ['子', '丑', '寅', '卯', '辰', '巳', '午', '未', '申', '酉', '戌', '亥'];
  return gongNames.indexOf(gongName);
}

function getOppositeGong(gongName) {
  const oppositeMap = {
    '命宫': '迁移宫', '兄弟宫': '仆役宫', '夫妻宫': '官禄宫',
    '子女宫': '田宅宫', '财帛宫': '福德宫', '疾厄宫': '父母宫',
    '迁移宫': '命宫', '仆役宫': '兄弟宫', '官禄宫': '夫妻宫',
    '田宅宫': '子女宫', '福德宫': '财帛宫', '父母宫': '疾厄宫'
  };
  return oppositeMap[gongName];
}

module.exports = {
  calculateZiweiPositionEnhanced,
  placeFourteenMajorStarsEnhanced,
  analyzeStarStrength,
  analyzeZiweiPatternsEnhanced
};
