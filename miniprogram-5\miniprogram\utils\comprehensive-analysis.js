// 综合分析工具
// 用于计算各种百分比数据和综合评分

/**
 * 计算综合分析数据
 * @param {Object} bazi - 八字数据
 * @param {Object} analysis - 基础分析数据
 * @param {Object} wuxingData - 五行分布数据
 * @param {Object} shenshaData - 神煞数据
 * @param {Object} traditionalInfo - 传统信息
 * @returns {Object} 综合分析结果
 */
function calculateComprehensiveAnalysis(bazi, analysis, wuxingData, shenshaData, traditionalInfo) {
  const result = {
    scores: {},           // 各项评分
    percentages: {},      // 各种百分比
    rankings: {},         // 排名数据
    recommendations: [],  // 建议
    summary: ''          // 总结
  };

  // 计算各项评分（满分100分）
  result.scores = {
    wealth: calculateWealthScore(bazi, analysis),      // 财运评分
    career: calculateCareerScore(bazi, analysis),      // 事业评分
    health: calculateHealthScore(bazi, wuxingData),    // 健康评分
    marriage: calculateMarriageScore(bazi, shenshaData), // 婚姻评分
    study: calculateStudyScore(bazi, shenshaData),     // 学业评分
    overall: 0  // 综合评分，后面计算
  };

  // 计算综合评分
  result.scores.overall = Math.round(
    (result.scores.wealth + result.scores.career + result.scores.health + 
     result.scores.marriage + result.scores.study) / 5
  );

  // 计算各种百分比
  result.percentages = {
    wuxingBalance: calculateWuxingBalancePercentage(wuxingData),
    luckyGod: calculateLuckyGodPercentage(shenshaData),
    strengthLevel: calculateStrengthPercentage(analysis),
    fortunePeriod: calculateFortunePeriodPercentage(bazi)
  };

  // 计算排名数据（模拟数据，实际应用中可以基于数据库统计）
  result.rankings = {
    overall: Math.floor(Math.random() * 30) + 70,  // 70-99%
    wealth: Math.floor(Math.random() * 40) + 60,   // 60-99%
    career: Math.floor(Math.random() * 35) + 65,   // 65-99%
    health: Math.floor(Math.random() * 25) + 75    // 75-99%
  };

  // 生成建议
  result.recommendations = generateRecommendations(result.scores, wuxingData, shenshaData);

  // 生成总结
  result.summary = generateSummary(result.scores, result.percentages);

  return result;
}

/**
 * 计算财运评分
 */
function calculateWealthScore(bazi, analysis) {
  let score = 50; // 基础分

  // 根据财星强弱调整
  if (analysis.tenGods && analysis.tenGods.distribution) {
    const wealthGods = ['正财', '偏财'];
    let wealthStrength = 0;
    
    Object.keys(analysis.tenGods.distribution).forEach(god => {
      if (wealthGods.includes(god)) {
        wealthStrength += analysis.tenGods.distribution[god] || 0;
      }
    });

    if (wealthStrength > 2) score += 20;
    else if (wealthStrength > 1) score += 10;
    else if (wealthStrength === 0) score -= 15;
  }

  // 根据格局调整
  if (analysis.pattern && analysis.pattern.pattern) {
    if (analysis.pattern.pattern.includes('财')) score += 15;
    if (analysis.pattern.pattern.includes('从财')) score += 25;
  }

  return Math.max(0, Math.min(100, score));
}

/**
 * 计算事业评分
 */
function calculateCareerScore(bazi, analysis) {
  let score = 50;

  // 根据官星强弱调整
  if (analysis.tenGods && analysis.tenGods.distribution) {
    const careerGods = ['正官', '七杀'];
    let careerStrength = 0;
    
    Object.keys(analysis.tenGods.distribution).forEach(god => {
      if (careerGods.includes(god)) {
        careerStrength += analysis.tenGods.distribution[god] || 0;
      }
    });

    if (careerStrength > 2) score += 20;
    else if (careerStrength > 1) score += 10;
    else if (careerStrength === 0) score -= 10;
  }

  // 根据格局调整
  if (analysis.pattern && analysis.pattern.pattern) {
    if (analysis.pattern.pattern.includes('官')) score += 15;
    if (analysis.pattern.pattern.includes('杀')) score += 10;
  }

  return Math.max(0, Math.min(100, score));
}

/**
 * 计算健康评分
 */
function calculateHealthScore(bazi, wuxingData) {
  let score = 70; // 健康基础分较高

  if (wuxingData && wuxingData.percentage) {
    // 五行过于偏颇影响健康
    Object.values(wuxingData.percentage).forEach(percent => {
      if (percent > 40) score -= 10; // 某五行过强
      if (percent === 0) score -= 15; // 某五行缺失
    });

    // 五行相对平衡加分
    const maxPercent = Math.max(...Object.values(wuxingData.percentage));
    const minPercent = Math.min(...Object.values(wuxingData.percentage).filter(p => p > 0));
    
    if (maxPercent - minPercent < 30) score += 15;
  }

  return Math.max(0, Math.min(100, score));
}

/**
 * 计算婚姻评分
 */
function calculateMarriageScore(bazi, shenshaData) {
  let score = 60;

  // 桃花星影响
  if (shenshaData && shenshaData.taohua && shenshaData.taohua.length > 0) {
    score += 15;
  }

  // 其他神煞影响
  if (shenshaData) {
    if (shenshaData.tianyi && shenshaData.tianyi.length > 0) score += 10;
    if (shenshaData.yangren && shenshaData.yangren.length > 0) score -= 10;
  }

  return Math.max(0, Math.min(100, score));
}

/**
 * 计算学业评分
 */
function calculateStudyScore(bazi, shenshaData) {
  let score = 60;

  // 文昌贵人影响
  if (shenshaData && shenshaData.wenchang && shenshaData.wenchang.length > 0) {
    score += 20;
  }

  // 华盖星影响
  if (shenshaData && shenshaData.huagai && shenshaData.huagai.length > 0) {
    score += 10;
  }

  return Math.max(0, Math.min(100, score));
}

/**
 * 计算五行平衡百分比
 */
function calculateWuxingBalancePercentage(wuxingData) {
  if (!wuxingData || !wuxingData.percentage) return 50;

  const percentages = Object.values(wuxingData.percentage).filter(p => p > 0);
  const maxPercent = Math.max(...percentages);
  const minPercent = Math.min(...percentages);
  
  // 差值越小，平衡度越高
  const balance = Math.max(0, 100 - (maxPercent - minPercent) * 2);
  return Math.round(balance);
}

/**
 * 计算吉神百分比
 */
function calculateLuckyGodPercentage(shenshaData) {
  if (!shenshaData) return 50;

  let totalGods = 0;
  let luckyGods = 0;

  Object.keys(shenshaData).forEach(category => {
    const gods = shenshaData[category];
    if (Array.isArray(gods)) {
      totalGods += gods.length;
      if (['tianyi', 'wenchang'].includes(category)) {
        luckyGods += gods.length;
      }
    }
  });

  if (totalGods === 0) return 50;
  return Math.round((luckyGods / totalGods) * 100);
}

/**
 * 计算强弱百分比
 */
function calculateStrengthPercentage(analysis) {
  if (!analysis || !analysis.strength) return 50;

  const level = analysis.strength.level;
  const strengthMap = {
    '极强': 90,
    '偏强': 75,
    '中和': 60,
    '偏弱': 40,
    '极弱': 20
  };

  return strengthMap[level] || 50;
}

/**
 * 计算运势周期百分比
 */
function calculateFortunePeriodPercentage(bazi) {
  // 简化计算，实际应该基于大运流年
  const currentYear = new Date().getFullYear();
  const birthYear = bazi.year ? parseInt(bazi.year.stem + bazi.year.branch) : currentYear - 30;
  
  // 模拟运势周期
  const age = currentYear - birthYear;
  const cycle = age % 10; // 10年一个周期
  
  if (cycle >= 3 && cycle <= 7) return 80; // 中间阶段运势较好
  return 60;
}

/**
 * 生成建议
 */
function generateRecommendations(scores, wuxingData, shenshaData) {
  const recommendations = [];

  // 根据评分生成建议
  if (scores.wealth < 60) {
    recommendations.push('财运偏弱，建议多关注理财规划，稳健投资');
  }
  
  if (scores.career < 60) {
    recommendations.push('事业运势一般，建议提升个人能力，把握机遇');
  }
  
  if (scores.health < 70) {
    recommendations.push('注意身体健康，保持良好的作息和饮食习惯');
  }

  // 根据五行分布生成建议
  if (wuxingData && wuxingData.weakestElement) {
    recommendations.push(`五行中${wuxingData.weakestElement}较弱，可适当补充相关元素`);
  }

  // 根据神煞生成建议
  if (shenshaData && shenshaData.wenchang && shenshaData.wenchang.length > 0) {
    recommendations.push('有文昌贵人，适合从事文化教育相关工作');
  }

  if (recommendations.length === 0) {
    recommendations.push('整体运势平衡，保持现状即可');
  }

  return recommendations;
}

/**
 * 生成总结
 */
function generateSummary(scores, percentages) {
  const overallScore = scores.overall;
  
  if (overallScore >= 80) {
    return '综合运势优秀，各方面发展均衡，前景光明';
  } else if (overallScore >= 70) {
    return '综合运势良好，有一定优势，需要继续努力';
  } else if (overallScore >= 60) {
    return '综合运势中等，有改善空间，需要重点关注薄弱环节';
  } else {
    return '综合运势偏弱，需要多方面调理和改善';
  }
}

module.exports = {
  calculateComprehensiveAnalysis,
  calculateWealthScore,
  calculateCareerScore,
  calculateHealthScore,
  calculateMarriageScore,
  calculateStudyScore
};
