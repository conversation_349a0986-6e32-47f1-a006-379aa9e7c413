// 双重验证系统
// 整合八字分析和终身卦分析，提供高精度的命理预测

// 导入相关模块
const { calculateLifeHexagram, verifyConsistency } = require('./life-hexagram-calculator.js');
const { predictSpecificTimes } = require('./time-prediction-engine.js');
const { comprehensiveBaziAnalysis } = require('./bazi-analysis.js');

/**
 * 双重验证分析系统
 * @param {Object} bazi - 八字信息
 * @param {Object} birthInfo - 出生信息
 * @param {string} question - 用户问题
 * @param {Object} currentInfo - 当前信息
 * @returns {Object} 双重验证结果
 */
function performDualVerification(bazi, birthInfo, question, currentInfo) {
  console.log('🔄 启动双重验证系统...');
  
  try {
    // 第一步：计算终身卦（隐藏式）
    const lifeHexagram = calculateLifeHexagram(birthInfo);
    console.log('✅ 终身卦计算完成:', lifeHexagram.originalHexagram.name);
    
    // 第二步：八字综合分析
    const baziAnalysis = comprehensiveBaziAnalysis(bazi);
    console.log('✅ 八字分析完成');
    
    // 第三步：验证一致性
    const consistency = verifyConsistency(lifeHexagram, bazi);
    console.log('✅ 一致性验证完成，得分:', consistency.consistency);
    
    // 第四步：时间预测分析
    const eventType = extractEventType(question);
    const timePrediction = predictSpecificTimes(bazi, eventType, currentInfo, lifeHexagram);
    console.log('✅ 时间预测完成');
    
    // 第五步：综合分析结果
    const finalAnalysis = synthesizeAnalysis(bazi, lifeHexagram, baziAnalysis, timePrediction, consistency, question);
    console.log('✅ 综合分析完成');
    
    return {
      success: true,
      dualVerification: {
        baziAnalysis: baziAnalysis,
        lifeHexagram: lifeHexagram,
        consistency: consistency,
        timePrediction: timePrediction,
        finalAnalysis: finalAnalysis,
        confidence: calculateOverallConfidence(consistency, timePrediction, baziAnalysis),
        timestamp: new Date().toISOString()
      },
      // 用户友好的结果（隐藏复杂计算过程）
      userResult: formatUserFriendlyResult(finalAnalysis, timePrediction, consistency)
    };
    
  } catch (error) {
    console.error('❌ 双重验证系统错误:', error);
    return {
      success: false,
      error: error.message,
      fallbackResult: '系统分析中遇到问题，建议重新分析'
    };
  }
}

/**
 * 提取问题中的事件类型
 * @param {string} question - 用户问题
 * @returns {string} 事件类型
 */
function extractEventType(question) {
  const eventKeywords = {
    '财运': ['财运', '赚钱', '发财', '收入', '投资', '股票', '生意'],
    '事业': ['事业', '工作', '升职', '跳槽', '创业', '官运', '仕途'],
    '婚姻': ['婚姻', '结婚', '感情', '恋爱', '配偶', '另一半', '对象'],
    '学业': ['学业', '考试', '升学', '学习', '读书', '文凭', '学历'],
    '健康': ['健康', '身体', '疾病', '医疗', '养生', '康复', '治疗'],
    '子女': ['子女', '孩子', '生育', '怀孕', '生子', '儿女', '后代'],
    '朋友': ['朋友', '人际', '合伙', '同事', '社交', '关系', '贵人']
  };
  
  for (const [type, keywords] of Object.entries(eventKeywords)) {
    if (keywords.some(keyword => question.includes(keyword))) {
      return type;
    }
  }
  
  return '综合运势';
}

/**
 * 综合分析结果
 * @param {Object} bazi - 八字信息
 * @param {Object} lifeHexagram - 终身卦
 * @param {Object} baziAnalysis - 八字分析
 * @param {Object} timePrediction - 时间预测
 * @param {Object} consistency - 一致性验证
 * @param {string} question - 用户问题
 * @returns {Object} 综合分析
 */
function synthesizeAnalysis(bazi, lifeHexagram, baziAnalysis, timePrediction, consistency, question) {
  // 基础命理特征
  const coreTraits = analyzeCoreTraits(bazi, lifeHexagram, baziAnalysis);
  
  // 人生发展趋势
  const lifeTrend = analyzeLifeTrend(bazi, lifeHexagram, baziAnalysis);
  
  // 关键时间节点
  const keyTimePoints = extractKeyTimePoints(timePrediction);
  
  // 具体建议
  const recommendations = generateRecommendations(bazi, lifeHexagram, timePrediction, question);
  
  // 验证说明
  const verificationExplanation = generateVerificationExplanation(consistency, timePrediction);
  
  return {
    coreTraits: coreTraits,
    lifeTrend: lifeTrend,
    keyTimePoints: keyTimePoints,
    recommendations: recommendations,
    verificationExplanation: verificationExplanation,
    analysisMethod: '基于子平八字与梅花易数双重验证分析',
    knowledgeBase: '严格依据《子平真诠》《滴天髓》《梅花易数》等古籍理论'
  };
}

/**
 * 分析核心特征
 */
function analyzeCoreTraits(bazi, lifeHexagram, baziAnalysis) {
  const traits = [];
  
  // 八字特征
  if (baziAnalysis.pattern && baziAnalysis.pattern.pattern) {
    traits.push(`八字格局：${baziAnalysis.pattern.pattern}`);
  }
  
  if (baziAnalysis.strength && baziAnalysis.strength.level) {
    traits.push(`日主强弱：${baziAnalysis.strength.level}`);
  }
  
  // 终身卦特征
  if (lifeHexagram.analysis && lifeHexagram.analysis.lifeTheme) {
    traits.push(`命运主题：${lifeHexagram.analysis.lifeTheme}`);
  }
  
  if (lifeHexagram.analysis && lifeHexagram.analysis.personality) {
    traits.push(`性格特征：${lifeHexagram.analysis.personality.mainTrait}`);
  }
  
  return traits;
}

/**
 * 分析人生发展趋势
 */
function analyzeLifeTrend(bazi, lifeHexagram, baziAnalysis) {
  const trends = [];
  
  // 基于八字的趋势
  if (baziAnalysis.useGod && baziAnalysis.useGod.gods) {
    trends.push(`用神为${baziAnalysis.useGod.gods.join('、')}，宜从事相关五行的行业`);
  }
  
  // 基于终身卦的趋势
  if (lifeHexagram.analysis && lifeHexagram.analysis.trendAnalysis) {
    trends.push(lifeHexagram.analysis.trendAnalysis.direction);
  }
  
  // 综合趋势判断
  const overallTrend = determineOverallTrend(bazi, lifeHexagram);
  trends.push(overallTrend);
  
  return trends;
}

/**
 * 确定整体趋势
 */
function determineOverallTrend(bazi, lifeHexagram) {
  // 简化的趋势分析逻辑
  const baziStrength = bazi.strength || '中和';
  const hexagramName = lifeHexagram.originalHexagram.name;
  
  if (baziStrength.includes('偏强') && hexagramName.includes('乾')) {
    return '命格较强，适合主动出击，创业或担任领导职务';
  } else if (baziStrength.includes('偏弱') && hexagramName.includes('坤')) {
    return '命格偏柔，适合稳扎稳打，从事辅助性或服务性工作';
  } else {
    return '命格中和，进退有度，适应性强，可根据时机灵活调整发展方向';
  }
}

/**
 * 提取关键时间节点
 */
function extractKeyTimePoints(timePrediction) {
  const timePoints = [];
  
  // 过去验证点
  if (timePrediction.pastAnalysis && timePrediction.pastAnalysis.length > 0) {
    timePoints.push({
      type: '过去验证',
      events: timePrediction.pastAnalysis.map(event => ({
        time: `${event.year}年${event.season}`,
        description: event.description,
        probability: event.probability
      }))
    });
  }
  
  // 未来预测点
  if (timePrediction.futurePredictons && timePrediction.futurePredictons.length > 0) {
    timePoints.push({
      type: '未来预测',
      events: timePrediction.futurePredictons.map(event => ({
        time: `${event.year}年${event.season}`,
        description: event.description,
        probability: event.probability
      }))
    });
  }
  
  return timePoints;
}

/**
 * 生成具体建议
 */
function generateRecommendations(bazi, lifeHexagram, timePrediction, question) {
  const recommendations = [];
  
  // 基于八字的建议
  if (bazi.useGod) {
    recommendations.push(`根据用神分析，建议多接触${bazi.useGod}相关的人事物`);
  }
  
  // 基于终身卦的建议
  if (lifeHexagram.analysis && lifeHexagram.analysis.keyInsights) {
    lifeHexagram.analysis.keyInsights.forEach(insight => {
      if (insight.includes('建议') || insight.includes('宜') || insight.includes('应')) {
        recommendations.push(insight);
      }
    });
  }
  
  // 基于时间预测的建议
  if (timePrediction.futurePredictons && timePrediction.futurePredictons.length > 0) {
    const nextEvent = timePrediction.futurePredictons[0];
    recommendations.push(`重点关注${nextEvent.year}年${nextEvent.season}，${nextEvent.reasoning}`);
  }
  
  // 通用建议
  recommendations.push('建议定期回顾分析结果，结合实际情况调整行动策略');
  
  return recommendations;
}

/**
 * 生成验证说明
 */
function generateVerificationExplanation(consistency, timePrediction) {
  const explanation = [];
  
  explanation.push(`双重验证一致性得分：${consistency.consistency}分`);
  explanation.push(`时间预测可信度：${timePrediction.confidence}%`);
  
  if (consistency.consistency >= 80) {
    explanation.push('八字与终身卦高度一致，分析结果可信度很高');
  } else if (consistency.consistency >= 60) {
    explanation.push('八字与终身卦基本一致，分析结果具有参考价值');
  } else {
    explanation.push('八字与终身卦存在差异，建议结合实际情况谨慎参考');
  }
  
  return explanation.join('；');
}

/**
 * 计算整体可信度
 */
function calculateOverallConfidence(consistency, timePrediction, baziAnalysis) {
  let confidence = 60; // 基础可信度
  
  // 一致性加分
  confidence += consistency.consistency * 0.2;
  
  // 时间预测加分
  confidence += timePrediction.confidence * 0.15;
  
  // 八字分析完整性加分
  if (baziAnalysis.pattern && baziAnalysis.strength && baziAnalysis.useGod) {
    confidence += 10;
  }
  
  return Math.min(95, Math.round(confidence));
}

/**
 * 格式化用户友好结果
 */
function formatUserFriendlyResult(finalAnalysis, timePrediction, consistency) {
  let result = '';
  
  // 核心特征
  result += '【命理特征】\n';
  finalAnalysis.coreTraits.forEach(trait => {
    result += `• ${trait}\n`;
  });
  result += '\n';
  
  // 发展趋势
  result += '【发展趋势】\n';
  finalAnalysis.lifeTrend.forEach(trend => {
    result += `• ${trend}\n`;
  });
  result += '\n';
  
  // 时间节点
  if (finalAnalysis.keyTimePoints.length > 0) {
    finalAnalysis.keyTimePoints.forEach(timePoint => {
      result += `【${timePoint.type}】\n`;
      timePoint.events.forEach(event => {
        result += `• ${event.time}：${event.description}（概率${event.probability}）\n`;
      });
      result += '\n';
    });
  }
  
  // 具体建议
  result += '【调理建议】\n';
  finalAnalysis.recommendations.forEach(rec => {
    result += `• ${rec}\n`;
  });
  result += '\n';
  
  // 验证说明
  result += '【分析说明】\n';
  result += `• ${finalAnalysis.verificationExplanation}\n`;
  result += `• ${finalAnalysis.analysisMethod}\n`;
  result += `• ${finalAnalysis.knowledgeBase}\n`;
  
  return result;
}

/**
 * 快速双重验证（简化版）
 * @param {Object} bazi - 八字信息
 * @param {Object} birthInfo - 出生信息
 * @returns {Object} 简化验证结果
 */
function quickDualVerification(bazi, birthInfo) {
  try {
    const lifeHexagram = calculateLifeHexagram(birthInfo);
    const consistency = verifyConsistency(lifeHexagram, bazi);
    
    return {
      success: true,
      lifeHexagram: lifeHexagram.originalHexagram.name,
      consistency: consistency.consistency,
      recommendation: consistency.recommendation
    };
  } catch (error) {
    return {
      success: false,
      error: error.message
    };
  }
}

module.exports = {
  performDualVerification,
  quickDualVerification,
  extractEventType,
  synthesizeAnalysis,
  formatUserFriendlyResult
};
