// pages/index/index.js - 主界面
const app = getApp();

Page({
  data: {
    userInfo: null,
    isLoggedIn: false,

    // 功能模块列表 - 统一黑白水墨太极风格
    functionList: [
      {
        id: 'yijing',
        title: '周易卦象',
        subtitle: '朱熹原著',
        icon: '☰',
        descriptionLine1: '朱熹原著',
        descriptionLine2: '传统六爻',
        color: 'linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 50%, #1a1a1a 100%)',
        route: '/pages/yijing/yijing'
      },
      {
        id: 'meihua',
        title: '梅花易数',
        subtitle: '邵雍原著',
        icon: '梅',
        descriptionLine1: '邵雍原著',
        descriptionLine2: '高阶卜卦',
        color: 'linear-gradient(135deg, #2d2d2d 0%, #1a1a1a 50%, #333333 100%)',
        route: '/pages/meihua/meihua'
      },
      {
        id: 'bazi',
        title: '子平八字',
        subtitle: '徐子平著',
        icon: '八',
        descriptionLine1: '徐子平著',
        descriptionLine2: '四柱推命',
        color: 'linear-gradient(135deg, #333333 0%, #1a1a1a 50%, #2d2d2d 100%)',
        route: '/pages/bazi/bazi'
      },
      {
        id: 'ziwei',
        title: '紫微斗数',
        subtitle: '陈希夷著',
        icon: '紫',
        descriptionLine1: '陈希夷著',
        descriptionLine2: '星宿命盘',
        color: 'linear-gradient(135deg, #1a1a1a 0%, #333333 50%, #2d2d2d 100%)',
        route: '/pages/ziwei/ziwei'
      }
    ]
  },
  onLoad() {
    console.log('主界面加载');
    this.initPage();
  },

  onShow() {
    this.refreshUserInfo();
  },

  // 初始化页面
  initPage() {
    this.refreshUserInfo();
    this.checkFirstVisit();
  },

  // 刷新用户信息
  refreshUserInfo() {
    const userInfo = app.globalData.userInfo;

    this.setData({
      userInfo: userInfo,
      isLoggedIn: !!userInfo
    });
  },

  // 检查首次访问
  checkFirstVisit() {
    const isFirstVisit = wx.getStorageSync('isFirstVisit');
    if (!isFirstVisit) {
      wx.setStorageSync('isFirstVisit', true);
      // 可以显示引导页面或提示
    }
  },

  // 点击功能模块
  onClickFunction(e) {
    const { id, route } = e.currentTarget.dataset;

    // 检查是否登录
    if (!this.data.isLoggedIn) {
      this.showLoginTip();
      return;
    }

    // 所有功能模块都已开发完成，可以直接访问

    // 直接跳转到对应功能页面（免费使用）
    wx.navigateTo({
      url: route
    });
  },

  // 点击免费功能
  onClickFreeFeature(e) {
    const { route } = e.currentTarget.dataset;
    wx.navigateTo({
      url: route
    });
  },

  // 显示登录提示
  showLoginTip() {
    wx.showModal({
      title: '需要登录',
      content: '请先登录后使用此功能',
      confirmText: '去登录',
      cancelText: '取消',
      success: (res) => {
        if (res.confirm) {
          wx.navigateTo({
            url: '/pages/splash/splash'
          });
        }
      }
    });
  },





  // 页面分享
  onShareAppMessage() {
    return {
      title: '元亨利贞 - 千年古籍AI智慧',
      path: '/pages/splash/splash',
      imageUrl: '/images/share-cover.jpg'
    };
  }
});
