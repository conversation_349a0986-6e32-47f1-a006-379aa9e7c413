// 测试优化后的八字和紫微斗数系统

const { comprehensiveBaziAnalysis } = require('./miniprogram/utils/bazi-analysis.js');
const { calculateBazi } = require('./miniprogram/utils/bazi-calculator.js');
const { calculateZiweiChart } = require('./miniprogram/utils/ziwei-calculator.js');
const { comprehensiveZiweiAnalysis } = require('./miniprogram/utils/ziwei-analysis.js');

// 测试数据
const testData = {
  year: 1990,
  month: 5,
  day: 15,
  hour: 14,
  gender: '男',
  location: '北京'
};

console.log('🎯 开始测试优化后的系统');
console.log('测试数据:', testData);

// 测试八字系统
console.log('\n=== 测试八字系统（优化版） ===');
try {
  // 创建正确的日期对象
  const birthDate = new Date(testData.year, testData.month - 1, testData.day, testData.hour);
  const baziData = calculateBazi(birthDate);
  console.log('八字排盘结果:', baziData);

  const baziAnalysis = comprehensiveBaziAnalysis(baziData);
  console.log('八字分析结果:', JSON.stringify(baziAnalysis, null, 2));
} catch (error) {
  console.error('八字系统测试失败:', error);
}

// 测试紫微斗数系统
console.log('\n=== 测试紫微斗数系统（优化版） ===');
try {
  // 创建农历信息对象
  const lunarInfo = {
    year: testData.year,
    month: testData.month,
    day: testData.day,
    hour: testData.hour,
    yearStem: '庚',
    yearBranch: '午',
    gender: testData.gender
  };

  const ziweiData = calculateZiweiChart(lunarInfo);
  console.log('紫微排盘结果:', JSON.stringify(ziweiData, null, 2));

  const ziweiAnalysis = comprehensiveZiweiAnalysis(ziweiData);
  console.log('紫微分析结果:', JSON.stringify(ziweiAnalysis, null, 2));
} catch (error) {
  console.error('紫微斗数系统测试失败:', error);
}

console.log('\n🎯 系统测试完成');
