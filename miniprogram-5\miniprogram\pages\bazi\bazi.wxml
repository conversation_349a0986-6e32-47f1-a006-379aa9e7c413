<!--pages/bazi/bazi.wxml - 子平八字页面-->
<view class="container">
  <!-- 页面标题 -->
  <view class="header">
    <text class="title">子平八字</text>
    <text class="subtitle">朱熹原著·传统命理</text>
  </view>

  <!-- 输入区域 -->
  <view class="input-section" wx:if="{{!showResult}}">
    <!-- 问题输入 -->
    <view class="input-group">
      <text class="label">请输入您的问题：</text>
      <textarea
        class="question-input"
        placeholder="例如：我的财运如何？何时能升职？婚姻运势怎样？"
        value="{{question}}"
        bindinput="onQuestionInput"
        maxlength="200"
      ></textarea>
    </view>

    <!-- 出生信息 -->
    <view class="birth-info">
      <view class="info-row">
        <text class="label">出生日期：</text>
        <picker mode="date" value="{{birthDate}}" bindchange="onDateChange">
          <view class="picker">{{birthDate || '请选择'}}</view>
        </picker>
      </view>

      <view class="info-row">
        <text class="label">出生时间：</text>
        <picker mode="time" value="{{birthTime}}" bindchange="onTimeChange">
          <view class="picker">{{birthTime || '请选择'}}</view>
        </picker>
      </view>

      <view class="info-row">
        <text class="label">出生地：</text>
        <picker range="{{cityList}}" range-key="name" value="{{selectedCityIndex}}" bindchange="onCityChange">
          <view class="picker">{{selectedCity || '请选择出生地'}}</view>
        </picker>
      </view>

      <view class="info-row" wx:if="{{useTrueSolarTime}}">
        <view class="solar-time-info">
          <text class="solar-time-title">真太阳时修正</text>
          <text class="solar-time-desc">{{solarTimeExplanation}}</text>
          <text class="solar-time-result">修正后时间：{{trueSolarTimeString}}</text>
        </view>
      </view>

      <view class="info-row">
        <text class="label">性别：</text>
        <radio-group bindchange="onGenderChange">
          <label class="radio">
            <radio value="男" checked="{{isMale}}" />男
          </label>
          <label class="radio">
            <radio value="女" checked="{{!isMale}}" />女
          </label>
        </radio-group>
      </view>
    </view>

    <!-- 开始按钮 -->
    <button
      class="start-btn"
      bindtap="onStartAnalysis"
      loading="{{isAnalyzing}}"
      disabled="{{isAnalyzing}}"
    >
      {{isAnalyzing ? '正在排盘...' : '开始排盘'}}
    </button>
  </view>

  <!-- 结果显示区域 -->
  <view class="result-section" wx:if="{{showResult}}">
    <!-- 四柱八字 -->
    <view class="bazi-chart">
      <text class="section-title">四柱八字</text>
      <view class="pillars">
        <view class="pillar">
          <text class="pillar-name">年柱</text>
          <view class="pillar-value-vertical">
            <text class="pillar-stem">{{formattedBazi.yearStem}}</text>
            <text class="pillar-branch">{{formattedBazi.yearBranch}}</text>
          </view>
        </view>
        <view class="pillar">
          <text class="pillar-name">月柱</text>
          <view class="pillar-value-vertical">
            <text class="pillar-stem">{{formattedBazi.monthStem}}</text>
            <text class="pillar-branch">{{formattedBazi.monthBranch}}</text>
          </view>
        </view>
        <view class="pillar">
          <text class="pillar-name">日柱</text>
          <view class="pillar-value-vertical">
            <text class="pillar-stem">{{formattedBazi.dayStem}}</text>
            <text class="pillar-branch">{{formattedBazi.dayBranch}}</text>
          </view>
        </view>
        <view class="pillar">
          <text class="pillar-name">时柱</text>
          <view class="pillar-value-vertical">
            <text class="pillar-stem">{{formattedBazi.hourStem}}</text>
            <text class="pillar-branch">{{formattedBazi.hourBranch}}</text>
          </view>
        </view>
      </view>
      <view class="daymaster">
        <text>日主：{{analysis.dayMaster}}（{{analysis.dayMasterElement}}）</text>
      </view>
    </view>

    <!-- 格局分析 -->
    <view class="pattern-analysis">
      <text class="section-title">格局分析</text>
      <view class="analysis-content">
        <text class="pattern">格局：{{analysis.pattern.pattern}}</text>
        <text class="strength">强弱：{{analysis.strength.level}}</text>
        <text class="pattern-desc">{{analysis.pattern.analysis[0] || '普通格局，需要综合分析'}}</text>
      </view>
    </view>

    <!-- 个人信息 -->
    <view class="personal-info" wx:if="{{traditionalInfo}}">
      <text class="section-title">个人信息</text>
      <view class="info-sections">
        <!-- 基本信息 -->
        <view class="info-section">
          <text class="info-section-title">基本信息</text>
          <view class="info-grid">
            <view class="info-item">
              <text class="info-label">性别</text>
              <text class="info-value">{{traditionalInfo.personalInfo.gender}}</text>
            </view>
            <view class="info-item">
              <text class="info-label">年龄</text>
              <text class="info-value">{{traditionalInfo.personalInfo.age}}岁</text>
            </view>
            <view class="info-item">
              <text class="info-label">出生地</text>
              <text class="info-value">{{traditionalInfo.personalInfo.birthLocation}}</text>
            </view>
            <view class="info-item">
              <text class="info-label">阳历</text>
              <text class="info-value">{{traditionalInfo.personalInfo.solarCalendar}}</text>
            </view>
          </view>
        </view>

        <!-- 命理信息 -->
        <view class="info-section">
          <text class="info-section-title">命理信息</text>
          <view class="info-grid">
            <view class="info-item">
              <text class="info-label">纳音</text>
              <text class="info-value">{{traditionalInfo.nayin}}</text>
            </view>
            <view class="info-item">
              <text class="info-label">五行命</text>
              <text class="info-value">{{traditionalInfo.wuxingDestiny}}</text>
            </view>
            <view class="info-item">
              <text class="info-label">生肖</text>
              <text class="info-value">{{traditionalInfo.zodiac}}</text>
            </view>
            <view class="info-item">
              <text class="info-label">星座</text>
              <text class="info-value">{{traditionalInfo.constellation}}</text>
            </view>
            <view class="info-item">
              <text class="info-label">本命佛</text>
              <text class="info-value">{{traditionalInfo.benmingBuddha}}</text>
            </view>
            <view class="info-item">
              <text class="info-label">命卦</text>
              <text class="info-value">{{traditionalInfo.mingGua}}</text>
            </view>
          </view>
        </view>

        <!-- 吉祥物推荐 -->
        <view class="info-section">
          <text class="info-section-title">吉祥物推荐</text>
          <view class="amulet-recommendation">
            <text class="amulet-text">{{traditionalInfo.zodiacAmulet}}</text>
            <text class="amulet-desc">根据您的生肖和五行属性推荐</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 五行力量分布 -->
    <view class="wuxing-distribution" wx:if="{{wuxingData}}">
      <text class="section-title">五行力量分布</text>
      <view class="wuxing-chart-container">
        <!-- 圆环图区域 -->
        <view class="chart-circle">
          <view class="chart-center">
            <text class="center-title">五行</text>
            <text class="center-subtitle">力量</text>
          </view>
        </view>

        <!-- 五行数据列表 -->
        <view class="wuxing-legend">
          <view
            class="legend-item"
            wx:for="{{wuxingData.chartData}}"
            wx:key="name"
          >
            <view class="legend-color" style="background-color: {{item.color}};"></view>
            <text class="legend-name">{{item.name}}</text>
            <text class="legend-percentage">{{item.percentage}}%</text>
          </view>
        </view>
      </view>

      <!-- 五行分析 -->
      <view class="wuxing-analysis" wx:if="{{wuxingAnalysis}}">
        <text class="analysis-title">平衡状态：{{wuxingAnalysis.balance}}</text>
        <text class="analysis-desc">{{wuxingAnalysis.description}}</text>
        <view class="analysis-suggestions">
          <text class="suggestion-title">调理建议：</text>
          <text
            class="suggestion-item"
            wx:for="{{wuxingAnalysis.suggestions}}"
            wx:key="*this"
          >• {{item}}</text>
        </view>
      </view>
    </view>

    <!-- 神煞信息 -->
    <view class="shensha-info" wx:if="{{shenshaData}}">
      <text class="section-title">神煞信息</text>
      <view class="shensha-content">
        <!-- 神煞列表 -->
        <view class="shensha-list">
          <view
            class="shensha-item"
            wx:for="{{formatShenshaList(shenshaData)}}"
            wx:key="name"
          >
            <view class="shensha-name">{{item.name}}</view>
            <view class="shensha-position" wx:if="{{item.position}}">{{item.position}}</view>
            <view class="shensha-desc">{{item.description}}</view>
          </view>
        </view>

        <!-- 神煞分析 -->
        <view class="shensha-analysis" wx:if="{{shenshaAnalysis}}">
          <text class="analysis-summary">{{shenshaAnalysis.summary}}</text>

          <view class="shensha-categories">
            <view class="category-section" wx:if="{{shenshaAnalysis.lucky.length > 0}}">
              <text class="category-title lucky">吉神 ({{shenshaAnalysis.lucky.length}}个)</text>
              <view class="category-items">
                <text
                  class="category-item"
                  wx:for="{{shenshaAnalysis.lucky}}"
                  wx:key="name"
                >{{item.name}}</text>
              </view>
            </view>

            <view class="category-section" wx:if="{{shenshaAnalysis.unlucky.length > 0}}">
              <text class="category-title unlucky">凶神 ({{shenshaAnalysis.unlucky.length}}个)</text>
              <view class="category-items">
                <text
                  class="category-item"
                  wx:for="{{shenshaAnalysis.unlucky}}"
                  wx:key="name"
                >{{item.name}}</text>
              </view>
            </view>

            <view class="category-section" wx:if="{{shenshaAnalysis.neutral.length > 0}}">
              <text class="category-title neutral">中性 ({{shenshaAnalysis.neutral.length}}个)</text>
              <view class="category-items">
                <text
                  class="category-item"
                  wx:for="{{shenshaAnalysis.neutral}}"
                  wx:key="name"
                >{{item.name}}</text>
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 综合分析 -->
    <view class="comprehensive-analysis" wx:if="{{comprehensiveAnalysis}}">
      <text class="section-title">综合分析</text>
      <view class="analysis-content">
        <!-- 评分雷达图 -->
        <view class="scores-section">
          <text class="subsection-title">各项评分</text>
          <view class="scores-grid">
            <view class="score-item">
              <text class="score-label">财运</text>
              <view class="score-bar">
                <view class="score-fill" style="width: {{comprehensiveAnalysis.scores.wealth}}%;"></view>
              </view>
              <text class="score-value">{{comprehensiveAnalysis.scores.wealth}}分</text>
            </view>
            <view class="score-item">
              <text class="score-label">事业</text>
              <view class="score-bar">
                <view class="score-fill" style="width: {{comprehensiveAnalysis.scores.career}}%;"></view>
              </view>
              <text class="score-value">{{comprehensiveAnalysis.scores.career}}分</text>
            </view>
            <view class="score-item">
              <text class="score-label">健康</text>
              <view class="score-bar">
                <view class="score-fill" style="width: {{comprehensiveAnalysis.scores.health}}%;"></view>
              </view>
              <text class="score-value">{{comprehensiveAnalysis.scores.health}}分</text>
            </view>
            <view class="score-item">
              <text class="score-label">婚姻</text>
              <view class="score-bar">
                <view class="score-fill" style="width: {{comprehensiveAnalysis.scores.marriage}}%;"></view>
              </view>
              <text class="score-value">{{comprehensiveAnalysis.scores.marriage}}分</text>
            </view>
            <view class="score-item">
              <text class="score-label">学业</text>
              <view class="score-bar">
                <view class="score-fill" style="width: {{comprehensiveAnalysis.scores.study}}%;"></view>
              </view>
              <text class="score-value">{{comprehensiveAnalysis.scores.study}}分</text>
            </view>
          </view>

          <!-- 综合评分 -->
          <view class="overall-score">
            <text class="overall-label">综合评分</text>
            <text class="overall-value">{{comprehensiveAnalysis.scores.overall}}分</text>
            <text class="overall-rank">超越{{comprehensiveAnalysis.rankings.overall}}%的用户</text>
          </view>
        </view>

        <!-- 百分比数据 -->
        <view class="percentages-section">
          <text class="subsection-title">关键指标</text>
          <view class="percentage-grid">
            <view class="percentage-item">
              <text class="percentage-label">五行平衡度</text>
              <text class="percentage-value">{{comprehensiveAnalysis.percentages.wuxingBalance}}%</text>
            </view>
            <view class="percentage-item">
              <text class="percentage-label">吉神比例</text>
              <text class="percentage-value">{{comprehensiveAnalysis.percentages.luckyGod}}%</text>
            </view>
            <view class="percentage-item">
              <text class="percentage-label">强弱程度</text>
              <text class="percentage-value">{{comprehensiveAnalysis.percentages.strengthLevel}}%</text>
            </view>
            <view class="percentage-item">
              <text class="percentage-label">运势周期</text>
              <text class="percentage-value">{{comprehensiveAnalysis.percentages.fortunePeriod}}%</text>
            </view>
          </view>
        </view>

        <!-- 建议和总结 -->
        <view class="recommendations-section">
          <text class="subsection-title">调理建议</text>
          <view class="recommendations-list">
            <text
              class="recommendation-item"
              wx:for="{{comprehensiveAnalysis.recommendations}}"
              wx:key="*this"
            >• {{item}}</text>
          </view>
          <view class="summary-box">
            <text class="summary-text">{{comprehensiveAnalysis.summary}}</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 十神分布 -->
    <view class="tengod-analysis" wx:if="{{showResult}}">
      <text class="section-title">十神分布</text>
      <view class="tengod-content">
        <text class="tengod-text">{{formattedTenGods || '十神分析中...'}}</text>
      </view>
    </view>

    <!-- 大运流年 -->
    <view class="luck-analysis" wx:if="{{showResult}}">
      <text class="section-title">大运流年</text>
      <view class="luck-content">
        <text class="current-luck">当前大运：{{formattedDayun || '计算中...'}}</text>
        <text class="current-year">当前流年：{{currentLiunian ? currentLiunian.ganzhi + '年' : '计算中...'}}</text>

        <!-- 一生大运详表 -->
        <view class="lifetime-dayun">
          <text class="subsection-title">一生大运详表</text>
          <text class="dayun-list">{{formattedLifetimeDayun || '计算中...'}}</text>
        </view>
      </view>
    </view>

    <!-- 专项分析 -->
    <view class="custom-analysis" wx:if="{{showResult}}">
      <text class="section-title">专项分析</text>
      <view class="analysis-content">
        <text class="question-type">问题类型：{{customAnalysis ? customAnalysis.questionType : '财运'}}</text>
        <text class="analysis-result">{{formattedCustomAnalysis || '专项分析中...'}}</text>
      </view>
    </view>

    <!-- AI智能分析 -->
    <view class="ai-analysis" wx:if="{{analysis.aiAnalysis}}">
      <text class="section-title">AI智能解读</text>
      <view class="analysis-content">
        <text class="ai-result">{{analysis.aiAnalysis}}</text>
      </view>
    </view>

    <!-- 重新排盘按钮 -->
    <button class="restart-btn" bindtap="onRestart">重新排盘</button>
  </view>

  <!-- 预分析对话界面 -->
  <view class="conversation-panel" wx:if="{{showConversationPanel}}">
    <view class="conversation-header">
      <text class="conversation-title">精准度提升咨询</text>
      <text class="conversation-subtitle">为了提供更准确的分析，请回答以下问题</text>
      <view class="close-btn" bindtap="closeConversationMode">×</view>
    </view>

    <view class="conversation-content">
      <!-- 对话历史 -->
      <scroll-view class="conversation-history" scroll-y="true" scroll-top="{{scrollTop}}">
        <view class="message-item" wx:for="{{conversationHistory}}" wx:key="timestamp">
          <view class="message {{item.type === 'ai' ? 'ai-message' : 'user-message'}}">
            <view class="message-content">{{item.content}}</view>
            <view class="message-time">{{item.timestamp}}</view>
          </view>
        </view>

        <!-- AI正在打字指示器 -->
        <view class="typing-indicator" wx:if="{{isTyping}}">
          <view class="typing-dots">
            <view class="dot"></view>
            <view class="dot"></view>
            <view class="dot"></view>
          </view>
          <text class="typing-text">AI正在思考...</text>
        </view>
      </scroll-view>

      <!-- 输入区域 -->
      <view class="conversation-input-area" wx:if="{{isWaitingResponse}}">
        <view class="input-container">
          <textarea
            class="conversation-input"
            placeholder="请输入您的回答..."
            value="{{conversationInput}}"
            bindinput="onConversationInput"
            maxlength="500"
          ></textarea>
          <button class="send-btn" bindtap="handleUserResponse">发送</button>
        </view>
      </view>
    </view>
  </view>
</view>