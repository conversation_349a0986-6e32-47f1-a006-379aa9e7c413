// 八字神煞分析系统
// 基于传统命理学神煞理论

/**
 * 神煞数据库
 */
const SHENSHA_DATA = {
  // 天乙贵人
  tianyi: {
    '甲': ['丑', '未'],
    '乙': ['子', '申'],
    '丙': ['亥', '酉'],
    '丁': ['亥', '酉'],
    '戊': ['丑', '未'],
    '己': ['子', '申'],
    '庚': ['丑', '未'],
    '辛': ['寅', '午'],
    '壬': ['卯', '巳'],
    '癸': ['卯', '巳']
  },
  
  // 文昌贵人
  wenchang: {
    '甲': '巳',
    '乙': '午',
    '丙': '申',
    '丁': '酉',
    '戊': '申',
    '己': '酉',
    '庚': '亥',
    '辛': '子',
    '壬': '寅',
    '癸': '卯'
  },
  
  // 文曲贵人
  wenqu: {
    '甲': '午',
    '乙': '巳',
    '丙': '酉',
    '丁': '申',
    '戊': '酉',
    '己': '申',
    '庚': '子',
    '辛': '亥',
    '壬': '卯',
    '癸': '寅'
  },
  
  // 桃花星（咸池）
  taohua: {
    '申子辰': '酉',
    '寅午戌': '卯',
    '巳酉丑': '午',
    '亥卯未': '子'
  },
  
  // 驿马星
  yima: {
    '申子辰': '寅',
    '寅午戌': '申',
    '巳酉丑': '亥',
    '亥卯未': '巳'
  },
  
  // 华盖星
  huagai: {
    '申子辰': '辰',
    '寅午戌': '戌',
    '巳酉丑': '丑',
    '亥卯未': '未'
  },
  
  // 羊刃
  yangblade: {
    '甲': '卯',
    '乙': '寅',
    '丙': '午',
    '丁': '巳',
    '戊': '午',
    '己': '巳',
    '庚': '酉',
    '辛': '申',
    '壬': '子',
    '癸': '亥'
  },
  
  // 空亡
  kongwang: {
    '甲子乙丑': ['戌', '亥'],
    '丙寅丁卯': ['子', '丑'],
    '戊辰己巳': ['寅', '卯'],
    '庚午辛未': ['辰', '巳'],
    '壬申癸酉': ['午', '未'],
    '甲戌乙亥': ['申', '酉']
  }
};

/**
 * 分析八字神煞
 * @param {Object} bazi - 八字信息
 * @returns {Object} 神煞分析结果
 */
function analyzeShensha(bazi) {
  const result = {
    tianyi: [],      // 天乙贵人
    wenchang: [],    // 文昌贵人
    wenqu: [],       // 文曲贵人
    taohua: [],      // 桃花星
    yima: [],        // 驿马星
    huagai: [],      // 华盖星
    yangblade: [],   // 羊刃
    kongwang: [],    // 空亡
    summary: []      // 总结
  };

  const dayGan = bazi.day.stem;
  const yearZhi = bazi.year.branch;
  const allZhi = [bazi.year.branch, bazi.month.branch, bazi.day.branch, bazi.hour.branch];
  const dayGanzhi = dayGan + bazi.day.branch;

  // 1. 天乙贵人
  const tianyiList = SHENSHA_DATA.tianyi[dayGan];
  if (tianyiList) {
    tianyiList.forEach(zhi => {
      allZhi.forEach((branch, index) => {
        if (branch === zhi) {
          const pillarName = ['年支', '月支', '日支', '时支'][index];
          result.tianyi.push({
            position: pillarName,
            branch: zhi,
            description: '逢凶化吉，遇难呈祥，得贵人相助'
          });
        }
      });
    });
  }

  // 2. 文昌贵人
  const wenchangZhi = SHENSHA_DATA.wenchang[dayGan];
  if (wenchangZhi) {
    allZhi.forEach((branch, index) => {
      if (branch === wenchangZhi) {
        const pillarName = ['年支', '月支', '日支', '时支'][index];
        result.wenchang.push({
          position: pillarName,
          branch: wenchangZhi,
          description: '主聪明好学，文章出众，利考试求学'
        });
      }
    });
  }

  // 3. 文曲贵人
  const wenquZhi = SHENSHA_DATA.wenqu[dayGan];
  if (wenquZhi) {
    allZhi.forEach((branch, index) => {
      if (branch === wenquZhi) {
        const pillarName = ['年支', '月支', '日支', '时支'][index];
        result.wenqu.push({
          position: pillarName,
          branch: wenquZhi,
          description: '主文思敏捷，口才出众，利文艺创作'
        });
      }
    });
  }

  // 4. 桃花星
  const taohuaKey = findTriadKey(yearZhi);
  if (taohuaKey && SHENSHA_DATA.taohua[taohuaKey]) {
    const taohuaZhi = SHENSHA_DATA.taohua[taohuaKey];
    allZhi.forEach((branch, index) => {
      if (branch === taohuaZhi) {
        const pillarName = ['年支', '月支', '日支', '时支'][index];
        result.taohua.push({
          position: pillarName,
          branch: taohuaZhi,
          description: '主异性缘佳，魅力出众，但需防桃花劫'
        });
      }
    });
  }

  // 5. 驿马星
  if (taohuaKey && SHENSHA_DATA.yima[taohuaKey]) {
    const yimaZhi = SHENSHA_DATA.yima[taohuaKey];
    allZhi.forEach((branch, index) => {
      if (branch === yimaZhi) {
        const pillarName = ['年支', '月支', '日支', '时支'][index];
        result.yima.push({
          position: pillarName,
          branch: yimaZhi,
          description: '主奔波劳碌，变动频繁，利外出发展'
        });
      }
    });
  }

  // 6. 华盖星
  if (taohuaKey && SHENSHA_DATA.huagai[taohuaKey]) {
    const huagaiZhi = SHENSHA_DATA.huagai[taohuaKey];
    allZhi.forEach((branch, index) => {
      if (branch === huagaiZhi) {
        const pillarName = ['年支', '月支', '日支', '时支'][index];
        result.huagai.push({
          position: pillarName,
          branch: huagaiZhi,
          description: '主聪明孤高，喜神秘文化，利宗教哲学'
        });
      }
    });
  }

  // 7. 羊刃
  const yangbladeZhi = SHENSHA_DATA.yangblade[dayGan];
  if (yangbladeZhi) {
    allZhi.forEach((branch, index) => {
      if (branch === yangbladeZhi) {
        const pillarName = ['年支', '月支', '日支', '时支'][index];
        result.yangblade.push({
          position: pillarName,
          branch: yangbladeZhi,
          description: '主刚强果断，但易冲动，需要制化'
        });
      }
    });
  }

  // 8. 空亡
  const kongwangBranches = getKongwang(dayGanzhi);
  if (kongwangBranches) {
    kongwangBranches.forEach(kwBranch => {
      allZhi.forEach((branch, index) => {
        if (branch === kwBranch) {
          const pillarName = ['年支', '月支', '日支', '时支'][index];
          result.kongwang.push({
            position: pillarName,
            branch: kwBranch,
            description: '主虚空不实，六亲缘薄，但利精神修养'
          });
        }
      });
    });
  }

  // 生成总结
  result.summary = generateShenshaSummary(result);

  return result;
}

/**
 * 查找三合局关键字
 */
function findTriadKey(yearZhi) {
  const triads = {
    '申': '申子辰',
    '子': '申子辰',
    '辰': '申子辰',
    '寅': '寅午戌',
    '午': '寅午戌',
    '戌': '寅午戌',
    '巳': '巳酉丑',
    '酉': '巳酉丑',
    '丑': '巳酉丑',
    '亥': '亥卯未',
    '卯': '亥卯未',
    '未': '亥卯未'
  };
  return triads[yearZhi];
}

/**
 * 获取空亡地支
 */
function getKongwang(dayGanzhi) {
  for (const [key, value] of Object.entries(SHENSHA_DATA.kongwang)) {
    if (key.includes(dayGanzhi)) {
      return value;
    }
  }
  return null;
}

/**
 * 生成神煞总结
 */
function generateShenshaSummary(shenshaResult) {
  const summary = [];
  
  if (shenshaResult.tianyi.length > 0) {
    summary.push(`天乙贵人在${shenshaResult.tianyi.map(t => t.position).join('、')}，一生多贵人相助`);
  }
  
  if (shenshaResult.wenchang.length > 0 || shenshaResult.wenqu.length > 0) {
    summary.push('命带文昌文曲，聪明好学，文思敏捷');
  }
  
  if (shenshaResult.taohua.length > 0) {
    summary.push('命带桃花，异性缘佳，魅力出众');
  }
  
  if (shenshaResult.yima.length > 0) {
    summary.push('命带驿马，一生多变动，利外出发展');
  }
  
  if (shenshaResult.huagai.length > 0) {
    summary.push('命带华盖，聪明孤高，喜神秘文化');
  }
  
  if (shenshaResult.yangblade.length > 0) {
    summary.push('命带羊刃，性格刚强，需要制化');
  }
  
  if (shenshaResult.kongwang.length > 0) {
    summary.push('命带空亡，六亲缘薄，但利精神修养');
  }
  
  if (summary.length === 0) {
    summary.push('命局神煞较少，平稳安康');
  }
  
  return summary;
}

module.exports = {
  analyzeShensha,
  SHENSHA_DATA
};
