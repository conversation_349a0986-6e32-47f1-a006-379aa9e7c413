// AI分析服务模块 - 集成DeepSeek API
const DEEPSEEK_API_KEY = '***********************************';
const DEEPSEEK_API_URL = 'https://api.deepseek.com/v1/chat/completions';

// 导入知识库模块
const { getRelevantKnowledge, searchKnowledge } = require('./knowledge-base.js');

// 导入专业提示词模板
const {
  MEIHUA_PROMPTS,
  YIJING_PROMPTS,
  BAZI_PROMPTS,
  ZIWEI_PROMPTS
} = require('./prompt-templates.js');

// 导入双重验证系统
const { performDualVerification, quickDualVerification } = require('./dual-verification-system.js');

/**
 * 调用DeepSeek API进行AI分析（深度优化版）
 * @param {string} prompt - 分析提示词
 * @param {object} context - 上下文信息（卦象、八字、紫微等）
 * @param {string} questionType - 问题类型
 * @param {string} moduleType - 模块类型
 * @returns {Promise<string>} AI分析结果
 */
async function callDeepSeekAPI(prompt, context = {}, questionType = '', moduleType = '', retryCount = 0) {
  const maxRetries = 3; // 最多重试3次

  try {
    // 构建系统提示词
    const systemPrompt = buildSystemPrompt(questionType, moduleType);

    // 构建用户提示词
    const userPrompt = await buildUserPrompt(prompt, context, moduleType);

    console.log('AI分析请求:', { questionType, moduleType, promptLength: userPrompt.length, retryCount });

    const response = await new Promise((resolve, reject) => {
      wx.request({
        url: DEEPSEEK_API_URL,
        method: 'POST',
        header: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${DEEPSEEK_API_KEY}`
        },
        data: {
          model: 'deepseek-chat',
          messages: [
            {
              role: 'system',
              content: systemPrompt
            },
            {
              role: 'user',
              content: userPrompt
            }
          ],
          temperature: 0.3, // 降低温度以提高准确性
          max_tokens: 8192, // DeepSeek Chat模型的最大token限制
          stream: false
        },
        timeout: 300000, // 设置超时时间为5分钟
        success: resolve,
        fail: reject
      });
    });

    if (response.statusCode === 200 && response.data.choices && response.data.choices.length > 0) {
      const aiResult = response.data.choices[0].message.content;
      console.log('AI分析成功:', { resultLength: aiResult.length });

      // 进行结果验证
      const validatedResult = await validateAIResult(aiResult, context, questionType);
      return validatedResult;
    } else {
      console.error('DeepSeek API调用失败:', response);
      console.error('🚨 错误详情:', response.data);
      console.error('🚨 请求数据长度:', JSON.stringify({
        systemPromptLength: systemPrompt?.length || 0,
        userPromptLength: userPrompt?.length || 0,
        totalLength: (systemPrompt?.length || 0) + (userPrompt?.length || 0)
      }));
      return generateFallbackResponse(questionType, context);
    }
  } catch (error) {
    console.error('DeepSeek API调用错误:', error);

    // 如果是超时错误且还有重试次数，则重试
    if (error.errMsg && error.errMsg.includes('timeout') && retryCount < maxRetries) {
      const waitTime = (retryCount + 1) * 3000; // 递增等待时间：3秒、6秒、9秒
      console.log(`🔄 网络超时，正在进行第${retryCount + 1}次重试...等待${waitTime/1000}秒`);
      await new Promise(resolve => setTimeout(resolve, waitTime));
      return await callDeepSeekAPI(prompt, context, questionType, moduleType, retryCount + 1);
    }

    return generateFallbackResponse(questionType, context);
  }
}

/**
 * 构建系统提示词（专业模板版）
 * @param {string} questionType - 问题类型
 * @param {string} moduleType - 模块类型（meihua, yijing, bazi, ziwei）
 * @returns {string} 系统提示词
 */
function buildSystemPrompt(questionType, moduleType = '') {
  console.log('🔍🔍🔍 [V2] 构建系统提示词，moduleType:', moduleType);
  console.log('🔍🔍🔍 [V2] BAZI_PROMPTS存在:', !!BAZI_PROMPTS);
  console.log('🔍🔍🔍 [V2] BAZI_PROMPTS.system长度:', BAZI_PROMPTS?.system?.length || 0);

  // 根据模块类型选择专业提示词模板
  const promptTemplates = {
    'meihua': MEIHUA_PROMPTS,
    'yijing': YIJING_PROMPTS,
    'bazi': BAZI_PROMPTS,
    'bazi_dual': BAZI_PROMPTS, // 双重验证八字也使用八字提示词
    'ziwei': ZIWEI_PROMPTS,
    'ziwei_dual': ZIWEI_PROMPTS // 双重验证紫微也使用紫微提示词
  };

  const template = promptTemplates[moduleType];
  if (!template) {
    console.warn('🚨 未找到专业提示词模板，降级到通用提示词，moduleType:', moduleType);
    return buildGenericSystemPrompt(questionType, moduleType);
  }

  console.log('✅ 使用专业提示词模板:', moduleType);

  // 构建专业系统提示词
  const systemPrompt = template.system;
  const userSpecific = template.user(questionType);
  const finalPrompt = `${systemPrompt}\n\n${userSpecific}`;

  console.log('🔍 系统提示词长度:', finalPrompt.length);
  console.log('🔍 系统提示词前100字符:', finalPrompt.substring(0, 100));

  return finalPrompt;
}

/**
 * 构建通用系统提示词（降级方案）
 * @param {string} questionType - 问题类型
 * @param {string} moduleType - 模块类型
 * @returns {string} 通用系统提示词
 */
function buildGenericSystemPrompt(questionType, moduleType) {
  const basePrompt = `你是一位精通中国传统命理学的大师，拥有深厚的易经、八字、紫微斗数知识。

【核心要求】
1. 严格基于传统古籍理论，每个判断都要有古籍依据
2. 提供具体的时间预测（精确到年月日，农历和公历并列）
3. 给出实用的建议和化解方法
4. 语言通俗易懂，让现代人能够理解
5. 保持最高准确性和专业性，绝不臆测

【分析框架】
- 【古籍理论依据】：引用具体古籍条文
- 【卦象/命理含义】：详细解释象征意义
- 【精准时间预测】：具体时间节点（提供2-3个可能时间）
- 【具体事件预测】：详细描述可能发生的事件
- 【实用建议】：可操作的具体建议
- 【化解方法】：传统化解方式（如适用）
- 【注意事项】：需要特别关注的方面`;

  const moduleSpecificPrompts = {
    'meihua': `
【梅花易数专业要求】
- 严格按照《梅花易数》原著理论分析
- 重点分析体卦、用卦、互卦、变卦的关系
- 考虑卦气旺衰和时间因素
- 运用八卦万物类象进行具体推断`,

    'yijing': `
【周易六爻专业要求】
- 严格按照《增删卜易》《卜筮正宗》等经典理论
- 重点分析世爻、应爻、用神的旺衰
- 考虑六神、六亲的作用关系
- 分析动爻对静爻的影响`,

    'bazi': `
【子平八字专业要求】
- 严格按照《子平真诠》《滴天髓》等经典理论
- 重点分析日主强弱和用神喜忌
- 考虑大运流年的作用关系
- 分析十神格局的高低`,

    'ziwei': `
【紫微斗数专业要求】
- 严格按照《紫微斗数全书》等经典理论
- 重点分析命宫、身宫和相关宫位
- 考虑十四主星和四化的作用
- 分析三方四正的格局组合`
  };

  const typeSpecificPrompts = {
    '财运': '特别关注财星、妻财爻的分析，提供投资时机和收益预测，必须给出具体的盈利时间和金额范围',
    '事业': '重点分析官星、官鬼爻，提供升职跳槽的具体时间，必须预测职位变化的具体时间节点',
    '婚姻': '专注配偶星、夫妻宫的分析，预测结婚时机和对象特征，必须给出具体的相遇和结婚时间',
    '健康': '分析疾病相关的六亲和宫位，提供康复时间和预防建议，必须给出病情转折的具体时间',
    '学业': '重点看父母爻、文昌星，提供考试运势和学习建议，必须预测考试结果和录取时间'
  };

  const modulePrompt = moduleSpecificPrompts[moduleType] || '';
  const specificPrompt = typeSpecificPrompts[questionType] || '根据问题类型进行针对性分析';

  return `${basePrompt}${modulePrompt}\n\n【针对${questionType}问题的特殊要求】\n${specificPrompt}`;
}

/**
 * 构建用户提示词（深度集成知识库检索）
 * @param {string} question - 用户问题
 * @param {object} context - 上下文信息
 * @param {string} moduleType - 模块类型
 * @returns {Promise<string>} 用户提示词
 */
async function buildUserPrompt(question, context, moduleType = '') {
  console.log('🔍 构建用户提示词，context数据:', context);
  console.log('🔍 context.bazi存在:', !!context.bazi);
  console.log('🔍 context.tenGodsDistribution存在:', !!context.tenGodsDistribution);
  console.log('🔍 moduleType:', moduleType);

  let prompt = `【用户问题】${question}\n\n`;

  // 添加卦象信息
  if (context.hexagram) {
    // 处理不同的卦象数据结构
    let hexagramInfo = '';

    if (moduleType === 'meihua' && context.hexagram.upper && context.hexagram.lower) {
      // 梅花易数数据结构
      hexagramInfo = `【梅花易数卦象信息】
- 上卦：${context.hexagram.upper.name}（${context.hexagram.upper.symbol}）
- 下卦：${context.hexagram.lower.name}（${context.hexagram.lower.symbol}）
- 动爻：第${context.hexagram.change}爻
- 体卦：${context.hexagram.body?.name || ''}
- 用卦：${context.hexagram.use?.name || ''}
- 互卦：${context.hexagram.mutual?.name || ''}
- 变卦：${context.hexagram.changed?.name || ''}
- 起卦方式：${context.hexagram.method || ''}
- 起卦时间：${context.hexagram.time || context.hexagram.date || ''}
`;
    } else if (moduleType === 'yijing' || context.hexagram.liuyaoInfo) {
      // 六爻数据结构
      const liuyao = context.liuyaoInfo || context.hexagram.liuyaoInfo || {};
      hexagramInfo = `【六爻卦象信息】
- 卦名：${context.hexagram.name || ''}
- 卦象：${context.hexagram.symbol || ''}
- 动爻：${context.hexagram.changingYaos ? `第${context.hexagram.changingYaos.join('、')}爻` : '无'}
- 世爻：${liuyao.worldYao || ''}
- 应爻：${liuyao.responseYao || ''}
- 用神：${liuyao.usefulGod || ''}
- 六亲：${liuyao.sixRelatives ? liuyao.sixRelatives.join('、') : ''}
- 起卦方式：${context.hexagram.method || ''}
- 起卦时间：${context.hexagram.date || ''}
`;
    } else {
      // 其他数据结构
      hexagramInfo = `【卦象信息】
- 卦名：${context.hexagram.name || ''}
- 卦象：${context.hexagram.symbol || ''}
- 上卦：${context.hexagram.upper || ''}
- 下卦：${context.hexagram.lower || ''}
- 动爻：${context.hexagram.changingLines ? context.hexagram.changingLines.join('、') : '无'}
- 变卦：${context.hexagram.changed || ''}
- 起卦方式：${context.hexagram.method || ''}
- 起卦时间：${context.hexagram.date || ''}
`;
    }

    prompt += hexagramInfo;
  }

  // 添加八字详细信息（参考详批命书格式）
  if (context.bazi) {
    prompt += `【八字详细信息】

【基础四柱】
- 年柱：${context.bazi.year}（${context.bazi.yearElement || ''}）
- 月柱：${context.bazi.month}（${context.bazi.monthElement || ''}）
- 日柱：${context.bazi.day}（${context.bazi.dayElement || ''}）
- 时柱：${context.bazi.hour}（${context.bazi.hourElement || ''}）

【命主信息】
- 日主：${context.bazi.dayMaster}（${context.bazi.dayMasterElement}）
- 日主强弱：${context.bazi.strength || ''}
- 格局：${context.bazi.pattern || ''}
- 用神：${context.bazi.useGod || ''}
- 喜神：${context.bazi.favorableGod || ''}
- 忌神：${context.bazi.unfavorableGod || ''}

【十神分布】
${context.tenGodsDistribution && Object.keys(context.tenGodsDistribution).length > 0 ?
  Object.keys(context.tenGodsDistribution).map(god =>
    context.tenGodsDistribution[god] && context.tenGodsDistribution[god].length > 0 ?
    `- ${god}：${context.tenGodsDistribution[god].join('、')}` : `- ${god}：无`
  ).join('\n') :
  `- 正官：${context.bazi && context.bazi.zhengGuan || '无'}
- 七杀：${context.bazi && context.bazi.qiSha || '无'}
- 正财：${context.bazi && context.bazi.zhengCai || '无'}
- 偏财：${context.bazi && context.bazi.pianCai || '无'}
- 正印：${context.bazi && context.bazi.zhengYin || '无'}
- 偏印：${context.bazi && context.bazi.pianYin || '无'}
- 食神：${context.bazi && context.bazi.shiShen || '无'}
- 伤官：${context.bazi && context.bazi.shangGuan || '无'}
- 比肩：${context.bazi && context.bazi.biJian || '无'}
- 劫财：${context.bazi && context.bazi.jieCai || '无'}`}

【五行分析】
${context.wuxingData ? `- 金：${context.wuxingData.金 || 0}个
- 木：${context.wuxingData.木 || 0}个
- 水：${context.wuxingData.水 || 0}个
- 火：${context.wuxingData.火 || 0}个
- 土：${context.wuxingData.土 || 0}个
- 五行特点：${context.wuxingAnalysis || ''}` : '- 五行分析计算中...'}

【大运流年】
- 当前大运：${context.currentDayun ? `${context.currentDayun.ganzhi}运（${context.currentDayun.startAge}-${context.currentDayun.endAge}岁）` : '计算中...'}
- 当前流年：${context.currentLiunian ? `${context.currentLiunian.ganzhi}年` : '计算中...'}
- 起运年龄：${context.startLuckAge || ''}岁

【神煞信息】
${context.shenshaData ? Object.keys(context.shenshaData).map(shensha =>
  `- ${shensha}：${context.shenshaData[shensha] ? '有' : '无'}`
).join('\n') : '- 神煞分析计算中...'}

【传统信息】
${context.traditionalInfo ? `- 纳音：${context.traditionalInfo.nayin || ''}
- 生肖：${context.traditionalInfo.zodiac || ''}
- 星座：${context.traditionalInfo.constellation || ''}` : ''}

【出生信息】
${context.birthInfo ? `- 出生时间：${context.birthInfo.year}年${context.birthInfo.month}月${context.birthInfo.day}日${context.birthInfo.hour}时${context.birthInfo.minute}分
- 性别：${context.birthInfo.isMale ? '男' : '女'}
- 当前年龄：${new Date().getFullYear() - context.birthInfo.year}岁` : ''}

【问题类型】
- 咨询类型：${context.questionType || '综合'}

`;
  }

  // 添加紫微斗数信息
  if (context.ziwei) {
    prompt += `紫微斗数信息：
- 命宫主星：${context.ziwei.mingGong || ''}
- 财帛宫：${context.ziwei.caibogong || ''}
- 官禄宫：${context.ziwei.guanlugong || ''}
- 夫妻宫：${context.ziwei.fuqigong || ''}
- 格局：${context.ziwei.pattern || ''}
`;
  }

  // 添加用户回答信息（预分析对话收集的信息）
  if (context.userResponses && context.userResponses.length > 0) {
    prompt += `\n【用户补充信息】\n`;
    context.userResponses.forEach((response, index) => {
      prompt += `${index + 1}. ${response.question}: ${response.answer}\n`;
    });
    prompt += '\n';
  }

  // 深度检索相关知识库内容
  try {
    const questionType = detectQuestionType(question);
    const relevantKnowledge = await getRelevantKnowledge(questionType, question, context);

    if (relevantKnowledge && relevantKnowledge.length > 0) {
      prompt += `\n【相关古籍依据】\n`;
      for (const knowledge of relevantKnowledge) {
        prompt += `\n《${knowledge.title}》${knowledge.author ? `·${knowledge.author}著` : ''}：\n`;

        // 添加语义相关性信息
        if (knowledge.semanticScore) {
          prompt += `[相关性: ${knowledge.semanticScore}/100] `;
        }
        if (knowledge.relevanceReason) {
          prompt += `[${knowledge.relevanceReason}]\n`;
        }

        // 添加匹配的内容片段
        if (knowledge.content) {
          const contentLines = knowledge.content.split('\n').filter(line => line.trim().length > 10);
          for (const line of contentLines.slice(0, 3)) {
            prompt += `- ${line.trim()}\n`;
          }
        }

        // 添加匹配的句子（如果有）
        if (knowledge.matchedSnippets && knowledge.matchedSnippets.length > 0) {
          for (const snippet of knowledge.matchedSnippets.slice(0, 2)) {
            if (snippet.trim().length > 10) {
              prompt += `- ${snippet.trim()}\n`;
            }
          }
        }
        prompt += '\n';
      }
    } else {
      prompt += `\n【注意】未找到直接相关的古籍依据，请基于传统理论进行分析。\n`;
    }
  } catch (error) {
    console.warn('知识库检索失败:', error);
    prompt += `\n【注意】知识库检索失败，请基于传统理论进行分析。\n`;
  }

  prompt += `\n【分析要求】
请严格基于以上信息和古籍依据，结合传统命理学理论，为用户提供详细的分析和建议。
特别注意：
1. 每个判断都要有古籍理论支撑
2. 时间预测要具体到年月日
3. 建议要实用可操作
4. 如果古籍依据不足，请明确说明并谨慎推断`;

  return prompt;
}

/**
 * AI结果验证
 * @param {string} aiResult - AI分析结果
 * @param {object} context - 上下文信息
 * @param {string} questionType - 问题类型
 * @returns {Promise<string>} 验证后的结果
 */
async function validateAIResult(aiResult, context, questionType) {
  try {
    // 根据模块类型检查不同的分析框架
    let requiredSections = [];

    if (context.moduleType === 'bazi' || context.moduleType === 'bazi_dual') {
      // 八字分析必要框架
      requiredSections = ['基础命盘', '日主', '格局', '大运', '预测'];
    } else if (context.moduleType === 'ziwei' || context.moduleType === 'ziwei_dual') {
      // 紫微斗数分析必要框架
      requiredSections = ['命宫', '财帛', '官禄', '夫妻', '预测'];
    } else {
      // 六爻梅花易数分析框架
      requiredSections = ['古籍理论依据', '卦象', '命理含义', '时间预测', '建议'];
    }

    const missingSections = requiredSections.filter(section =>
      !aiResult.includes(section) && !aiResult.includes(section.replace('/', ''))
    );

    // 降低验证标准，允许更灵活的分析框架
    if (missingSections.length > 4) {
      console.warn('AI结果缺少必要分析框架:', missingSections);
      return aiResult + '\n\n【系统提示】本次分析可能不够完整，建议重新咨询或参考传统理论。';
    }

    // 检查字数是否达到专业标准（至少5000字）
    if (aiResult.length < 3000) {
      console.warn('AI分析字数不足，当前字数:', aiResult.length);
      return aiResult + '\n\n【系统提示】本次分析深度可能不够，建议重新咨询以获得更详细的专业分析。';
    }

    return aiResult;
  } catch (error) {
    console.warn('AI结果验证失败:', error);
    return aiResult;
  }
}

/**
 * 生成降级响应
 * @param {string} questionType - 问题类型
 * @param {object} context - 上下文信息
 * @returns {string} 降级响应
 */
function generateFallbackResponse(questionType, context) {
  // 尝试提供基于卦象的基础分析
  let basicAnalysis = '';

  if (context.hexagram && context.hexagram.upper && context.hexagram.lower) {
    const upperName = context.hexagram.upper.name || '';
    const lowerName = context.hexagram.lower.name || '';
    const changeLine = context.hexagram.change || '';

    basicAnalysis = `\n\n【基础卦象信息】\n上卦：${upperName}\n下卦：${lowerName}\n动爻：第${changeLine}爻\n\n根据传统梅花易数理论，此卦象需要结合体用关系进行深入分析。建议您稍后重试获取完整的AI分析。`;
  }

  const fallbackResponses = {
    '财运': `抱歉，AI分析服务暂时不可用。根据传统理论，财运分析需要结合具体的卦象信息和时间因素。${basicAnalysis}`,
    '事业': `抱歉，AI分析服务暂时不可用。事业运势的分析需要综合考虑卦象的体用关系。${basicAnalysis}`,
    '婚姻': `抱歉，AI分析服务暂时不可用。婚姻感情的分析涉及复杂的卦象关系。${basicAnalysis}`,
    '健康': `抱歉，AI分析服务暂时不可用。健康相关的预测需要谨慎对待。${basicAnalysis}`,
    '学业': `抱歉，AI分析服务暂时不可用。学业运势需要结合个人努力和卦象指引。${basicAnalysis}`,
    '综合': `抱歉，AI分析服务暂时不可用。${basicAnalysis}`
  };

  return fallbackResponses[questionType] || `抱歉，AI分析服务暂时不可用，请稍后再试。${basicAnalysis}`;
}

/**
 * 梅花易数AI分析（深度优化版）
 * @param {string} question - 用户问题
 * @param {object} hexagram - 卦象信息
 * @param {object} additionalContext - 额外上下文信息（用户回答等）
 * @returns {Promise<string>} AI分析结果
 */
async function analyzeMeihuaWithAI(question, hexagram, additionalContext = {}) {
  // 确保question是字符串类型
  if (typeof question !== 'string') {
    console.warn('⚠️ analyzeMeihuaWithAI: question参数不是字符串类型:', typeof question, question);
    question = String(question || '综合分析');
  }

  const context = {
    hexagram,
    ...additionalContext
  };
  const questionType = detectQuestionType(question);

  // 调试日志：检查传递给AI的卦象数据
  console.log('🔍 analyzeMeihuaWithAI 接收到的数据:');
  console.log('📝 问题:', question);
  console.log('🎯 卦象数据:', hexagram);
  console.log('📊 上下卦信息:', {
    upper: hexagram?.upper,
    lower: hexagram?.lower,
    change: hexagram?.change
  });

  const prompt = `请基于梅花易数理论深度分析以下卦象，严格按照《梅花易数》原著进行推断：`;

  return await callDeepSeekAPI(prompt, context, questionType, 'meihua');
}

/**
 * 周易六爻AI分析（深度优化版）
 * @param {string} question - 用户问题
 * @param {object} hexagram - 卦象信息
 * @returns {Promise<string>} AI分析结果
 */
async function analyzeYijingWithAI(question, context = {}) {
  // 支持旧的调用方式：analyzeYijingWithAI(question, hexagram)
  if (context && !context.hexagram && context.name) {
    context = { hexagram: context };
  }

  const questionType = detectQuestionType(question);

  const prompt = `请基于周易六爻理论深度分析以下卦象，严格按照《增删卜易》《卜筮正宗》等经典进行推断：`;

  return await callDeepSeekAPI(prompt, context, questionType, 'yijing');
}

/**
 * 八字AI分析（深度优化版 + 双重验证）
 * @param {string} question - 用户问题
 * @param {object} bazi - 八字信息
 * @param {object} birthInfo - 出生信息（用于终身卦计算）
 * @param {object} currentInfo - 当前信息（年龄、年份等）
 * @returns {Promise<string>} AI分析结果
 */
async function analyzeBaziWithAI(question, bazi, birthInfo = null, currentInfo = null) {
  let context = { bazi };
  let enhancedAnalysis = '';

  // 如果提供了出生信息，启用双重验证
  if (birthInfo && currentInfo) {
    console.log('🔄 启用八字双重验证分析...');
    try {
      const dualResult = performDualVerification(bazi, birthInfo, question, currentInfo);
      if (dualResult.success) {
        context.dualVerification = dualResult.dualVerification;
        enhancedAnalysis = dualResult.userResult;
        console.log('✅ 双重验证完成，一致性:', dualResult.dualVerification.consistency.consistency);
      }
    } catch (error) {
      console.warn('⚠️ 双重验证失败，使用标准分析:', error.message);
    }
  }

  const questionType = detectQuestionType(question);

  let prompt = `请基于子平八字理论深度分析以下命盘，严格按照《子平真诠》《滴天髓》等经典进行推断：

【重要要求】
1. 必须提供具体的时间预测，包括过去验证和未来预测
2. 时间预测格式：XXXX年X季，概率XX%
3. 先分析过去2-3个关键时间点进行验证
4. 再预测未来2-3个重要时间节点
5. 每个预测都要说明依据的古籍理论

【分析框架】
- 【过去验证】：根据大运流年分析过去的关键事件时间
- 【未来预测】：基于知识库预测具体的时间节点
- 【理论依据】：引用具体的古籍条文和计算方法
- 【调理建议】：提供可操作的具体建议`;

  // 如果有双重验证结果，加入提示
  if (enhancedAnalysis) {
    prompt += `

【双重验证结果】
以下是基于八字+终身卦双重验证的分析结果，请在此基础上进行深度解读：
${enhancedAnalysis}

请结合上述双重验证结果，提供更精准的分析和时间预测。`;
  }

  const aiResult = await callDeepSeekAPI(prompt, context, questionType, 'bazi');

  // 如果有双重验证结果，将其整合到最终结果中
  if (enhancedAnalysis) {
    return `【双重验证分析结果】

${enhancedAnalysis}

【AI深度解读】

${aiResult}`;
  }

  return aiResult;
}

/**
 * 紫微斗数AI分析（深度优化版）
 * @param {string} question - 用户问题
 * @param {object} ziwei - 紫微斗数信息
 * @returns {Promise<string>} AI分析结果
 */
async function analyzeZiweiWithAI(question, ziwei) {
  const context = { ziwei };
  const questionType = detectQuestionType(question);

  const prompt = `请基于紫微斗数理论深度分析以下命盘，严格按照《紫微斗数全书》等经典进行推断：`;

  return await callDeepSeekAPI(prompt, context, questionType, 'ziwei');
}

/**
 * 紫微斗数双重验证AI分析（高精度版）
 * @param {string} question - 用户问题
 * @param {object} ziwei - 紫微斗数信息
 * @param {object} birthInfo - 出生信息
 * @param {object} currentInfo - 当前信息
 * @returns {Promise<string>} AI分析结果
 */
async function analyzeZiweiWithDualVerification(question, ziwei, birthInfo, currentInfo) {
  try {
    console.log('🔮 启动紫微斗数双重验证AI分析...');

    // 导入双重验证系统
    const { performZiweiDualVerification } = require('./ziwei-dual-verification.js');

    // 执行双重验证
    const dualResult = performZiweiDualVerification(ziwei, birthInfo, question, currentInfo);

    if (!dualResult.success) {
      console.log('⚠️ 双重验证失败，使用标准分析');
      return await analyzeZiweiWithAI(question, ziwei);
    }

    console.log('✅ 双重验证成功，一致性得分:', dualResult.dualVerification.consistency.consistency);

    // 构建高精度分析提示词
    const prompt = `作为专业的紫微斗数命理分析师，请基于以下双重验证结果进行深度分析：

【分析要求】
1. 严格依据《紫微斗数全书》《斗数宣微》《梅花易数》等古籍理论
2. 禁止假设和胡编乱造，每个判断都要有理论依据
3. 必须按照"先过去验证，再未来预测"的格式
4. 时间预测要具体到年份季节，并提供概率百分比
5. 基于知识库资料给出职业建议（当官/创业/上班等）

【双重验证数据】
紫微斗数分析：${JSON.stringify(dualResult.dualVerification.ziweiAnalysis, null, 2)}
终身卦分析：${JSON.stringify(dualResult.dualVerification.lifeHexagram.analysis, null, 2)}
一致性验证：${dualResult.dualVerification.consistency.analysis}（得分${dualResult.dualVerification.consistency.consistency}分）
时间预测：${JSON.stringify(dualResult.dualVerification.timePrediction, null, 2)}

【用户问题】
${question}

【分析格式要求】
请严格按照以下格式输出：

【命理特征】
根据"紫微斗数全书"资料显示，您的命宫主星为XXX，五行局为XXX，更适合XXX（当官/创业/上班等具体建议）。

【过去验证】（基于大限流年分析）
20XX年X季：XXX事件（基于XXX理论）
20XX年X季：XXX事件（基于XXX理论）
20XX年X季：XXX事件（基于XXX理论）

【未来预测】（基于双重验证结果）
20XX年X季有XX%的概率XXX（基于XXX星曜+终身卦XXX分析）
20XX年X季有XX%的概率XXX（基于XXX理论+卦象变化）
20XX年X季有XX%的概率XXX（基于XXX宫位+流年运势）

【调理建议】
基于紫微斗数理论的具体调理方法和注意事项。

请确保每个预测都有具体的理论依据，不得凭空假设。`;

    // 调用AI分析
    const aiAnalysis = await callDeepSeekAPI(
      prompt,
      {
        ziwei: ziwei,
        dualVerification: dualResult.dualVerification
      },
      detectQuestionType(question),
      'ziwei_dual'
    );

    console.log('✅ 紫微斗数双重验证AI分析完成');
    return aiAnalysis;

  } catch (error) {
    console.error('❌ 紫微斗数双重验证AI分析错误:', error);
    // 降级到标准分析
    return await analyzeZiweiWithAI(question, ziwei);
  }
}

/**
 * 检测问题类型
 * @param {string} question - 用户问题
 * @returns {string} 问题类型
 */
function detectQuestionType(question) {
  // 确保question是字符串类型
  if (typeof question !== 'string') {
    console.warn('⚠️ detectQuestionType: question参数不是字符串类型:', typeof question, question);
    return '综合';
  }
  const keywords = {
    '财运': ['财运', '赚钱', '投资', '理财', '收入', '财富', '经济'],
    '事业': ['工作', '事业', '升职', '跳槽', '职业', '升迁', '当官'],
    '婚姻': ['婚姻', '结婚', '感情', '恋爱', '配偶', '对象', '桃花'],
    '健康': ['健康', '疾病', '身体', '病情', '康复', '医疗'],
    '学业': ['学习', '考试', '学业', '读书', '升学', '文凭']
  };
  
  for (const [type, words] of Object.entries(keywords)) {
    if (words.some(word => question.includes(word))) {
      return type;
    }
  }
  
  return '综合';
}

/**
 * 双重验证AI分析（专用于高精度分析）
 * @param {string} question - 用户问题
 * @param {object} bazi - 八字信息
 * @param {object} birthInfo - 出生信息
 * @param {object} currentInfo - 当前信息
 * @returns {Promise<object>} 双重验证分析结果
 */
async function analyzeBaziWithDualVerification(question, bazi, birthInfo, currentInfo) {
  console.log('🚀 开始双重验证AI分析...');

  try {
    // 执行双重验证
    const dualResult = performDualVerification(bazi, birthInfo, question, currentInfo);

    if (!dualResult.success) {
      throw new Error('双重验证失败: ' + dualResult.error);
    }

    // 构建专业的AI分析提示词
    const prompt = `作为专业的命理分析师，请基于以下双重验证结果进行深度分析：

【八字信息】
日主：${bazi.day.stem}${bazi.day.branch}
四柱：${bazi.year.stem}${bazi.year.branch} ${bazi.month.stem}${bazi.month.branch} ${bazi.day.stem}${bazi.day.branch} ${bazi.hour.stem}${bazi.hour.branch}

【终身卦信息】
卦名：${dualResult.dualVerification.lifeHexagram.originalHexagram.name}
卦象分析：${dualResult.dualVerification.lifeHexagram.analysis.lifeTheme}

【一致性验证】
验证得分：${dualResult.dualVerification.consistency.consistency}分
验证说明：${dualResult.dualVerification.consistency.analysis}

【时间预测】
${dualResult.dualVerification.timePrediction.summary}

【用户问题】
${question}

【分析要求】
1. 必须严格基于知识库理论，引用具体古籍条文
2. 提供精确的时间预测，格式：XXXX年X季，概率XX%
3. 先验证过去，再预测未来，每个时间点都要说明理论依据
4. 分析必须具体到某个事件，不能模糊表述
5. 提供可操作的调理建议

请按照以下格式输出：

【过去验证】
（分析过去2-3个关键时间点，用于验证分析准确性）

【未来预测】
（预测未来2-3个重要时间节点，每个都要有概率和理论依据）

【理论依据】
（引用的具体古籍理论和计算方法）

【调理建议】
（基于分析结果的具体可操作建议）`;

    // 调用AI分析
    const aiAnalysis = await callDeepSeekAPI(prompt, {
      bazi: bazi,
      dualVerification: dualResult.dualVerification
    }, detectQuestionType(question), 'bazi_dual');

    return {
      success: true,
      dualVerificationResult: dualResult,
      aiAnalysis: aiAnalysis,
      confidence: dualResult.dualVerification.confidence,
      summary: dualResult.userResult,
      timestamp: new Date().toISOString()
    };

  } catch (error) {
    console.error('❌ 双重验证AI分析失败:', error);
    return {
      success: false,
      error: error.message,
      fallbackAnalysis: await analyzeBaziWithAI(question, bazi)
    };
  }
}

module.exports = {
  callDeepSeekAPI,
  analyzeMeihuaWithAI,
  analyzeYijingWithAI,
  analyzeBaziWithAI,
  analyzeBaziWithDualVerification,
  analyzeZiweiWithAI,
  analyzeZiweiWithDualVerification,
  detectQuestionType,
  validateAIResult,
  generateFallbackResponse,
  buildSystemPrompt,
  buildUserPrompt
};
