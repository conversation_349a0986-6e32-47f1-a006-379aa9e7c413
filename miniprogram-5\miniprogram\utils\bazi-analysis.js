// 八字十神分析系统
// 基于传统子平八字理论

const { FIVE_ELEMENTS, YIN_YANG } = require('./bazi-calculator.js');
const { analyzeShensha } = require('./bazi-shensha.js');

// 十神定义
const TEN_GODS = {
  '比肩': { nature: '中性', meaning: '兄弟朋友、竞争、自我' },
  '劫财': { nature: '凶', meaning: '破财、小人、争夺' },
  '食神': { nature: '吉', meaning: '才华、享受、子女' },
  '伤官': { nature: '凶', meaning: '聪明、叛逆、伤害' },
  '偏财': { nature: '吉', meaning: '横财、父亲、情人' },
  '正财': { nature: '吉', meaning: '正当收入、妻子、财富' },
  '七杀': { nature: '凶', meaning: '压力、小人、疾病' },
  '正官': { nature: '吉', meaning: '地位、名声、丈夫' },
  '偏印': { nature: '凶', meaning: '偏业、继母、孤独' },
  '正印': { nature: '吉', meaning: '学问、母亲、贵人' }
};

// 五行生克关系
const FIVE_ELEMENTS_RELATION = {
  '木': { generates: '火', destroys: '土', generatedBy: '水', destroyedBy: '金' },
  '火': { generates: '土', destroys: '金', generatedBy: '木', destroyedBy: '水' },
  '土': { generates: '金', destroys: '水', generatedBy: '火', destroyedBy: '木' },
  '金': { generates: '水', destroys: '木', generatedBy: '土', destroyedBy: '火' },
  '水': { generates: '木', destroys: '火', generatedBy: '金', destroyedBy: '土' }
};

// 地支本气天干对照表
const BRANCH_MAIN_STEM = {
  '子': '癸', '丑': '己', '寅': '甲', '卯': '乙',
  '辰': '戊', '巳': '丙', '午': '丁', '未': '己',
  '申': '庚', '酉': '辛', '戌': '戊', '亥': '壬'
};

/**
 * 计算十神关系
 * @param {string} dayMaster - 日主天干
 * @param {string} targetStem - 目标天干
 * @returns {string} 十神名称
 */
function calculateTenGod(dayMaster, targetStem) {
  // 精确的十神对照表（基于传统子平八字理论）
  const tenGodTable = {
    '甲': { '甲': '比肩', '乙': '劫财', '丙': '食神', '丁': '伤官', '戊': '偏财', '己': '正财', '庚': '七杀', '辛': '正官', '壬': '偏印', '癸': '正印' },
    '乙': { '乙': '比肩', '甲': '劫财', '丁': '食神', '丙': '伤官', '己': '偏财', '戊': '正财', '辛': '七杀', '庚': '正官', '癸': '偏印', '壬': '正印' },
    '丙': { '丙': '比肩', '丁': '劫财', '戊': '食神', '己': '伤官', '庚': '偏财', '辛': '正财', '壬': '七杀', '癸': '正官', '甲': '偏印', '乙': '正印' },
    '丁': { '丁': '比肩', '丙': '劫财', '己': '食神', '戊': '伤官', '辛': '偏财', '庚': '正财', '癸': '七杀', '壬': '正官', '乙': '偏印', '甲': '正印' },
    '戊': { '戊': '比肩', '己': '劫财', '庚': '食神', '辛': '伤官', '壬': '偏财', '癸': '正财', '甲': '七杀', '乙': '正官', '丙': '偏印', '丁': '正印' },
    '己': { '己': '比肩', '戊': '劫财', '辛': '食神', '庚': '伤官', '癸': '偏财', '壬': '正财', '乙': '七杀', '甲': '正官', '丁': '偏印', '丙': '正印' },
    '庚': { '庚': '比肩', '辛': '劫财', '壬': '食神', '癸': '伤官', '甲': '偏财', '乙': '正财', '丙': '七杀', '丁': '正官', '戊': '偏印', '己': '正印' },
    '辛': { '辛': '比肩', '庚': '劫财', '癸': '食神', '壬': '伤官', '乙': '偏财', '甲': '正财', '丁': '七杀', '丙': '正官', '己': '偏印', '戊': '正印' },
    '壬': { '壬': '比肩', '癸': '劫财', '甲': '食神', '乙': '伤官', '丙': '偏财', '丁': '正财', '戊': '七杀', '己': '正官', '庚': '偏印', '辛': '正印' },
    '癸': { '癸': '比肩', '壬': '劫财', '乙': '食神', '甲': '伤官', '丁': '偏财', '丙': '正财', '己': '七杀', '戊': '正官', '辛': '偏印', '庚': '正印' }
  };

  // 验证输入参数
  if (!tenGodTable[dayMaster] || !tenGodTable[dayMaster][targetStem]) {
    console.error('十神计算参数错误:', { dayMaster, targetStem });
    return '比肩';
  }

  return tenGodTable[dayMaster][targetStem];
}

/**
 * 获取地支的本气天干
 * @param {string} branch - 地支
 * @returns {string} 本气天干
 */
function getBranchMainStem(branch) {
  return BRANCH_MAIN_STEM[branch] || null;
}

/**
 * 分析八字十神分布
 * @param {Object} bazi - 八字信息
 * @returns {Object} 十神分析结果
 */
function analyzeTenGods(bazi) {
  console.log('🎯 开始十神分析，输入八字:', bazi);
  const dayMaster = bazi.dayMaster;
  console.log('🎯 日主:', dayMaster);
  const tenGodsDistribution = {};

  // 初始化十神统计
  Object.keys(TEN_GODS).forEach(god => {
    tenGodsDistribution[god] = [];
  });
  console.log('🎯 初始化十神分布:', tenGodsDistribution);

  // 分析年月时三柱的天干（日柱天干是日主，不分析）
  const stemPillars = [
    { name: '年干', stem: bazi.year.stem },
    { name: '月干', stem: bazi.month.stem },
    { name: '时干', stem: bazi.hour.stem }
  ];

  // 分析四柱地支（包括日支）
  const branchPillars = [
    { name: '年支', branch: bazi.year.branch },
    { name: '月支', branch: bazi.month.branch },
    { name: '日支', branch: bazi.day.branch },
    { name: '时支', branch: bazi.hour.branch }
  ];

  // 分析天干十神
  console.log('🎯 分析天干十神，天干柱:', stemPillars);
  stemPillars.forEach(pillar => {
    const tenGod = calculateTenGod(dayMaster, pillar.stem);
    console.log(`🎯 ${pillar.name}(${pillar.stem}) -> 十神: ${tenGod}`);
    if (tenGodsDistribution[tenGod]) {
      tenGodsDistribution[tenGod].push(pillar.name);
    }
  });
  console.log('🎯 天干十神分析后:', tenGodsDistribution);

  // 分析地支十神（地支以其本气天干论十神）
  branchPillars.forEach(pillar => {
    // 地支本气天干对照表
    const branchMainStem = getBranchMainStem(pillar.branch);
    if (branchMainStem) {
      const tenGod = calculateTenGod(dayMaster, branchMainStem);
      if (tenGodsDistribution[tenGod]) {
        tenGodsDistribution[tenGod].push(pillar.name);
      }
    }
  });

  console.log('🎯 十神分析完成，最终分布:', tenGodsDistribution);
  const result = {
    distribution: tenGodsDistribution,
    dayMaster: dayMaster,
    dayMasterElement: FIVE_ELEMENTS[dayMaster]
  };
  console.log('🎯 十神分析返回结果:', result);
  return result;
}

/**
 * 判断日主强弱（精确版）
 * @param {Object} bazi - 八字信息
 * @returns {Object} 强弱分析结果
 */
function analyzeDayMasterStrength(bazi) {
  const dayMaster = bazi.dayMaster;
  const dayElement = FIVE_ELEMENTS[dayMaster];
  const monthBranch = bazi.month.branch;
  const seasonElement = getSeasonElement(bazi.month.branch);

  let strength = 50; // 基础分数
  let analysis = [];
  let detailAnalysis = [];

  // 1. 得时（月令司权）- 最重要的因素
  const monthStrength = getMonthStrength(dayMaster, monthBranch);
  strength += monthStrength.score;
  analysis.push(monthStrength.description);
  detailAnalysis.push(`月令${monthBranch}：${monthStrength.description}（${monthStrength.score > 0 ? '+' : ''}${monthStrength.score}分）`);
  
  // 月令得气（最重要）
  if (monthElement === dayElement) {
    strength += 3;
    analysis.push('月令本气，日主得气');
  } else if (FIVE_ELEMENTS_RELATION[monthElement].generates === dayElement) {
    strength += 2;
    analysis.push('月令生助，日主有气');
  } else if (FIVE_ELEMENTS_RELATION[monthElement].destroys === dayElement) {
    strength -= 2;
    analysis.push('月令克制，日主失气');
  } else if (FIVE_ELEMENTS_RELATION[dayElement].destroys === monthElement) {
    strength -= 1;
    analysis.push('日主克月令，耗气');
  }
  
  // 统计其他柱的帮扶和克制
  [bazi.year, bazi.month, bazi.hour].forEach(pillar => {
    const stemElement = FIVE_ELEMENTS[pillar.stem];
    const branchElement = FIVE_ELEMENTS[pillar.branch];
    
    // 天干帮扶
    if (stemElement === dayElement) {
      strength += 1;
      analysis.push(`${pillar === bazi.year ? '年' : pillar === bazi.month ? '月' : '时'}干同类帮身`);
    } else if (FIVE_ELEMENTS_RELATION[stemElement].generates === dayElement) {
      strength += 0.5;
      analysis.push(`${pillar === bazi.year ? '年' : pillar === bazi.month ? '月' : '时'}干生助`);
    }
    
    // 地支帮扶（权重较小）
    if (branchElement === dayElement) {
      strength += 0.5;
      analysis.push(`${pillar === bazi.year ? '年' : pillar === bazi.month ? '月' : '时'}支同类`);
    }
  });
  
  // 判断强弱
  let strengthLevel;
  if (strength >= 3) {
    strengthLevel = '偏旺';
  } else if (strength >= 1) {
    strengthLevel = '中和偏强';
  } else if (strength >= -1) {
    strengthLevel = '中和';
  } else if (strength >= -3) {
    strengthLevel = '偏弱';
  } else {
    strengthLevel = '很弱';
  }
  
  return {
    strength: strength,
    level: strengthLevel,
    analysis: analysis
  };
}

/**
 * 确定用神喜忌
 * @param {Object} bazi - 八字信息
 * @param {Object} strengthAnalysis - 强弱分析
 * @returns {Object} 用神喜忌分析
 */
function analyzeUseGod(bazi, strengthAnalysis) {
  const dayElement = FIVE_ELEMENTS[bazi.dayMaster];
  const strengthLevel = strengthAnalysis.level;
  
  let useGod = [];
  let avoidGod = [];
  let analysis = [];
  
  if (strengthLevel === '偏旺' || strengthLevel === '很旺') {
    // 日主偏旺，需要克泄耗
    const drainElement = FIVE_ELEMENTS_RELATION[dayElement].generates; // 食伤
    const restrainElement = FIVE_ELEMENTS_RELATION[dayElement].destroyedBy; // 官杀
    const consumeElement = FIVE_ELEMENTS_RELATION[dayElement].destroys; // 财星
    
    useGod.push(drainElement, restrainElement, consumeElement);
    avoidGod.push(dayElement, FIVE_ELEMENTS_RELATION[dayElement].generatedBy);
    analysis.push('日主偏旺，喜克泄耗，忌生助');
    
  } else if (strengthLevel === '偏弱' || strengthLevel === '很弱') {
    // 日主偏弱，需要生助
    const supportElement = FIVE_ELEMENTS_RELATION[dayElement].generatedBy; // 印星
    
    useGod.push(dayElement, supportElement);
    avoidGod.push(
      FIVE_ELEMENTS_RELATION[dayElement].generates,
      FIVE_ELEMENTS_RELATION[dayElement].destroyedBy,
      FIVE_ELEMENTS_RELATION[dayElement].destroys
    );
    analysis.push('日主偏弱，喜生助，忌克泄耗');
    
  } else {
    // 中和，根据具体情况调节
    analysis.push('日主中和，需要根据具体格局和大运流年调节');
    useGod.push('因时制宜');
  }
  
  return {
    useGod: useGod,
    avoidGod: avoidGod,
    analysis: analysis
  };
}

/**
 * 分析格局（完整版）
 * @param {Object} bazi - 八字信息
 * @param {Object} tenGodsAnalysis - 十神分析
 * @param {Object} strengthAnalysis - 身强身弱分析
 * @returns {Object} 格局分析
 */
function analyzePattern(bazi, tenGodsAnalysis, strengthAnalysis) {
  const distribution = tenGodsAnalysis.distribution;
  const dayMaster = bazi.dayMaster;
  const monthBranch = bazi.month.branch;
  const monthStem = bazi.month.stem;

  // 首先判断是否为特殊格局
  const specialPattern = analyzeSpecialPattern(bazi, tenGodsAnalysis, strengthAnalysis);
  if (specialPattern.pattern !== '普通格局') {
    return specialPattern;
  }

  // 判断正格（以月令为主）
  let pattern = '普通格局';
  let analysis = [];
  let patternGod = '';

  // 月令用神确定
  const monthTenGod = calculateTenGod(dayMaster, getBranchMainStem(monthBranch));

  // 正官格（月令本气为正官，且天干透出正官）
  if (monthTenGod === '正官' && distribution['正官'].length > 0 && distribution['七杀'].length === 0) {
    pattern = '正官格';
    patternGod = '正官';
    analysis.push('月令正官当权，天干透出正官，为正官格');
    analysis.push('主贵气、名声、地位，利求官求名');
  }

  // 七杀格（月令本气为七杀，且天干透出七杀）
  else if (monthTenGod === '七杀' && distribution['七杀'].length > 0 && distribution['正官'].length === 0) {
    pattern = '七杀格';
    patternGod = '七杀';
    analysis.push('月令七杀当权，天干透出七杀，为七杀格');
    analysis.push('主威权、魄力、事业，需要制化得宜');
  }

  // 正财格（月令本气为正财，且天干透出正财）
  else if (monthTenGod === '正财' && distribution['正财'].length > 0 && distribution['偏财'].length === 0) {
    pattern = '正财格';
    patternGod = '正财';
    analysis.push('月令正财当权，天干透出正财，为正财格');
    analysis.push('主财富、妻缘、稳定收入');
  }
  
  // 偏财格
  else if (distribution['偏财'].length > 0 && distribution['正财'].length === 0) {
    pattern = '偏财格';
    analysis.push('偏财透干，为偏财格，主横财');
  }
  
  // 食神格
  else if (distribution['食神'].length > 0 && distribution['伤官'].length === 0) {
    pattern = '食神格';
    analysis.push('食神透干，为食神格，主才华');
  }
  
  // 伤官格
  else if (distribution['伤官'].length > 0 && distribution['食神'].length === 0) {
    pattern = '伤官格';
    analysis.push('伤官透干，为伤官格，主聪明');
  }
  
  // 正印格
  else if (distribution['正印'].length > 0 && distribution['偏印'].length === 0) {
    pattern = '正印格';
    analysis.push('正印透干，为正印格，主学问');
  }
  
  // 偏印格
  else if (distribution['偏印'].length > 0 && distribution['正印'].length === 0) {
    pattern = '偏印格';
    analysis.push('偏印透干，为偏印格，主偏业');
  }

  // 偏财格（月令本气为偏财，且天干透出偏财）
  else if (monthTenGod === '偏财' && distribution['偏财'].length > 0 && distribution['正财'].length === 0) {
    pattern = '偏财格';
    patternGod = '偏财';
    analysis.push('月令偏财当权，天干透出偏财，为偏财格');
    analysis.push('主横财、投资、父缘、多妻');
  }

  // 食神格（月令本气为食神，且天干透出食神）
  else if (monthTenGod === '食神' && distribution['食神'].length > 0 && distribution['伤官'].length === 0) {
    pattern = '食神格';
    patternGod = '食神';
    analysis.push('月令食神当权，天干透出食神，为食神格');
    analysis.push('主才华、享受、子女、衣食丰足');
  }

  // 伤官格（月令本气为伤官，且天干透出伤官）
  else if (monthTenGod === '伤官' && distribution['伤官'].length > 0 && distribution['食神'].length === 0) {
    pattern = '伤官格';
    patternGod = '伤官';
    analysis.push('月令伤官当权，天干透出伤官，为伤官格');
    analysis.push('主聪明、才华、叛逆，需要制化');
  }

  // 正印格（月令本气为正印，且天干透出正印）
  else if (monthTenGod === '正印' && distribution['正印'].length > 0 && distribution['偏印'].length === 0) {
    pattern = '正印格';
    patternGod = '正印';
    analysis.push('月令正印当权，天干透出正印，为正印格');
    analysis.push('主学问、贵人、母缘、文职');
  }

  // 偏印格（月令本气为偏印，且天干透出偏印）
  else if (monthTenGod === '偏印' && distribution['偏印'].length > 0 && distribution['正印'].length === 0) {
    pattern = '偏印格';
    patternGod = '偏印';
    analysis.push('月令偏印当权，天干透出偏印，为偏印格');
    analysis.push('主偏业、技艺、继母、孤独');
  }

  // 建禄格（月令为日主禄地）
  else if (isLuDi(dayMaster, monthBranch)) {
    pattern = '建禄格';
    patternGod = '比肩';
    analysis.push('月令为日主禄地，为建禄格');
    analysis.push('主自立自强，需要财官配合');
  }

  // 羊刃格（月令为日主刃地）
  else if (isYangRen(dayMaster, monthBranch)) {
    pattern = '羊刃格';
    patternGod = '劫财';
    analysis.push('月令为日主刃地，为羊刃格');
    analysis.push('主刚强果断，需要官杀制化');
  }

  return {
    pattern: pattern,
    patternGod: patternGod,
    analysis: analysis,
    level: getPatternLevel(pattern, bazi, tenGodsAnalysis)
  };
}

/**
 * 分析特殊格局（从格、专旺格、化格等）- 完整版
 * @param {Object} bazi - 八字信息
 * @param {Object} tenGodsAnalysis - 十神分析
 * @param {Object} strengthAnalysis - 身强身弱分析
 * @returns {Object} 特殊格局分析
 */
function analyzeSpecialPattern(bazi, tenGodsAnalysis, strengthAnalysis) {
  let pattern = '普通格局';
  let analysis = [];
  const distribution = tenGodsAnalysis.distribution;
  const strengthLevel = strengthAnalysis.level;
  const strengthScore = strengthAnalysis.score;

  // 从格判断（日主极弱，无根无助，顺从其他五行）
  if (strengthScore <= 20 && strengthLevel === '极弱') {
    // 统计各类十神数量
    const wealthCount = (distribution['正财'] || []).length + (distribution['偏财'] || []).length;
    const officialCount = (distribution['正官'] || []).length + (distribution['七杀'] || []).length;
    const foodCount = (distribution['食神'] || []).length + (distribution['伤官'] || []).length;
    const sealCount = (distribution['正印'] || []).length + (distribution['偏印'] || []).length;
    const robCount = (distribution['比肩'] || []).length + (distribution['劫财'] || []).length;

    // 从财格（财星最多且无印星克制）
    if (wealthCount >= 2 && sealCount === 0 && robCount === 0) {
      pattern = '从财格';
      analysis.push('日主极弱无根，财星当令得势，无印比救助');
      analysis.push('从财而论，主富贵双全，利经商求财');
      analysis.push('忌印比岁运，喜财食官运');
    }
    // 从官杀格（官杀最多且无食伤克制）
    else if (officialCount >= 2 && foodCount === 0 && robCount === 0) {
      pattern = '从官杀格';
      analysis.push('日主极弱无根，官杀当令得势，无食伤制克');
      analysis.push('从官杀而论，主贵气权威，利求官求名');
      analysis.push('忌食伤比劫运，喜官杀财印运');
    }
    // 从儿格（食伤最多且无印星克制）
    else if (foodCount >= 2 && sealCount === 0 && robCount === 0) {
      pattern = '从儿格';
      analysis.push('日主极弱无根，食伤当令得势，无印星克制');
      analysis.push('从儿而论，主才华横溢，利艺术创作');
      analysis.push('忌印比岁运，喜食伤财运');
    }
    // 从势格（多种五行混杂，日主顺从大势）
    else if (wealthCount + officialCount + foodCount >= 3 && robCount === 0 && sealCount === 0) {
      pattern = '从势格';
      analysis.push('日主极弱无根，多种五行混杂当令');
      analysis.push('从势而论，随波逐流，适应性强');
      analysis.push('忌印比岁运，喜顺势而为');
    }
  }

  // 专旺格判断（日主极强，专旺一行）
  else if (strengthScore >= 80 && strengthLevel === '极强') {
    const robCount = (distribution['比肩'] || []).length + (distribution['劫财'] || []).length;
    const sealCount = (distribution['正印'] || []).length + (distribution['偏印'] || []).length;

    // 曲直格（木日主专旺）
    if (bazi.dayMasterElement === '木' && robCount + sealCount >= 3) {
      pattern = '曲直格';
      analysis.push('甲乙木日主，地支寅卯辰或亥卯未会木局');
      analysis.push('木气专旺，主仁慈正直，利文职教育');
      analysis.push('喜水木运，忌金运，火土运平平');
    }
    // 炎上格（火日主专旺）
    else if (bazi.dayMasterElement === '火' && robCount + sealCount >= 3) {
      pattern = '炎上格';
      analysis.push('丙丁火日主，地支巳午未或寅午戌会火局');
      analysis.push('火气专旺，主聪明热情，利文艺表演');
      analysis.push('喜木火运，忌水运，金土运平平');
    }
    // 从革格（金日主专旺）
    else if (bazi.dayMasterElement === '金' && robCount + sealCount >= 3) {
      pattern = '从革格';
      analysis.push('庚辛金日主，地支申酉戌或巳酉丑会金局');
      analysis.push('金气专旺，主刚毅果断，利武职军警');
      analysis.push('喜土金运，忌火运，水木运平平');
    }
    // 润下格（水日主专旺）
    else if (bazi.dayMasterElement === '水' && robCount + sealCount >= 3) {
      pattern = '润下格';
      analysis.push('壬癸水日主，地支亥子丑或申子辰会水局');
      analysis.push('水气专旺，主智慧灵活，利流动行业');
      analysis.push('喜金水运，忌土运，木火运平平');
    }
    // 稼穑格（土日主专旺）
    else if (bazi.dayMasterElement === '土' && robCount + sealCount >= 3) {
      pattern = '稼穑格';
      analysis.push('戊己土日主，地支辰戌丑未四库全');
      analysis.push('土气专旺，主厚重稳健，利农业地产');
      analysis.push('喜火土运，忌木运，金水运平平');
    }
  }

  return {
    pattern: pattern,
    analysis: analysis,
    level: getSpecialPatternLevel(pattern)
  };
}

/**
 * 判断是否为禄地
 */
function isLuDi(dayMaster, branch) {
  const luDiMap = {
    '甲': '寅', '乙': '卯', '丙': '巳', '丁': '午',
    '戊': '巳', '己': '午', '庚': '申', '辛': '酉',
    '壬': '亥', '癸': '子'
  };
  return luDiMap[dayMaster] === branch;
}

/**
 * 判断是否为羊刃
 */
function isYangRen(dayMaster, branch) {
  const yangRenMap = {
    '甲': '卯', '丙': '午', '戊': '午',
    '庚': '酉', '壬': '子'
  };
  return yangRenMap[dayMaster] === branch;
}

/**
 * 获取格局等级
 */
function getPatternLevel(pattern, bazi, tenGodsAnalysis) {
  // 根据格局类型和配置判断等级
  if (pattern.includes('从') || pattern.includes('专旺')) {
    return '特殊格局';
  } else if (['正官格', '正财格', '食神格', '正印格'].includes(pattern)) {
    return '上等格局';
  } else if (['七杀格', '偏财格', '伤官格', '偏印格'].includes(pattern)) {
    return '中等格局';
  } else if (['建禄格', '羊刃格'].includes(pattern)) {
    return '普通格局';
  }
  return '普通格局';
}

/**
 * 获取特殊格局等级
 */
function getSpecialPatternLevel(pattern) {
  if (['曲直格', '炎上格', '从革格', '润下格', '稼穑格'].includes(pattern)) {
    return '上等特殊格局';
  } else if (['从财格', '从官杀格', '从儿格'].includes(pattern)) {
    return '中等特殊格局';
  } else if (pattern === '从势格') {
    return '下等特殊格局';
  }
  return '普通格局';
}

/**
 * 获取月令强度
 */
function getMonthStrength(dayMaster, monthBranch) {
  const dayElement = FIVE_ELEMENTS[dayMaster];
  const monthMainStem = getBranchMainStem(monthBranch);
  const monthElement = FIVE_ELEMENTS[monthMainStem];

  // 十二长生表
  const twelveStages = {
    '甲': { '亥': '长生', '子': '沐浴', '丑': '冠带', '寅': '临官', '卯': '帝旺', '辰': '衰', '巳': '病', '午': '死', '未': '墓', '申': '绝', '酉': '胎', '戌': '养' },
    '乙': { '午': '长生', '巳': '沐浴', '辰': '冠带', '卯': '临官', '寅': '帝旺', '丑': '衰', '子': '病', '亥': '死', '戌': '墓', '酉': '绝', '申': '胎', '未': '养' },
    '丙': { '寅': '长生', '卯': '沐浴', '辰': '冠带', '巳': '临官', '午': '帝旺', '未': '衰', '申': '病', '酉': '死', '戌': '墓', '亥': '绝', '子': '胎', '丑': '养' },
    '丁': { '酉': '长生', '申': '沐浴', '未': '冠带', '午': '临官', '巳': '帝旺', '辰': '衰', '卯': '病', '寅': '死', '丑': '墓', '子': '绝', '亥': '胎', '戌': '养' },
    '戊': { '寅': '长生', '卯': '沐浴', '辰': '冠带', '巳': '临官', '午': '帝旺', '未': '衰', '申': '病', '酉': '死', '戌': '墓', '亥': '绝', '子': '胎', '丑': '养' },
    '己': { '酉': '长生', '申': '沐浴', '未': '冠带', '午': '临官', '巳': '帝旺', '辰': '衰', '卯': '病', '寅': '死', '丑': '墓', '子': '绝', '亥': '胎', '戌': '养' },
    '庚': { '巳': '长生', '午': '沐浴', '未': '冠带', '申': '临官', '酉': '帝旺', '戌': '衰', '亥': '病', '子': '死', '丑': '墓', '寅': '绝', '卯': '胎', '辰': '养' },
    '辛': { '子': '长生', '亥': '沐浴', '戌': '冠带', '酉': '临官', '申': '帝旺', '未': '衰', '午': '病', '巳': '死', '辰': '墓', '卯': '绝', '寅': '胎', '丑': '养' },
    '壬': { '申': '长生', '酉': '沐浴', '戌': '冠带', '亥': '临官', '子': '帝旺', '丑': '衰', '寅': '病', '卯': '死', '辰': '墓', '巳': '绝', '午': '胎', '未': '养' },
    '癸': { '卯': '长生', '寅': '沐浴', '丑': '冠带', '子': '临官', '亥': '帝旺', '戌': '衰', '酉': '病', '申': '死', '未': '墓', '午': '绝', '巳': '胎', '辰': '养' }
  };

  const stage = twelveStages[dayMaster][monthBranch];
  let score = 0;
  let description = '';

  switch (stage) {
    case '帝旺':
      score = 25;
      description = `${dayMaster}在${monthBranch}月帝旺，极强`;
      break;
    case '临官':
      score = 20;
      description = `${dayMaster}在${monthBranch}月临官，很强`;
      break;
    case '长生':
      score = 15;
      description = `${dayMaster}在${monthBranch}月长生，较强`;
      break;
    case '冠带':
      score = 10;
      description = `${dayMaster}在${monthBranch}月冠带，稍强`;
      break;
    case '养':
      score = 5;
      description = `${dayMaster}在${monthBranch}月养地，微强`;
      break;
    case '胎':
      score = 0;
      description = `${dayMaster}在${monthBranch}月胎地，平和`;
      break;
    case '沐浴':
      score = -5;
      description = `${dayMaster}在${monthBranch}月沐浴，稍弱`;
      break;
    case '衰':
      score = -10;
      description = `${dayMaster}在${monthBranch}月衰地，较弱`;
      break;
    case '病':
      score = -15;
      description = `${dayMaster}在${monthBranch}月病地，很弱`;
      break;
    case '死':
      score = -20;
      description = `${dayMaster}在${monthBranch}月死地，极弱`;
      break;
    case '墓':
      score = -15;
      description = `${dayMaster}在${monthBranch}月墓地，很弱`;
      break;
    case '绝':
      score = -25;
      description = `${dayMaster}在${monthBranch}月绝地，极弱`;
      break;
    default:
      score = 0;
      description = `${dayMaster}在${monthBranch}月平和`;
  }

  return { score, description, stage };
}

/**
 * 分析地支根气
 */
function analyzeRoots(dayMaster, bazi) {
  const branches = [bazi.year.branch, bazi.month.branch, bazi.day.branch, bazi.hour.branch];
  let totalScore = 0;
  let rootDetails = [];

  branches.forEach((branch, index) => {
    const pillarName = ['年支', '月支', '日支', '时支'][index];
    const rootStrength = getRootStrength(dayMaster, branch);

    if (rootStrength.score > 0) {
      totalScore += rootStrength.score;
      rootDetails.push(`${pillarName}${branch}${rootStrength.description}`);
    }
  });

  let description = '';
  if (totalScore >= 20) {
    description = `根气深厚（${rootDetails.join('，')}）`;
  } else if (totalScore >= 10) {
    description = `有根气（${rootDetails.join('，')}）`;
  } else if (totalScore > 0) {
    description = `根气微弱（${rootDetails.join('，')}）`;
  } else {
    description = '无根，虚浮';
  }

  return { score: totalScore, description, details: rootDetails };
}

/**
 * 获取根气强度
 */
function getRootStrength(dayMaster, branch) {
  // 地支藏干表
  const branchHiddenStems = {
    '子': [{ stem: '癸', strength: 10 }],
    '丑': [{ stem: '己', strength: 6 }, { stem: '癸', strength: 3 }, { stem: '辛', strength: 1 }],
    '寅': [{ stem: '甲', strength: 7 }, { stem: '丙', strength: 2 }, { stem: '戊', strength: 1 }],
    '卯': [{ stem: '乙', strength: 10 }],
    '辰': [{ stem: '戊', strength: 6 }, { stem: '乙', strength: 3 }, { stem: '癸', strength: 1 }],
    '巳': [{ stem: '丙', strength: 7 }, { stem: '庚', strength: 2 }, { stem: '戊', strength: 1 }],
    '午': [{ stem: '丁', strength: 7 }, { stem: '己', strength: 3 }],
    '未': [{ stem: '己', strength: 6 }, { stem: '丁', strength: 3 }, { stem: '乙', strength: 1 }],
    '申': [{ stem: '庚', strength: 7 }, { stem: '壬', strength: 2 }, { stem: '戊', strength: 1 }],
    '酉': [{ stem: '辛', strength: 10 }],
    '戌': [{ stem: '戊', strength: 6 }, { stem: '辛', strength: 3 }, { stem: '丁', strength: 1 }],
    '亥': [{ stem: '壬', strength: 7 }, { stem: '甲', strength: 3 }]
  };

  const hiddenStems = branchHiddenStems[branch] || [];
  let totalScore = 0;
  let description = '';

  hiddenStems.forEach(hidden => {
    if (hidden.stem === dayMaster) {
      totalScore += hidden.strength;
      if (hidden.strength >= 7) {
        description = '本气根';
      } else if (hidden.strength >= 3) {
        description = '中气根';
      } else {
        description = '余气根';
      }
    }
  });

  return { score: totalScore, description };
}

/**
 * 分析天干帮扶
 */
function analyzeHelp(dayMaster, bazi) {
  const stems = [bazi.year.stem, bazi.month.stem, bazi.hour.stem]; // 不包括日干
  const dayElement = FIVE_ELEMENTS[dayMaster];
  let totalScore = 0;
  let helpDetails = [];

  stems.forEach((stem, index) => {
    const pillarName = ['年干', '月干', '时干'][index];
    const stemElement = FIVE_ELEMENTS[stem];

    if (stemElement === dayElement) {
      totalScore += 15;
      helpDetails.push(`${pillarName}${stem}比劫帮身`);
    }
  });

  let description = '';
  if (totalScore >= 30) {
    description = `比劫众多，帮身有力（${helpDetails.join('，')}）`;
  } else if (totalScore >= 15) {
    description = `有比劫帮身（${helpDetails.join('，')}）`;
  } else {
    description = '无比劫帮身，孤立无援';
  }

  return { score: totalScore, description, details: helpDetails };
}

/**
 * 分析印星生扶
 */
function analyzeSealSupport(dayMaster, bazi) {
  const allStems = [bazi.year.stem, bazi.month.stem, bazi.hour.stem];
  const allBranches = [bazi.year.branch, bazi.month.branch, bazi.day.branch, bazi.hour.branch];
  const dayElement = FIVE_ELEMENTS[dayMaster];
  let totalScore = 0;
  let sealDetails = [];

  // 检查天干印星
  allStems.forEach((stem, index) => {
    const pillarName = ['年干', '月干', '时干'][index];
    const stemElement = FIVE_ELEMENTS[stem];

    if (FIVE_ELEMENTS_RELATION[stemElement] && FIVE_ELEMENTS_RELATION[stemElement].generates === dayElement) {
      totalScore += 10;
      const tenGod = calculateTenGod(dayMaster, stem);
      sealDetails.push(`${pillarName}${stem}${tenGod}生身`);
    }
  });

  // 检查地支印星（简化处理）
  allBranches.forEach((branch, index) => {
    const pillarName = ['年支', '月支', '日支', '时支'][index];
    const mainStem = getBranchMainStem(branch);
    const branchElement = FIVE_ELEMENTS[mainStem];

    if (FIVE_ELEMENTS_RELATION[branchElement] && FIVE_ELEMENTS_RELATION[branchElement].generates === dayElement) {
      totalScore += 5;
      sealDetails.push(`${pillarName}${branch}印星暗助`);
    }
  });

  let description = '';
  if (totalScore >= 20) {
    description = `印星众多，生扶有力（${sealDetails.join('，')}）`;
  } else if (totalScore >= 10) {
    description = `有印星生扶（${sealDetails.join('，')}）`;
  } else if (totalScore > 0) {
    description = `印星微弱（${sealDetails.join('，')}）`;
  } else {
    description = '';
  }

  return { score: totalScore, description, details: sealDetails };
}

/**
 * 分析克泄耗力量
 */
function analyzeWeakenForces(dayMaster, bazi) {
  const allStems = [bazi.year.stem, bazi.month.stem, bazi.hour.stem];
  const dayElement = FIVE_ELEMENTS[dayMaster];
  let totalScore = 0;
  let weakenDetails = [];

  allStems.forEach((stem, index) => {
    const pillarName = ['年干', '月干', '时干'][index];
    const stemElement = FIVE_ELEMENTS[stem];
    const tenGod = calculateTenGod(dayMaster, stem);

    // 官杀克身
    if (['正官', '七杀'].includes(tenGod)) {
      totalScore -= 8;
      weakenDetails.push(`${pillarName}${stem}${tenGod}克身`);
    }
    // 食伤泄身
    else if (['食神', '伤官'].includes(tenGod)) {
      totalScore -= 6;
      weakenDetails.push(`${pillarName}${stem}${tenGod}泄身`);
    }
    // 财星耗身
    else if (['正财', '偏财'].includes(tenGod)) {
      totalScore -= 4;
      weakenDetails.push(`${pillarName}${stem}${tenGod}耗身`);
    }
  });

  let description = '';
  if (totalScore <= -20) {
    description = `克泄耗严重（${weakenDetails.join('，')}）`;
  } else if (totalScore <= -10) {
    description = `有克泄耗（${weakenDetails.join('，')}）`;
  } else if (totalScore < 0) {
    description = `克泄耗轻微（${weakenDetails.join('，')}）`;
  } else {
    description = '';
  }

  return { score: totalScore, description, details: weakenDetails };
}

/**
 * 分析用神喜忌
 */
function analyzeYongshen(strengthLevel, dayMaster, bazi) {
  const dayElement = FIVE_ELEMENTS[dayMaster];
  let yongshen = [];
  let xishen = [];
  let jishen = [];
  let choushen = [];

  if (['极强', '偏强'].includes(strengthLevel)) {
    // 身强用官杀、食伤、财星
    yongshen = ['官杀', '食伤', '财星'];
    xishen = ['财星', '食伤'];
    jishen = ['印星', '比劫'];
    choushen = ['比劫'];
  } else if (['极弱', '偏弱'].includes(strengthLevel)) {
    // 身弱用印星、比劫
    yongshen = ['印星', '比劫'];
    xishen = ['比劫', '印星'];
    jishen = ['官杀', '食伤', '财星'];
    choushen = ['官杀'];
  } else {
    // 中和看具体配置
    yongshen = ['调候用神'];
    xishen = ['平衡五行'];
    jishen = ['过旺之神'];
    choushen = ['破格之神'];
  }

  return { yongshen, xishen, jishen, choushen };
}

/**
 * 获取季节五行
 */
function getSeasonElement(monthBranch) {
  const seasonMap = {
    '寅': '木', '卯': '木', '辰': '土',
    '巳': '火', '午': '火', '未': '土',
    '申': '金', '酉': '金', '戌': '土',
    '亥': '水', '子': '水', '丑': '土'
  };
  return seasonMap[monthBranch] || '土';
}

/**
 * 分析格局成败
 * @param {Object} bazi - 八字信息
 * @param {string} pattern - 格局名称
 * @param {Object} strengthAnalysis - 身强身弱分析
 * @returns {Object} 格局成败分析
 */
function analyzePatternSuccess(bazi, pattern, strengthAnalysis) {
  let success = '中等';
  let analysis = [];

  const strengthLevel = strengthAnalysis.level;

  // 根据不同格局判断成败
  if (pattern.includes('正官格')) {
    if (strengthLevel === '中和' || strengthLevel === '中和偏强') {
      success = '成格';
      analysis.push('身官平衡，正官格成，主贵气显达');
    } else if (strengthLevel === '偏弱') {
      success = '偏败';
      analysis.push('身弱官强，需要印星生身或比劫帮身');
    } else {
      success = '偏败';
      analysis.push('身强官弱，官星无力，贵气不显');
    }
  }
  else if (pattern.includes('正财格')) {
    if (strengthLevel === '中和偏强' || strengthLevel === '偏旺') {
      success = '成格';
      analysis.push('身强财旺，正财格成，主富裕安康');
    } else {
      success = '偏败';
      analysis.push('身弱财多，财多身弱，需要比劫帮身');
    }
  }
  else if (pattern.includes('从')) {
    if (strengthLevel === '很弱' || strengthLevel === '偏弱') {
      success = '成格';
      analysis.push('身弱从势，格局清纯，主富贵可期');
    } else {
      success = '败格';
      analysis.push('身不够弱，从格不成，反为贫贱');
    }
  }

  return {
    success: success,
    analysis: analysis
  };
}

/**
 * 综合八字分析（完整优化版）
 * @param {Object} bazi - 八字信息
 * @returns {Object} 综合分析结果
 */
function comprehensiveBaziAnalysis(bazi) {
  console.log('🎯 开始综合八字分析，输入八字:', bazi);

  // 十神分析
  const tenGodsAnalysis = analyzeTenGods(bazi);
  console.log('🎯 十神分析结果:', tenGodsAnalysis);

  // 身强身弱分析（优化版）
  const strengthAnalysis = analyzeDayMasterStrength(bazi);
  console.log('🎯 身强身弱分析结果:', strengthAnalysis);

  // 格局分析（包含特殊格局）
  const patternAnalysis = analyzePattern(bazi, tenGodsAnalysis, strengthAnalysis);
  console.log('🎯 格局分析结果:', patternAnalysis);

  // 神煞分析
  const shenshaAnalysis = analyzeShensha(bazi);
  console.log('🎯 神煞分析结果:', shenshaAnalysis);

  const result = {
    tenGods: tenGodsAnalysis,
    strength: strengthAnalysis,
    pattern: patternAnalysis,
    shensha: shenshaAnalysis,
    dayMaster: bazi.dayMaster,
    dayMasterElement: FIVE_ELEMENTS[bazi.dayMaster]
  };

  console.log('🎯 综合八字分析完成，返回结果:', result);
  return result;
}

module.exports = {
  TEN_GODS,
  FIVE_ELEMENTS_RELATION,
  calculateTenGod,
  analyzeTenGods,
  analyzeDayMasterStrength,
  analyzeUseGod,
  analyzePattern,
  comprehensiveBaziAnalysis
};
