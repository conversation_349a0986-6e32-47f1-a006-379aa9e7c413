// 八字十神分析系统
// 基于传统子平八字理论

const { FIVE_ELEMENTS, YIN_YANG } = require('./bazi-calculator.js');

// 十神定义
const TEN_GODS = {
  '比肩': { nature: '中性', meaning: '兄弟朋友、竞争、自我' },
  '劫财': { nature: '凶', meaning: '破财、小人、争夺' },
  '食神': { nature: '吉', meaning: '才华、享受、子女' },
  '伤官': { nature: '凶', meaning: '聪明、叛逆、伤害' },
  '偏财': { nature: '吉', meaning: '横财、父亲、情人' },
  '正财': { nature: '吉', meaning: '正当收入、妻子、财富' },
  '七杀': { nature: '凶', meaning: '压力、小人、疾病' },
  '正官': { nature: '吉', meaning: '地位、名声、丈夫' },
  '偏印': { nature: '凶', meaning: '偏业、继母、孤独' },
  '正印': { nature: '吉', meaning: '学问、母亲、贵人' }
};

// 五行生克关系
const FIVE_ELEMENTS_RELATION = {
  '木': { generates: '火', destroys: '土', generatedBy: '水', destroyedBy: '金' },
  '火': { generates: '土', destroys: '金', generatedBy: '木', destroyedBy: '水' },
  '土': { generates: '金', destroys: '水', generatedBy: '火', destroyedBy: '木' },
  '金': { generates: '水', destroys: '木', generatedBy: '土', destroyedBy: '火' },
  '水': { generates: '木', destroys: '火', generatedBy: '金', destroyedBy: '土' }
};

// 地支本气天干对照表
const BRANCH_MAIN_STEM = {
  '子': '癸', '丑': '己', '寅': '甲', '卯': '乙',
  '辰': '戊', '巳': '丙', '午': '丁', '未': '己',
  '申': '庚', '酉': '辛', '戌': '戊', '亥': '壬'
};

/**
 * 计算十神关系
 * @param {string} dayMaster - 日主天干
 * @param {string} targetStem - 目标天干
 * @returns {string} 十神名称
 */
function calculateTenGod(dayMaster, targetStem) {
  // 精确的十神对照表（基于传统子平八字理论）
  const tenGodTable = {
    '甲': { '甲': '比肩', '乙': '劫财', '丙': '食神', '丁': '伤官', '戊': '偏财', '己': '正财', '庚': '七杀', '辛': '正官', '壬': '偏印', '癸': '正印' },
    '乙': { '乙': '比肩', '甲': '劫财', '丁': '食神', '丙': '伤官', '己': '偏财', '戊': '正财', '辛': '七杀', '庚': '正官', '癸': '偏印', '壬': '正印' },
    '丙': { '丙': '比肩', '丁': '劫财', '戊': '食神', '己': '伤官', '庚': '偏财', '辛': '正财', '壬': '七杀', '癸': '正官', '甲': '偏印', '乙': '正印' },
    '丁': { '丁': '比肩', '丙': '劫财', '己': '食神', '戊': '伤官', '辛': '偏财', '庚': '正财', '癸': '七杀', '壬': '正官', '乙': '偏印', '甲': '正印' },
    '戊': { '戊': '比肩', '己': '劫财', '庚': '食神', '辛': '伤官', '壬': '偏财', '癸': '正财', '甲': '七杀', '乙': '正官', '丙': '偏印', '丁': '正印' },
    '己': { '己': '比肩', '戊': '劫财', '辛': '食神', '庚': '伤官', '癸': '偏财', '壬': '正财', '乙': '七杀', '甲': '正官', '丁': '偏印', '丙': '正印' },
    '庚': { '庚': '比肩', '辛': '劫财', '壬': '食神', '癸': '伤官', '甲': '偏财', '乙': '正财', '丙': '七杀', '丁': '正官', '戊': '偏印', '己': '正印' },
    '辛': { '辛': '比肩', '庚': '劫财', '癸': '食神', '壬': '伤官', '乙': '偏财', '甲': '正财', '丁': '七杀', '丙': '正官', '己': '偏印', '戊': '正印' },
    '壬': { '壬': '比肩', '癸': '劫财', '甲': '食神', '乙': '伤官', '丙': '偏财', '丁': '正财', '戊': '七杀', '己': '正官', '庚': '偏印', '辛': '正印' },
    '癸': { '癸': '比肩', '壬': '劫财', '乙': '食神', '甲': '伤官', '丁': '偏财', '丙': '正财', '己': '七杀', '戊': '正官', '辛': '偏印', '庚': '正印' }
  };

  // 验证输入参数
  if (!tenGodTable[dayMaster] || !tenGodTable[dayMaster][targetStem]) {
    console.error('十神计算参数错误:', { dayMaster, targetStem });
    return '比肩';
  }

  return tenGodTable[dayMaster][targetStem];
}

/**
 * 获取地支的本气天干
 * @param {string} branch - 地支
 * @returns {string} 本气天干
 */
function getBranchMainStem(branch) {
  return BRANCH_MAIN_STEM[branch] || null;
}

/**
 * 分析八字十神分布
 * @param {Object} bazi - 八字信息
 * @returns {Object} 十神分析结果
 */
function analyzeTenGods(bazi) {
  console.log('🎯 开始十神分析，输入八字:', bazi);
  const dayMaster = bazi.dayMaster;
  console.log('🎯 日主:', dayMaster);
  const tenGodsDistribution = {};

  // 初始化十神统计
  Object.keys(TEN_GODS).forEach(god => {
    tenGodsDistribution[god] = [];
  });
  console.log('🎯 初始化十神分布:', tenGodsDistribution);

  // 分析年月时三柱的天干（日柱天干是日主，不分析）
  const stemPillars = [
    { name: '年干', stem: bazi.year.stem },
    { name: '月干', stem: bazi.month.stem },
    { name: '时干', stem: bazi.hour.stem }
  ];

  // 分析四柱地支（包括日支）
  const branchPillars = [
    { name: '年支', branch: bazi.year.branch },
    { name: '月支', branch: bazi.month.branch },
    { name: '日支', branch: bazi.day.branch },
    { name: '时支', branch: bazi.hour.branch }
  ];

  // 分析天干十神
  console.log('🎯 分析天干十神，天干柱:', stemPillars);
  stemPillars.forEach(pillar => {
    const tenGod = calculateTenGod(dayMaster, pillar.stem);
    console.log(`🎯 ${pillar.name}(${pillar.stem}) -> 十神: ${tenGod}`);
    if (tenGodsDistribution[tenGod]) {
      tenGodsDistribution[tenGod].push(pillar.name);
    }
  });
  console.log('🎯 天干十神分析后:', tenGodsDistribution);

  // 分析地支十神（地支以其本气天干论十神）
  branchPillars.forEach(pillar => {
    // 地支本气天干对照表
    const branchMainStem = getBranchMainStem(pillar.branch);
    if (branchMainStem) {
      const tenGod = calculateTenGod(dayMaster, branchMainStem);
      if (tenGodsDistribution[tenGod]) {
        tenGodsDistribution[tenGod].push(pillar.name);
      }
    }
  });

  console.log('🎯 十神分析完成，最终分布:', tenGodsDistribution);
  const result = {
    distribution: tenGodsDistribution,
    dayMaster: dayMaster,
    dayMasterElement: FIVE_ELEMENTS[dayMaster]
  };
  console.log('🎯 十神分析返回结果:', result);
  return result;
}

/**
 * 判断日主强弱
 * @param {Object} bazi - 八字信息
 * @returns {Object} 强弱分析结果
 */
function analyzeDayMasterStrength(bazi) {
  const dayMaster = bazi.dayMaster;
  const dayElement = FIVE_ELEMENTS[dayMaster];
  const monthBranch = bazi.month.branch;
  const monthElement = FIVE_ELEMENTS[monthBranch];
  
  let strength = 0;
  let analysis = [];
  
  // 月令得气（最重要）
  if (monthElement === dayElement) {
    strength += 3;
    analysis.push('月令本气，日主得气');
  } else if (FIVE_ELEMENTS_RELATION[monthElement].generates === dayElement) {
    strength += 2;
    analysis.push('月令生助，日主有气');
  } else if (FIVE_ELEMENTS_RELATION[monthElement].destroys === dayElement) {
    strength -= 2;
    analysis.push('月令克制，日主失气');
  } else if (FIVE_ELEMENTS_RELATION[dayElement].destroys === monthElement) {
    strength -= 1;
    analysis.push('日主克月令，耗气');
  }
  
  // 统计其他柱的帮扶和克制
  [bazi.year, bazi.month, bazi.hour].forEach(pillar => {
    const stemElement = FIVE_ELEMENTS[pillar.stem];
    const branchElement = FIVE_ELEMENTS[pillar.branch];
    
    // 天干帮扶
    if (stemElement === dayElement) {
      strength += 1;
      analysis.push(`${pillar === bazi.year ? '年' : pillar === bazi.month ? '月' : '时'}干同类帮身`);
    } else if (FIVE_ELEMENTS_RELATION[stemElement].generates === dayElement) {
      strength += 0.5;
      analysis.push(`${pillar === bazi.year ? '年' : pillar === bazi.month ? '月' : '时'}干生助`);
    }
    
    // 地支帮扶（权重较小）
    if (branchElement === dayElement) {
      strength += 0.5;
      analysis.push(`${pillar === bazi.year ? '年' : pillar === bazi.month ? '月' : '时'}支同类`);
    }
  });
  
  // 判断强弱
  let strengthLevel;
  if (strength >= 3) {
    strengthLevel = '偏旺';
  } else if (strength >= 1) {
    strengthLevel = '中和偏强';
  } else if (strength >= -1) {
    strengthLevel = '中和';
  } else if (strength >= -3) {
    strengthLevel = '偏弱';
  } else {
    strengthLevel = '很弱';
  }
  
  return {
    strength: strength,
    level: strengthLevel,
    analysis: analysis
  };
}

/**
 * 确定用神喜忌
 * @param {Object} bazi - 八字信息
 * @param {Object} strengthAnalysis - 强弱分析
 * @returns {Object} 用神喜忌分析
 */
function analyzeUseGod(bazi, strengthAnalysis) {
  const dayElement = FIVE_ELEMENTS[bazi.dayMaster];
  const strengthLevel = strengthAnalysis.level;
  
  let useGod = [];
  let avoidGod = [];
  let analysis = [];
  
  if (strengthLevel === '偏旺' || strengthLevel === '很旺') {
    // 日主偏旺，需要克泄耗
    const drainElement = FIVE_ELEMENTS_RELATION[dayElement].generates; // 食伤
    const restrainElement = FIVE_ELEMENTS_RELATION[dayElement].destroyedBy; // 官杀
    const consumeElement = FIVE_ELEMENTS_RELATION[dayElement].destroys; // 财星
    
    useGod.push(drainElement, restrainElement, consumeElement);
    avoidGod.push(dayElement, FIVE_ELEMENTS_RELATION[dayElement].generatedBy);
    analysis.push('日主偏旺，喜克泄耗，忌生助');
    
  } else if (strengthLevel === '偏弱' || strengthLevel === '很弱') {
    // 日主偏弱，需要生助
    const supportElement = FIVE_ELEMENTS_RELATION[dayElement].generatedBy; // 印星
    
    useGod.push(dayElement, supportElement);
    avoidGod.push(
      FIVE_ELEMENTS_RELATION[dayElement].generates,
      FIVE_ELEMENTS_RELATION[dayElement].destroyedBy,
      FIVE_ELEMENTS_RELATION[dayElement].destroys
    );
    analysis.push('日主偏弱，喜生助，忌克泄耗');
    
  } else {
    // 中和，根据具体情况调节
    analysis.push('日主中和，需要根据具体格局和大运流年调节');
    useGod.push('因时制宜');
  }
  
  return {
    useGod: useGod,
    avoidGod: avoidGod,
    analysis: analysis
  };
}

/**
 * 分析格局
 * @param {Object} bazi - 八字信息
 * @param {Object} tenGodsAnalysis - 十神分析
 * @returns {Object} 格局分析
 */
function analyzePattern(bazi, tenGodsAnalysis) {
  const distribution = tenGodsAnalysis.distribution;
  
  // 检查是否有明显的格局特征
  let pattern = '普通格局';
  let analysis = [];
  
  // 正官格
  if (distribution['正官'].length > 0 && distribution['七杀'].length === 0) {
    pattern = '正官格';
    analysis.push('正官透干，为正官格，主贵气');
  }
  
  // 七杀格
  else if (distribution['七杀'].length > 0 && distribution['正官'].length === 0) {
    pattern = '七杀格';
    analysis.push('七杀透干，为七杀格，主威权');
  }
  
  // 正财格
  else if (distribution['正财'].length > 0 && distribution['偏财'].length === 0) {
    pattern = '正财格';
    analysis.push('正财透干，为正财格，主富裕');
  }
  
  // 偏财格
  else if (distribution['偏财'].length > 0 && distribution['正财'].length === 0) {
    pattern = '偏财格';
    analysis.push('偏财透干，为偏财格，主横财');
  }
  
  // 食神格
  else if (distribution['食神'].length > 0 && distribution['伤官'].length === 0) {
    pattern = '食神格';
    analysis.push('食神透干，为食神格，主才华');
  }
  
  // 伤官格
  else if (distribution['伤官'].length > 0 && distribution['食神'].length === 0) {
    pattern = '伤官格';
    analysis.push('伤官透干，为伤官格，主聪明');
  }
  
  // 正印格
  else if (distribution['正印'].length > 0 && distribution['偏印'].length === 0) {
    pattern = '正印格';
    analysis.push('正印透干，为正印格，主学问');
  }
  
  // 偏印格
  else if (distribution['偏印'].length > 0 && distribution['正印'].length === 0) {
    pattern = '偏印格';
    analysis.push('偏印透干，为偏印格，主偏业');
  }
  
  // 比肩格
  else if (distribution['比肩'].length >= 2) {
    pattern = '比肩格';
    analysis.push('比肩多见，为比肩格，主自立');
  }
  
  // 劫财格
  else if (distribution['劫财'].length >= 2) {
    pattern = '劫财格';
    analysis.push('劫财多见，为劫财格，需要制化');
  }
  
  return {
    pattern: pattern,
    analysis: analysis
  };
}

/**
 * 分析特殊格局（从格、专旺格、化格等）
 * @param {Object} bazi - 八字信息
 * @param {Object} tenGodsAnalysis - 十神分析
 * @param {Object} strengthAnalysis - 身强身弱分析
 * @returns {Object} 特殊格局分析
 */
function analyzeSpecialPattern(bazi, tenGodsAnalysis, strengthAnalysis) {
  let pattern = '普通格局';
  let analysis = [];

  const distribution = tenGodsAnalysis.summary;
  const strengthLevel = strengthAnalysis.level;

  // 从格判断（身极弱，顺从其他五行）
  if (strengthLevel === '很弱' || strengthLevel === '偏弱') {
    // 从财格
    if ((distribution['正财'] + distribution['偏财']) >= 3) {
      pattern = '从财格';
      analysis.push('日主极弱，财星众多，从财而论，主富贵');
    }
    // 从官格
    else if ((distribution['正官'] + distribution['七杀']) >= 3) {
      pattern = '从官格';
      analysis.push('日主极弱，官杀众多，从官而论，主贵气');
    }
    // 从食伤格
    else if ((distribution['食神'] + distribution['伤官']) >= 3) {
      pattern = '从儿格';
      analysis.push('日主极弱，食伤众多，从儿而论，主才华');
    }
  }

  // 专旺格判断（身极强，专旺一行）
  if (strengthLevel === '偏旺') {
    // 比劫专旺
    if ((distribution['比肩'] + distribution['劫财']) >= 4) {
      pattern = '比劫专旺格';
      analysis.push('比劫众多，专旺比劫，主自立自强');
    }
    // 印绶专旺
    else if ((distribution['正印'] + distribution['偏印']) >= 4) {
      pattern = '印绶专旺格';
      analysis.push('印星众多，专旺印绶，主学问贵气');
    }
  }

  return {
    pattern: pattern,
    analysis: analysis
  };
}

/**
 * 分析格局成败
 * @param {Object} bazi - 八字信息
 * @param {string} pattern - 格局名称
 * @param {Object} strengthAnalysis - 身强身弱分析
 * @returns {Object} 格局成败分析
 */
function analyzePatternSuccess(bazi, pattern, strengthAnalysis) {
  let success = '中等';
  let analysis = [];

  const strengthLevel = strengthAnalysis.level;

  // 根据不同格局判断成败
  if (pattern.includes('正官格')) {
    if (strengthLevel === '中和' || strengthLevel === '中和偏强') {
      success = '成格';
      analysis.push('身官平衡，正官格成，主贵气显达');
    } else if (strengthLevel === '偏弱') {
      success = '偏败';
      analysis.push('身弱官强，需要印星生身或比劫帮身');
    } else {
      success = '偏败';
      analysis.push('身强官弱，官星无力，贵气不显');
    }
  }
  else if (pattern.includes('正财格')) {
    if (strengthLevel === '中和偏强' || strengthLevel === '偏旺') {
      success = '成格';
      analysis.push('身强财旺，正财格成，主富裕安康');
    } else {
      success = '偏败';
      analysis.push('身弱财多，财多身弱，需要比劫帮身');
    }
  }
  else if (pattern.includes('从')) {
    if (strengthLevel === '很弱' || strengthLevel === '偏弱') {
      success = '成格';
      analysis.push('身弱从势，格局清纯，主富贵可期');
    } else {
      success = '败格';
      analysis.push('身不够弱，从格不成，反为贫贱');
    }
  }

  return {
    success: success,
    analysis: analysis
  };
}

/**
 * 综合八字分析
 * @param {Object} bazi - 八字信息
 * @returns {Object} 综合分析结果
 */
function comprehensiveBaziAnalysis(bazi) {
  const tenGodsAnalysis = analyzeTenGods(bazi);
  const strengthAnalysis = analyzeDayMasterStrength(bazi);
  const useGodAnalysis = analyzeUseGod(bazi, strengthAnalysis);
  const patternAnalysis = analyzePattern(bazi, tenGodsAnalysis, strengthAnalysis);

  return {
    tenGods: tenGodsAnalysis,
    strength: strengthAnalysis,
    useGod: useGodAnalysis,
    pattern: patternAnalysis,
    dayMaster: bazi.dayMaster,
    dayMasterElement: FIVE_ELEMENTS[bazi.dayMaster]
  };
}

module.exports = {
  TEN_GODS,
  FIVE_ELEMENTS_RELATION,
  calculateTenGod,
  analyzeTenGods,
  analyzeDayMasterStrength,
  analyzeUseGod,
  analyzePattern,
  comprehensiveBaziAnalysis
};
