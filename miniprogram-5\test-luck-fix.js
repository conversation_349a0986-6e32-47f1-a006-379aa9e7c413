// 测试八字大运和紫微大限修复结果
const { calculateStartLuck, calculateDayun } = require('./miniprogram/utils/bazi-luck.js');
const { calculateZiweiStartLuck, calculateZiweiDaxian } = require('./miniprogram/utils/ziwei-luck.js');

console.log('🎯 开始测试八字大运和紫微大限修复结果...\n');

// 测试八字大运计算
console.log('=== 八字大运测试 ===');

try {
  // 模拟八字数据
  const testBazi = {
    birthDate: new Date('1990-05-15T10:30:00'),
    year: { stem: '庚', branch: '午' },
    month: { stem: '辛', branch: '巳' },
    day: { stem: '甲', branch: '子' },
    hour: { stem: '己', branch: '巳' }
  };

  // 测试男性
  console.log('📋 测试案例：1990年5月15日10:30 男性');
  const maleStartLuck = calculateStartLuck(testBazi, true);
  console.log('✅ 男性起运信息:', {
    起运年龄: `${maleStartLuck.startAgeYears}岁${maleStartLuck.startAgeMonths}个月${maleStartLuck.startAgeDays}天`,
    起运日期: maleStartLuck.startDateString,
    起运方向: maleStartLuck.direction,
    目标节气: maleStartLuck.targetSolarTerm
  });

  const maleDayun = calculateDayun(testBazi, maleStartLuck);
  console.log('✅ 男性大运列表（前3个）:');
  maleDayun.slice(0, 3).forEach((dayun, index) => {
    console.log(`   ${index + 1}. ${dayun.startAge}-${dayun.endAge}岁 ${dayun.ganzhi}运`);
  });

  // 测试女性
  console.log('\n📋 测试案例：1990年5月15日10:30 女性');
  const femaleStartLuck = calculateStartLuck(testBazi, false);
  console.log('✅ 女性起运信息:', {
    起运年龄: `${femaleStartLuck.startAgeYears}岁${femaleStartLuck.startAgeMonths}个月${femaleStartLuck.startAgeDays}天`,
    起运日期: femaleStartLuck.startDateString,
    起运方向: femaleStartLuck.direction,
    目标节气: femaleStartLuck.targetSolarTerm
  });

  const femaleDayun = calculateDayun(testBazi, femaleStartLuck);
  console.log('✅ 女性大运列表（前3个）:');
  femaleDayun.slice(0, 3).forEach((dayun, index) => {
    console.log(`   ${index + 1}. ${dayun.startAge}-${dayun.endAge}岁 ${dayun.ganzhi}运`);
  });

} catch (error) {
  console.log('❌ 八字大运测试失败:', error.message);
}

console.log('\n=== 紫微大限测试 ===');

try {
  // 模拟紫微斗数数据
  const testZiwei = {
    wuxingJu: '火六局',
    year: { stem: '庚', branch: '午' }
  };

  // 测试男性
  console.log('📋 测试案例：火六局 男性');
  const maleZiweiLuck = calculateZiweiStartLuck(testZiwei, true, '庚');
  console.log('✅ 男性大限起运信息:', {
    起运年龄: `${maleZiweiLuck.startAge}岁`,
    五行局: maleZiweiLuck.wuxingJu,
    大限方向: maleZiweiLuck.direction
  });

  const maleZiweiDaxian = calculateZiweiDaxian(maleZiweiLuck);
  console.log('✅ 男性大限列表（前4个）:');
  maleZiweiDaxian.slice(0, 4).forEach((daxian, index) => {
    console.log(`   ${index + 1}. ${daxian.startAge}-${daxian.endAge}岁 ${daxian.palace}`);
  });

  // 测试女性
  console.log('\n📋 测试案例：火六局 女性');
  const femaleZiweiLuck = calculateZiweiStartLuck(testZiwei, false, '庚');
  console.log('✅ 女性大限起运信息:', {
    起运年龄: `${femaleZiweiLuck.startAge}岁`,
    五行局: femaleZiweiLuck.wuxingJu,
    大限方向: femaleZiweiLuck.direction
  });

  const femaleZiweiDaxian = calculateZiweiDaxian(femaleZiweiLuck);
  console.log('✅ 女性大限列表（前4个）:');
  femaleZiweiDaxian.slice(0, 4).forEach((daxian, index) => {
    console.log(`   ${index + 1}. ${daxian.startAge}-${daxian.endAge}岁 ${daxian.palace}`);
  });

} catch (error) {
  console.log('❌ 紫微大限测试失败:', error.message);
}

console.log('\n🎉 测试完成！');
console.log('📝 修复要点总结:');
console.log('   1. 八字大运：基于节气和阴阳干支的精确起运计算');
console.log('   2. 紫微大限：基于五行局数的正确起运年龄');
console.log('   3. 消除了"8岁出生月交大运"的错误问题');
console.log('   4. 实现了阳男阴女顺行、阴男阳女逆行的正确逻辑');
