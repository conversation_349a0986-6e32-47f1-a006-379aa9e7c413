// 知识库处理模块 - 云开发数据库查询 + AI语义检索
// 注意：小程序环境无法使用Node.js的fs模块，改用云开发数据库

/**
 * 知识库云开发配置
 */
const KNOWLEDGE_CONFIG = {
  // 云数据库集合名
  collection: 'knowledge_base',
  // 搜索限制
  searchLimit: 20,
  // AI语义检索限制
  semanticSearchLimit: 10,
  // 分类映射
  categories: {
    'yijing': '易经类',
    'bazi': '八字类',
    'ziwei': '紫微斗数',
    'meihua': '梅花易数',
    'other': '其他'
  },
  // 古籍专业术语词典
  professionalTerms: {
    '梅花易数': ['体卦', '用卦', '互卦', '变卦', '八卦万物', '先天八卦', '后天八卦', '卦气', '旺衰'],
    '周易六爻': ['世爻', '应爻', '动爻', '静爻', '六神', '六亲', '用神', '原神', '忌神', '仇神', '飞神', '伏神'],
    '子平八字': ['日主', '用神', '喜神', '忌神', '仇神', '十神', '大运', '流年', '格局', '身强', '身弱', '从格'],
    '紫微斗数': ['命宫', '身宫', '十二宫', '十四主星', '辅星', '煞星', '四化', '三方四正', '大限', '流年', '格局']
  },
  // 问题类型关键词扩展
  questionKeywords: {
    '财运': ['财运', '赚钱', '投资', '理财', '收入', '财富', '经济', '生意', '买卖', '股票', '基金', '房产', '妻财', '财星'],
    '事业': ['工作', '事业', '升职', '跳槽', '职业', '升迁', '当官', '仕途', '官运', '官鬼', '官星', '印星'],
    '婚姻': ['婚姻', '结婚', '感情', '恋爱', '配偶', '对象', '桃花', '夫妻', '姻缘', '红鸾', '天喜', '咸池'],
    '健康': ['健康', '疾病', '身体', '病情', '康复', '医疗', '养生', '寿命', '疾厄', '药师'],
    '学业': ['学习', '考试', '学业', '读书', '升学', '文凭', '文昌', '文曲', '父母爻', '印星']
  }
};

/**
 * 云开发数据库实例
 */
let db = null;

/**
 * 初始化数据库连接（支持多环境）
 */
function initDatabase() {
  if (!db) {
    try {
      // 🎯 环境检测
      if (typeof wx !== 'undefined' && wx.cloud) {
        // 微信小程序环境 - 强制重新初始化
        console.log('🔧 强制初始化微信云数据库...');

        // 确保云开发已初始化
        if (!wx.cloud._initialized) {
          console.log('⚠️ 云开发未初始化，尝试初始化...');
          wx.cloud.init({
            env: 'cloud1-0g3xctv612d8f755',
            traceUser: true
          });
        }

        db = wx.cloud.database();
        console.log('✅ 知识库数据库初始化成功（微信小程序）');
        console.log('📊 数据库实例:', !!db);
        console.log('🏷️ 目标集合:', KNOWLEDGE_CONFIG.collection);

        // 立即测试数据库连接
        testDatabaseConnection();

      } else if (typeof require !== 'undefined') {
        // Node.js环境 - 使用兼容版本
        const { initDatabase: initNodeDB } = require('./knowledge-base-node.js');
        db = initNodeDB();
        return db;
      } else {
        throw new Error('不支持的运行环境');
      }
    } catch (error) {
      console.error('❌ 知识库数据库初始化失败:', error);
      throw error;
    }
  }
  return db;
}

/**
 * 测试数据库连接
 */
async function testDatabaseConnection() {
  try {
    if (db && typeof wx !== 'undefined') {
      console.log('🧪 测试数据库连接...');
      const testResult = await db.collection(KNOWLEDGE_CONFIG.collection).limit(1).get();
      console.log('✅ 数据库连接测试成功，记录数:', testResult.data?.length || 0);
      if (testResult.data?.length > 0) {
        console.log('📋 示例记录:', testResult.data[0]._id);
      }
    }
  } catch (error) {
    console.error('❌ 数据库连接测试失败:', error);
  }
}

/**
 * 搜索知识库内容
 * @param {string} query 搜索关键词
 * @param {string} category 分类筛选
 * @param {number} limit 返回数量限制
 * @returns {Promise<Array>} 搜索结果
 */
async function searchKnowledge(query, category = '', limit = 20) {
  try {
    console.log('🔍 开始知识库搜索...');
    console.log('📝 查询关键词:', query);
    console.log('🏷️ 分类筛选:', category);
    console.log('📊 限制数量:', limit);

    // 强制初始化数据库
    const database = initDatabase();
    if (!database) {
      throw new Error('数据库初始化失败');
    }

    console.log('✅ 数据库已初始化');

    // 构建查询条件
    let whereCondition = {};

    // 分类筛选
    if (category && category !== 'all') {
      whereCondition.category = category;
      console.log('🎯 添加分类筛选:', category);
    }

    // 关键词搜索 - 使用正则表达式进行模糊匹配
    if (query && query.trim()) {
      const searchRegex = new RegExp(query.trim(), 'i');
      whereCondition = {
        ...whereCondition,
        $or: [
          { title: searchRegex },
          { author: searchRegex },
          { content: searchRegex },
          { keywords: searchRegex }
        ]
      };
      console.log('🔎 添加关键词搜索条件');
    }

    console.log('📋 最终查询条件:', JSON.stringify(whereCondition, null, 2));

    // 执行查询
    console.log('🚀 执行数据库查询...');
    const result = await database.collection(KNOWLEDGE_CONFIG.collection)
      .where(whereCondition)
      .limit(limit)
      .orderBy('_id', 'desc')  // 改用_id排序，避免created_at字段不存在的问题
      .get();

    console.log('✅ 查询完成，结果数量:', result.data?.length || 0);

    if (result.data && result.data.length > 0) {
      console.log('📋 示例结果:', {
        id: result.data[0]._id,
        title: result.data[0].title,
        category: result.data[0].category
      });
    }

    return result.data || [];
  } catch (error) {
    console.error('❌ 搜索知识库失败:', error);
    console.error('❌ 错误详情:', error.message);
    console.error('❌ 错误堆栈:', error.stack);
    return [];
  }
}

/**
 * 获取知识库统计信息
 * @returns {Promise<Object>} 统计信息
 */
async function getKnowledgeStats() {
  try {
    initDatabase();

    // 获取总数
    const totalResult = await db.collection(KNOWLEDGE_CONFIG.collection).count();
    const total = totalResult.total;

    // 按分类统计
    const categoryStats = {};
    for (const [key, value] of Object.entries(KNOWLEDGE_CONFIG.categories)) {
      const categoryResult = await db.collection(KNOWLEDGE_CONFIG.collection)
        .where({ category: key })
        .count();
      categoryStats[key] = {
        name: value,
        count: categoryResult.total
      };
    }

    return {
      total,
      categories: categoryStats
    };
  } catch (error) {
    console.error('获取知识库统计失败:', error);
    return {
      total: 0,
      categories: {}
    };
  }
}

/**
 * 根据问题类型获取相关知识（AI语义检索增强版）
 * @param {string} questionType - 问题类型
 * @param {string} specificQuery - 具体查询内容
 * @param {object} context - 上下文信息（卦象、八字等）
 * @returns {Promise<Array>} 相关知识内容
 */
async function getRelevantKnowledge(questionType, specificQuery = '', context = {}) {
  try {
    // 问题类型到分类的映射
    const categoryMap = {
      '财运': 'bazi',
      '事业': 'bazi',
      '婚姻': 'bazi',
      '健康': 'bazi',
      '学业': 'bazi',
      '卦象': 'yijing',
      '六爻': 'yijing',
      '梅花': 'meihua',
      '紫微': 'ziwei'
    };

    const category = categoryMap[questionType] || '';

    // 构建增强搜索关键词
    const enhancedKeywords = buildEnhancedKeywords(questionType, specificQuery, context);

    // 先进行传统关键词搜索
    const traditionalResults = await searchKnowledge(enhancedKeywords, category, 5);

    // 再进行AI语义检索
    const semanticResults = await performSemanticSearch(questionType, specificQuery, context, category);

    // 合并和去重结果
    const combinedResults = mergeAndDeduplicateResults(traditionalResults, semanticResults);

    // 按相关性重新排序
    const rankedResults = await rankResultsByRelevance(combinedResults, questionType, specificQuery, context);

    return rankedResults.slice(0, KNOWLEDGE_CONFIG.semanticSearchLimit);
  } catch (error) {
    console.error('获取相关知识失败:', error);
    // 降级到传统搜索
    const keywords = [specificQuery, questionType].filter(k => k).join(' ');
    return await searchKnowledge(keywords, categoryMap[questionType] || '', 3);
  }
}



/**
 * 构建增强搜索关键词
 * @param {string} questionType - 问题类型
 * @param {string} specificQuery - 具体查询
 * @param {object} context - 上下文信息
 * @returns {string} 增强关键词
 */
function buildEnhancedKeywords(questionType, specificQuery, context) {
  let keywords = [specificQuery, questionType];

  // 添加专业术语
  const professionalTerms = KNOWLEDGE_CONFIG.professionalTerms;
  const questionKeywords = KNOWLEDGE_CONFIG.questionKeywords[questionType] || [];

  keywords.push(...questionKeywords.slice(0, 3)); // 限制数量避免过度匹配

  // 根据上下文添加相关术语
  if (context.hexagram) {
    if (context.hexagram.method === 'meihua') {
      keywords.push(...professionalTerms['梅花易数'].slice(0, 2));
    } else {
      keywords.push(...professionalTerms['周易六爻'].slice(0, 2));
    }
  }

  if (context.bazi) {
    keywords.push(...professionalTerms['子平八字'].slice(0, 2));
  }

  if (context.ziwei) {
    keywords.push(...professionalTerms['紫微斗数'].slice(0, 2));
  }

  return keywords.filter(k => k && k.trim()).join(' ');
}

/**
 * 执行AI语义检索
 * @param {string} questionType - 问题类型
 * @param {string} specificQuery - 具体查询
 * @param {object} context - 上下文信息
 * @param {string} category - 分类
 * @returns {Promise<Array>} 语义检索结果
 */
async function performSemanticSearch(questionType, specificQuery, context, category) {
  try {
    // 构建语义查询提示词
    const semanticQuery = buildSemanticQuery(questionType, specificQuery, context);

    // 调用云函数进行语义检索
    const result = await wx.cloud.callFunction({
      name: 'knowledge-search',
      data: {
        action: 'semantic_search',
        query: semanticQuery,
        category: category,
        limit: 5
      }
    });

    return result.result.success ? result.result.data : [];
  } catch (error) {
    console.warn('AI语义检索失败，使用传统检索:', error);
    return [];
  }
}

/**
 * 构建语义查询提示词
 * @param {string} questionType - 问题类型
 * @param {string} specificQuery - 具体查询
 * @param {object} context - 上下文信息
 * @returns {string} 语义查询提示词
 */
function buildSemanticQuery(questionType, specificQuery, context) {
  let semanticQuery = `关于${questionType}的问题：${specificQuery}`;

  // 添加上下文信息
  if (context.hexagram) {
    semanticQuery += `，涉及卦象：${context.hexagram.name || ''}`;
  }

  if (context.bazi) {
    semanticQuery += `，涉及八字：${context.bazi.dayMaster || ''}日主`;
  }

  if (context.ziwei) {
    semanticQuery += `，涉及紫微斗数命盘`;
  }

  return semanticQuery;
}

/**
 * 合并和去重结果
 * @param {Array} traditionalResults - 传统搜索结果
 * @param {Array} semanticResults - 语义搜索结果
 * @returns {Array} 合并后的结果
 */
function mergeAndDeduplicateResults(traditionalResults, semanticResults) {
  const seen = new Set();
  const merged = [];

  // 先添加传统搜索结果
  for (const result of traditionalResults) {
    if (!seen.has(result._id)) {
      seen.add(result._id);
      merged.push({ ...result, source: 'traditional' });
    }
  }

  // 再添加语义搜索结果
  for (const result of semanticResults) {
    if (!seen.has(result._id)) {
      seen.add(result._id);
      merged.push({ ...result, source: 'semantic' });
    }
  }

  return merged;
}

/**
 * 按相关性重新排序结果
 * @param {Array} results - 搜索结果
 * @param {string} questionType - 问题类型
 * @param {string} specificQuery - 具体查询
 * @param {object} context - 上下文信息
 * @returns {Promise<Array>} 排序后的结果
 */
async function rankResultsByRelevance(results, questionType, specificQuery, context) {
  // 为每个结果计算相关性分数
  const scoredResults = results.map(result => {
    let score = 0;

    // 基础分数：来源权重
    score += result.source === 'semantic' ? 10 : 5;

    // 标题匹配分数
    const title = (result.title || '').toLowerCase();
    const query = specificQuery.toLowerCase();
    if (title.includes(query)) score += 15;
    if (title.includes(questionType)) score += 10;

    // 专业术语匹配分数
    const professionalTerms = KNOWLEDGE_CONFIG.professionalTerms;
    const questionKeywords = KNOWLEDGE_CONFIG.questionKeywords[questionType] || [];

    for (const term of questionKeywords) {
      if (title.includes(term) || (result.content || '').includes(term)) {
        score += 5;
      }
    }

    // 上下文相关性分数
    if (context.hexagram && (title.includes('卦') || title.includes('易'))) {
      score += 8;
    }
    if (context.bazi && (title.includes('八字') || title.includes('命理'))) {
      score += 8;
    }
    if (context.ziwei && (title.includes('紫微') || title.includes('斗数'))) {
      score += 8;
    }

    return { ...result, relevanceScore: score };
  });

  // 按分数排序
  return scoredResults.sort((a, b) => b.relevanceScore - a.relevanceScore);
}

module.exports = {
  initDatabase,
  searchKnowledge,
  getRelevantKnowledge,
  getKnowledgeStats,
  buildEnhancedKeywords,
  performSemanticSearch,
  buildSemanticQuery,
  mergeAndDeduplicateResults,
  rankResultsByRelevance,
  KNOWLEDGE_CONFIG
};
