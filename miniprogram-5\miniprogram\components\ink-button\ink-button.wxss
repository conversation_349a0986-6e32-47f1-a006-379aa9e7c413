/* components/ink-button/ink-button.wxss - 水墨风按钮样式 */

.ink-button {
  position: relative;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  border: none;
  outline: none;
  cursor: pointer;
  transition: all 0.3s ease;
  overflow: hidden;
  font-family: 'STSong', '华文宋体', serif;
  user-select: none;
}

/* 水墨渐变背景 */
.ink-bg {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  border-radius: inherit;
  transition: all 0.3s ease;
}

/* 按钮内容 */
.button-content {
  position: relative;
  z-index: 2;
  display: flex;
  align-items: center;
  justify-content: center;
}

.button-text {
  font-weight: 500;
  letter-spacing: 1rpx;
}

/* 按钮类型样式 */
.ink-button.primary .ink-bg {
  background: linear-gradient(135deg, #1a1a1a 0%, #333333 100%);
  box-shadow: 0 8rpx 24rpx rgba(26, 26, 26, 0.2);
}

.ink-button.primary .button-text {
  color: white;
}

.ink-button.secondary .ink-bg {
  background: linear-gradient(135deg, #666666 0%, #888888 100%);
  box-shadow: 0 6rpx 20rpx rgba(102, 102, 102, 0.15);
}

.ink-button.secondary .button-text {
  color: white;
}

.ink-button.ghost {
  border: 2rpx solid #1a1a1a;
}

.ink-button.ghost .ink-bg {
  background: transparent;
}

.ink-button.ghost .button-text {
  color: #1a1a1a;
}

.ink-button.danger .ink-bg {
  background: linear-gradient(135deg, #d32f2f 0%, #f44336 100%);
  box-shadow: 0 6rpx 20rpx rgba(211, 47, 47, 0.2);
}

.ink-button.danger .button-text {
  color: white;
}

.ink-button.success .ink-bg {
  background: linear-gradient(135deg, #2e7d32 0%, #4caf50 100%);
  box-shadow: 0 6rpx 20rpx rgba(46, 125, 50, 0.2);
}

.ink-button.success .button-text {
  color: white;
}

/* 按钮大小 */
.ink-button.large {
  height: 96rpx;
  padding: 0 48rpx;
  border-radius: 48rpx;
  font-size: 32rpx;
}

.ink-button.medium {
  height: 80rpx;
  padding: 0 40rpx;
  border-radius: 40rpx;
  font-size: 28rpx;
}

.ink-button.small {
  height: 64rpx;
  padding: 0 32rpx;
  border-radius: 32rpx;
  font-size: 24rpx;
}

/* 圆角样式 */
.ink-button.round {
  border-radius: 50%;
  width: 80rpx;
  height: 80rpx;
  padding: 0;
}

.ink-button.round.large {
  width: 96rpx;
  height: 96rpx;
}

.ink-button.round.small {
  width: 64rpx;
  height: 64rpx;
}

/* 按下状态 */
.ink-button.pressed .ink-bg {
  transform: scale(0.95);
  opacity: 0.8;
}

/* 禁用状态 */
.ink-button.disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.ink-button.disabled .ink-bg {
  background: #cccccc !important;
  box-shadow: none !important;
}

/* 成功按钮禁用状态保持绿色但降低透明度 */
.ink-button.success.disabled .ink-bg {
  background: linear-gradient(135deg, #2e7d32 0%, #4caf50 100%) !important;
  box-shadow: 0 6rpx 20rpx rgba(46, 125, 50, 0.1) !important;
}

/* 加载状态 */
.loading-icon {
  margin-right: 16rpx;
}

.ink-spinner {
  display: flex;
  align-items: center;
  gap: 8rpx;
}

.spinner-dot {
  width: 8rpx;
  height: 8rpx;
  background: currentColor;
  border-radius: 50%;
  animation: inkSpinner 1.4s ease-in-out infinite both;
}

.spinner-dot:nth-child(1) { animation-delay: -0.32s; }
.spinner-dot:nth-child(2) { animation-delay: -0.16s; }

@keyframes inkSpinner {
  0%, 80%, 100% {
    transform: scale(0);
    opacity: 0.5;
  }
  40% {
    transform: scale(1);
    opacity: 1;
  }
}

/* 水墨涟漪效果 */
.ink-ripple {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.3);
  transform: translate(-50%, -50%);
  animation: inkRipple 0.6s ease-out;
}

@keyframes inkRipple {
  to {
    width: 200rpx;
    height: 200rpx;
    opacity: 0;
  }
}
