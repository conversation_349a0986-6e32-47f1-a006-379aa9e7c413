// 时间预测引擎
// 基于八字大运流年和知识库的具体时间预测系统

// 导入相关模块
const { calculateDayun, getCurrentDayun, getCurrentLiunian } = require('./bazi-luck.js');

// 天干地支
const HEAVENLY_STEMS = ['甲', '乙', '丙', '丁', '戊', '己', '庚', '辛', '壬', '癸'];
const EARTHLY_BRANCHES = ['子', '丑', '寅', '卯', '辰', '巳', '午', '未', '申', '酉', '戌', '亥'];

// 五行属性
const STEM_ELEMENTS = {
  '甲': '木', '乙': '木', '丙': '火', '丁': '火', '戊': '土', 
  '己': '土', '庚': '金', '辛': '金', '壬': '水', '癸': '水'
};

const BRANCH_ELEMENTS = {
  '子': '水', '丑': '土', '寅': '木', '卯': '木', '辰': '土', '巳': '火',
  '午': '火', '未': '土', '申': '金', '酉': '金', '戌': '土', '亥': '水'
};

// 季节对应
const SEASON_BRANCHES = {
  '春': ['寅', '卯', '辰'], '夏': ['巳', '午', '未'],
  '秋': ['申', '酉', '戌'], '冬': ['亥', '子', '丑']
};

// 事件类型与六亲对应
const EVENT_TYPES = {
  '财运': '妻财', '投资': '妻财', '赚钱': '妻财', '收入': '妻财',
  '事业': '官鬼', '工作': '官鬼', '升职': '官鬼', '官运': '官鬼',
  '学业': '父母', '考试': '父母', '学习': '父母', '文书': '父母',
  '健康': '日主', '疾病': '官鬼', '身体': '日主', '医疗': '父母',
  '婚姻': '妻财', '感情': '妻财', '结婚': '妻财', '恋爱': '妻财',
  '子女': '子孙', '生育': '子孙', '怀孕': '子孙', '孩子': '子孙',
  '朋友': '兄弟', '合伙': '兄弟', '竞争': '兄弟', '同事': '兄弟'
};

/**
 * 预测具体时间点
 * @param {Object} bazi - 八字信息
 * @param {string} eventType - 事件类型
 * @param {Object} currentInfo - 当前信息（年龄、年份等）
 * @param {Object} lifeHexagram - 终身卦（可选）
 * @returns {Object} 时间预测结果
 */
function predictSpecificTimes(bazi, eventType, currentInfo, lifeHexagram = null) {
  console.log('🔮 开始时间预测:', { eventType, currentInfo });

  const { currentAge, currentYear, isMale } = currentInfo;
  
  // 计算大运流年
  const dayunList = calculateDayun(bazi, { startAge: 8, startYear: currentYear - currentAge + 8 });
  
  // 分析过去事件（验证准确性）
  const pastEvents = analyzePastEvents(bazi, eventType, currentAge, currentYear, dayunList);
  
  // 预测未来事件
  const futureEvents = predictFutureEvents(bazi, eventType, currentAge, currentYear, dayunList, lifeHexagram);
  
  return {
    eventType: eventType,
    currentInfo: currentInfo,
    pastAnalysis: pastEvents,
    futurePredictons: futureEvents,
    confidence: calculateConfidence(pastEvents, futureEvents, lifeHexagram),
    summary: generatePredictionSummary(pastEvents, futureEvents, eventType)
  };
}

/**
 * 分析过去事件（用于验证）
 * @param {Object} bazi - 八字信息
 * @param {string} eventType - 事件类型
 * @param {number} currentAge - 当前年龄
 * @param {number} currentYear - 当前年份
 * @param {Array} dayunList - 大运列表
 * @returns {Array} 过去事件分析
 */
function analyzePastEvents(bazi, eventType, currentAge, currentYear, dayunList) {
  const pastEvents = [];
  const targetSixQin = EVENT_TYPES[eventType] || '日主';
  
  // 分析过去10年的关键时间点
  for (let yearsAgo = 1; yearsAgo <= 10; yearsAgo++) {
    const targetYear = currentYear - yearsAgo;
    const targetAge = currentAge - yearsAgo;
    
    if (targetAge < 0) continue;
    
    // 获取当时的大运
    const dayun = getCurrentDayun(dayunList, targetAge);
    if (!dayun) continue;
    
    // 计算流年干支
    const liunianGanzhi = calculateLiunianGanzhi(targetYear);
    
    // 分析该年的运势强度
    const strength = analyzeYearStrength(bazi, dayun, liunianGanzhi, targetSixQin);
    
    if (strength.score >= 75) {
      const season = predictSeason(bazi, liunianGanzhi, eventType);
      pastEvents.push({
        year: targetYear,
        age: targetAge,
        season: season,
        dayun: dayun.ganzhi,
        liunian: liunianGanzhi,
        strength: strength.score,
        description: `${targetYear}年${season}，${strength.analysis}`,
        probability: `${strength.score}%`,
        reasoning: strength.reasoning
      });
    }
  }
  
  return pastEvents.slice(0, 3); // 最多返回3个过去事件
}

/**
 * 预测未来事件
 * @param {Object} bazi - 八字信息
 * @param {string} eventType - 事件类型
 * @param {number} currentAge - 当前年龄
 * @param {number} currentYear - 当前年份
 * @param {Array} dayunList - 大运列表
 * @param {Object} lifeHexagram - 终身卦
 * @returns {Array} 未来事件预测
 */
function predictFutureEvents(bazi, eventType, currentAge, currentYear, dayunList, lifeHexagram) {
  const futureEvents = [];
  const targetSixQin = EVENT_TYPES[eventType] || '日主';
  
  // 预测未来10年的关键时间点
  for (let yearsLater = 1; yearsLater <= 10; yearsLater++) {
    const targetYear = currentYear + yearsLater;
    const targetAge = currentAge + yearsLater;
    
    // 获取未来的大运
    const dayun = getCurrentDayun(dayunList, targetAge);
    if (!dayun) continue;
    
    // 计算流年干支
    const liunianGanzhi = calculateLiunianGanzhi(targetYear);
    
    // 分析该年的运势强度
    const strength = analyzeYearStrength(bazi, dayun, liunianGanzhi, targetSixQin);
    
    // 结合终身卦进行调整
    if (lifeHexagram) {
      strength.score = adjustWithHexagram(strength.score, lifeHexagram, targetYear);
    }
    
    if (strength.score >= 70) {
      const season = predictSeason(bazi, liunianGanzhi, eventType);
      futureEvents.push({
        year: targetYear,
        age: targetAge,
        season: season,
        dayun: dayun.ganzhi,
        liunian: liunianGanzhi,
        strength: strength.score,
        description: `${targetYear}年${season}，${strength.analysis}`,
        probability: `${strength.score}%`,
        reasoning: strength.reasoning,
        hexagramAdjustment: lifeHexagram ? '已结合终身卦调整' : '未使用终身卦'
      });
    }
  }
  
  return futureEvents.slice(0, 3); // 最多返回3个未来事件
}

/**
 * 分析年份运势强度
 * @param {Object} bazi - 八字信息
 * @param {Object} dayun - 大运
 * @param {string} liunianGanzhi - 流年干支
 * @param {string} targetSixQin - 目标六亲
 * @returns {Object} 强度分析
 */
function analyzeYearStrength(bazi, dayun, liunianGanzhi, targetSixQin) {
  let score = 50; // 基础分
  let reasoning = [];
  
  const liunianStem = liunianGanzhi[0];
  const liunianBranch = liunianGanzhi[1];
  const dayunStem = dayun.stem;
  const dayunBranch = dayun.branch;
  
  // 分析流年与日主的关系
  const dayMaster = bazi.day.stem;
  const dayMasterElement = STEM_ELEMENTS[dayMaster];
  const liunianElement = STEM_ELEMENTS[liunianStem];
  
  // 五行生克关系分析
  if (isElementSupporting(liunianElement, dayMasterElement)) {
    score += 15;
    reasoning.push(`流年${liunianStem}${liunianElement}生助日主${dayMaster}${dayMasterElement}`);
  } else if (isElementConflicting(liunianElement, dayMasterElement)) {
    score -= 10;
    reasoning.push(`流年${liunianStem}${liunianElement}克制日主${dayMaster}${dayMasterElement}`);
  }
  
  // 大运与流年的配合
  if (dayunStem === liunianStem || dayunBranch === liunianBranch) {
    score += 20;
    reasoning.push(`大运${dayun.ganzhi}与流年${liunianGanzhi}天地同气`);
  }
  
  // 地支三合、六合、冲克分析
  const branchRelation = analyzeBranchRelation(dayunBranch, liunianBranch, bazi);
  score += branchRelation.score;
  if (branchRelation.description) {
    reasoning.push(branchRelation.description);
  }
  
  // 特殊格局加分
  if (isSpecialPattern(bazi, dayun, liunianGanzhi)) {
    score += 25;
    reasoning.push('形成特殊格局，运势大增');
  }
  
  const analysis = score >= 80 ? '运势极佳' : 
                  score >= 70 ? '运势良好' : 
                  score >= 60 ? '运势平稳' : '运势一般';
  
  return {
    score: Math.min(100, Math.max(0, score)),
    analysis: analysis,
    reasoning: reasoning.join('；')
  };
}

/**
 * 预测具体季节
 * @param {Object} bazi - 八字信息
 * @param {string} liunianGanzhi - 流年干支
 * @param {string} eventType - 事件类型
 * @returns {string} 季节预测
 */
function predictSeason(bazi, liunianGanzhi, eventType) {
  const liunianBranch = liunianGanzhi[1];
  
  // 根据流年地支确定主要季节
  for (const [season, branches] of Object.entries(SEASON_BRANCHES)) {
    if (branches.includes(liunianBranch)) {
      return season;
    }
  }
  
  return '年中';
}

/**
 * 计算流年干支
 * @param {number} year - 年份
 * @returns {string} 干支
 */
function calculateLiunianGanzhi(year) {
  const stemIndex = (year - 4) % 10; // 甲子年为公元4年
  const branchIndex = (year - 4) % 12;
  return HEAVENLY_STEMS[stemIndex] + EARTHLY_BRANCHES[branchIndex];
}

/**
 * 五行相生判断
 */
function isElementSupporting(element1, element2) {
  const supportRelations = {
    '木': '火', '火': '土', '土': '金', '金': '水', '水': '木'
  };
  return supportRelations[element1] === element2;
}

/**
 * 五行相克判断
 */
function isElementConflicting(element1, element2) {
  const conflictRelations = {
    '木': '土', '土': '水', '水': '火', '火': '金', '金': '木'
  };
  return conflictRelations[element1] === element2;
}

/**
 * 分析地支关系
 */
function analyzeBranchRelation(branch1, branch2, bazi) {
  // 简化的地支关系分析
  const relations = {
    '子丑': { score: 5, description: '子丑合土' },
    '寅亥': { score: 5, description: '寅亥合木' },
    '卯戌': { score: 5, description: '卯戌合火' },
    '辰酉': { score: 5, description: '辰酉合金' },
    '巳申': { score: 5, description: '巳申合水' },
    '午未': { score: 5, description: '午未合火' }
  };
  
  const key1 = branch1 + branch2;
  const key2 = branch2 + branch1;
  
  return relations[key1] || relations[key2] || { score: 0, description: '' };
}

/**
 * 判断特殊格局
 */
function isSpecialPattern(bazi, dayun, liunianGanzhi) {
  // 简化的特殊格局判断
  // 实际应该包含更复杂的格局分析
  return false;
}

/**
 * 结合终身卦调整预测
 */
function adjustWithHexagram(originalScore, lifeHexagram, targetYear) {
  if (!lifeHexagram || !lifeHexagram.analysis) {
    return originalScore;
  }
  
  // 简化的卦象调整逻辑
  const hexagramBonus = lifeHexagram.analysis.personality.mainTrait.includes('进取') ? 5 : 0;
  return Math.min(100, originalScore + hexagramBonus);
}

/**
 * 计算预测可信度
 */
function calculateConfidence(pastEvents, futureEvents, lifeHexagram) {
  let confidence = 60; // 基础可信度
  
  // 过去事件验证加分
  confidence += pastEvents.length * 10;
  
  // 终身卦验证加分
  if (lifeHexagram) {
    confidence += 15;
  }
  
  // 未来事件数量调整
  if (futureEvents.length > 0) {
    confidence += 10;
  }
  
  return Math.min(95, confidence);
}

/**
 * 生成预测总结
 */
function generatePredictionSummary(pastEvents, futureEvents, eventType) {
  let summary = `关于${eventType}的时间预测分析：\n\n`;
  
  if (pastEvents.length > 0) {
    summary += '【过去验证】\n';
    pastEvents.forEach(event => {
      summary += `${event.description}，概率${event.probability}\n`;
    });
    summary += '\n';
  }
  
  if (futureEvents.length > 0) {
    summary += '【未来预测】\n';
    futureEvents.forEach(event => {
      summary += `${event.description}，概率${event.probability}\n`;
    });
  } else {
    summary += '【未来预测】\n近期暂无明显的有利时机，建议耐心等待。\n';
  }
  
  return summary;
}

module.exports = {
  predictSpecificTimes,
  analyzePastEvents,
  predictFutureEvents,
  analyzeYearStrength,
  predictSeason,
  calculateLiunianGanzhi,
  generatePredictionSummary
};
