// 六爻系统数据文件
// 基于传统六爻理论整理

// 64卦的世应位置
const WORLD_RESPONSE_POSITIONS = {
  1: { world: 6, response: 3 },   // 乾为天
  2: { world: 1, response: 4 },   // 坤为地
  3: { world: 2, response: 5 },   // 水雷屯
  4: { world: 3, response: 6 },   // 山水蒙
  5: { world: 4, response: 1 },   // 水天需
  6: { world: 5, response: 2 },   // 天水讼
  7: { world: 1, response: 4 },   // 地水师
  8: { world: 2, response: 5 },   // 水地比
  // ... 其他卦的世应位置需要根据传统六爻理论补充
};

// 天干地支
const HEAVENLY_STEMS = ['甲', '乙', '丙', '丁', '戊', '己', '庚', '辛', '壬', '癸'];
const EARTHLY_BRANCHES = ['子', '丑', '寅', '卯', '辰', '巳', '午', '未', '申', '酉', '戌', '亥'];

// 五行属性
const FIVE_ELEMENTS_MAPPING = {
  '甲': '木', '乙': '木',
  '丙': '火', '丁': '火',
  '戊': '土', '己': '土',
  '庚': '金', '辛': '金',
  '壬': '水', '癸': '水',
  '子': '水', '亥': '水',
  '寅': '木', '卯': '木',
  '巳': '火', '午': '火',
  '申': '金', '酉': '金',
  '辰': '土', '戌': '土', '丑': '土', '未': '土'
};

// 六神（青龙、朱雀、勾陈、螣蛇、白虎、玄武）
const SIX_SPIRITS = ['青龙', '朱雀', '勾陈', '螣蛇', '白虎', '玄武'];

// 六亲关系（父母、兄弟、子孙、妻财、官鬼）
const SIX_RELATIVES = {
  '生我': '父母',
  '我生': '子孙', 
  '同我': '兄弟',
  '我克': '妻财',
  '克我': '官鬼'
};

// 八卦对应的地支
const TRIGRAM_BRANCHES = {
  1: ['戌', '申', '午'], // 乾卦：戌土、申金、午火
  2: ['丑', '亥', '酉'], // 兑卦：丑土、亥水、酉金
  3: ['卯', '丑', '亥'], // 离卦：卯木、丑土、亥水
  4: ['辰', '寅', '子'], // 震卦：辰土、寅木、子水
  5: ['巳', '卯', '丑'], // 巽卦：巳火、卯木、丑土
  6: ['午', '辰', '寅'], // 坎卦：午火、辰土、寅木
  7: ['未', '巳', '卯'], // 艮卦：未土、巳火、卯木
  8: ['申', '午', '辰']  // 坤卦：申金、午火、辰土
};

// 根据六爻生成装卦信息
function generateLiuyaoInfo(originalLines, question, time) {
  try {
    // 验证输入参数
    if (!originalLines || originalLines.length !== 6) {
      console.error('❌ 六爻数据无效:', originalLines);
      // 返回默认的乾卦数据
      originalLines = [1, 1, 1, 1, 1, 1];
    }

    // 将六爻分为上下卦
    const lowerTrigram = getLowerTrigram(originalLines);
    const upperTrigram = getUpperTrigram(originalLines);

    // 获取卦名
    const hexagramNumber = getHexagramNumber(upperTrigram, lowerTrigram);
    const hexagramName = getHexagramName(hexagramNumber);

    // 装地支
    const branches = assignBranches(upperTrigram, lowerTrigram);

    // 确定世应
    const worldResponse = getWorldResponse(hexagramNumber);

    // 装六神
    const spirits = assignSpirits(time);

    // 装六亲
    const relatives = assignRelatives(branches, upperTrigram, lowerTrigram);

    // 计算变卦（如果有动爻）
    const changedHexagram = calculateChangedHexagram(originalLines, hexagramNumber);

    // 计算互卦
    const mutualHexagram = calculateMutualHexagram(originalLines);

    // 计算空亡
    const voidBranches = calculateVoidBranches(time);

    const result = {
      hexagramNumber: hexagramNumber || 1,
      hexagramName: hexagramName || '乾为天',
      upperTrigram: upperTrigram || 1,
      lowerTrigram: lowerTrigram || 1,
      branches: branches || ['子', '寅', '辰', '午', '申', '戌'],
      worldResponse: worldResponse || { world: 6, response: 3 },
      spirits: spirits || ['青龙', '朱雀', '勾陈', '螣蛇', '白虎', '玄武'],
      relatives: relatives || ['兄弟', '子孙', '妻财', '官鬼', '父母', '兄弟'],
      originalLines: originalLines,
      changedHexagram: changedHexagram,
      mutualHexagram: mutualHexagram,
      voidBranches: voidBranches
    };

    console.log('✅ 六爻装卦完成:', result.hexagramName);
    return result;
  } catch (error) {
    console.error('❌ 生成六爻信息失败:', error);
    // 返回默认的乾卦数据
    return {
      hexagramNumber: 1,
      hexagramName: '乾为天',
      upperTrigram: 1,
      lowerTrigram: 1,
      branches: ['子', '寅', '辰', '午', '申', '戌'],
      worldResponse: { world: 6, response: 3 },
      spirits: ['青龙', '朱雀', '勾陈', '螣蛇', '白虎', '玄武'],
      relatives: ['兄弟', '子孙', '妻财', '官鬼', '父母', '兄弟'],
      originalLines: [1, 1, 1, 1, 1, 1]
    };
  }
}

// 获取下卦
function getLowerTrigram(lines) {
  const lowerLines = lines.slice(0, 3);
  return linesToTrigram(lowerLines);
}

// 获取上卦
function getUpperTrigram(lines) {
  const upperLines = lines.slice(3, 6);
  return linesToTrigram(upperLines);
}

// 将三爻转换为八卦
function linesToTrigram(lines) {
  const key = lines.join('');
  const trigramMap = {
    '111': 1, // 乾
    '011': 2, // 兑
    '101': 3, // 离
    '001': 4, // 震
    '110': 5, // 巽
    '010': 6, // 坎
    '100': 7, // 艮
    '000': 8  // 坤
  };
  return trigramMap[key] || 1;
}

// 获取卦序号
function getHexagramNumber(upperTrigram, lowerTrigram) {
  // 简化版本，实际需要完整的64卦对照表
  return (upperTrigram - 1) * 8 + lowerTrigram;
}

// 获取卦名
function getHexagramName(hexagramNumber) {
  // 完整的64卦名称对照表
  const names = {
    1: '乾为天', 2: '天泽履', 3: '天火同人', 4: '天雷无妄', 5: '天水讼', 6: '天山遁', 7: '天地否', 8: '天风姤',
    9: '泽天夬', 10: '泽泽兑', 11: '泽火革', 12: '泽雷随', 13: '泽水困', 14: '泽山咸', 15: '泽地萃', 16: '泽风大过',
    17: '火天大有', 18: '火泽睽', 19: '火火离', 20: '火雷噬嗑', 21: '火水未济', 22: '火山贲', 23: '火地晋', 24: '火风鼎',
    25: '雷天大壮', 26: '雷泽归妹', 27: '雷火丰', 28: '雷雷震', 29: '雷水解', 30: '雷山小过', 31: '雷地豫', 32: '雷风恒',
    33: '水天需', 34: '水泽节', 35: '水火既济', 36: '水雷屯', 37: '水水坎', 38: '水山蹇', 39: '水地师', 40: '水风井',
    41: '山天大畜', 42: '山泽损', 43: '山火贲', 44: '山雷颐', 45: '山水蒙', 46: '山山艮', 47: '山地剥', 48: '山风蛊',
    49: '地天泰', 50: '地泽临', 51: '地火明夷', 52: '地雷复', 53: '地水比', 54: '地山谦', 55: '地地坤', 56: '地风升',
    57: '风天小畜', 58: '风泽中孚', 59: '风火家人', 60: '风雷益', 61: '风水涣', 62: '风山渐', 63: '风地观', 64: '风风巽'
  };
  return names[hexagramNumber] || `第${hexagramNumber}卦`;
}

// 装地支
function assignBranches(upperTrigram, lowerTrigram) {
  const upperBranches = TRIGRAM_BRANCHES[upperTrigram];
  const lowerBranches = TRIGRAM_BRANCHES[lowerTrigram];
  
  // 六爻从下往上：初爻、二爻、三爻、四爻、五爻、上爻
  return [
    lowerBranches[0], // 初爻
    lowerBranches[1], // 二爻  
    lowerBranches[2], // 三爻
    upperBranches[0], // 四爻
    upperBranches[1], // 五爻
    upperBranches[2]  // 上爻
  ];
}

// 确定世应
function getWorldResponse(hexagramNumber) {
  // 简化版本，实际需要根据完整的六爻理论确定
  return WORLD_RESPONSE_POSITIONS[hexagramNumber] || { world: 6, response: 3 };
}

// 装六神
function assignSpirits(time) {
  // 根据起卦日的天干确定六神顺序
  const date = new Date(time);
  const dayIndex = date.getDay(); // 简化处理
  const startSpirit = dayIndex % 6;
  
  const spirits = [];
  for (let i = 0; i < 6; i++) {
    spirits.push(SIX_SPIRITS[(startSpirit + i) % 6]);
  }
  
  return spirits;
}

// 装六亲
function assignRelatives(branches, upperTrigram, lowerTrigram) {
  // 以下卦为主，确定六亲关系
  const mainElement = getTrigramElement(lowerTrigram);
  
  return branches.map(branch => {
    const branchElement = FIVE_ELEMENTS_MAPPING[branch];
    return getRelativeType(mainElement, branchElement);
  });
}

// 获取八卦五行属性
function getTrigramElement(trigram) {
  const elementMap = {
    1: '金', 2: '金', // 乾、兑
    3: '火',          // 离
    4: '木', 5: '木', // 震、巽
    6: '水',          // 坎
    7: '土', 8: '土'  // 艮、坤
  };
  return elementMap[trigram];
}

// 确定六亲关系
function getRelativeType(mainElement, branchElement) {
  const fiveElementsRelation = {
    '木': { '木': '兄弟', '火': '子孙', '土': '妻财', '金': '官鬼', '水': '父母' },
    '火': { '火': '兄弟', '土': '子孙', '金': '妻财', '水': '官鬼', '木': '父母' },
    '土': { '土': '兄弟', '金': '子孙', '水': '妻财', '木': '官鬼', '火': '父母' },
    '金': { '金': '兄弟', '水': '子孙', '木': '妻财', '火': '官鬼', '土': '父母' },
    '水': { '水': '兄弟', '木': '子孙', '火': '妻财', '土': '官鬼', '金': '父母' }
  };
  
  return fiveElementsRelation[mainElement]?.[branchElement] || '兄弟';
}

// 分析六爻卦象
function analyzeLiuyao(liuyaoInfo, changingYaos, question) {
  const analysis = {
    hexagramName: liuyaoInfo.hexagramName,
    worldResponse: liuyaoInfo.worldResponse,
    changingYaos: changingYaos,
    keyPoints: [],
    useGod: '未确定' // 添加用神字段
  };

  // 分析世爻
  const worldYao = liuyaoInfo.worldResponse.world;
  const worldBranch = liuyaoInfo.branches[worldYao - 1];
  const worldRelative = liuyaoInfo.relatives[worldYao - 1];
  const worldSpirit = liuyaoInfo.spirits[worldYao - 1];

  analysis.keyPoints.push(`世爻在第${worldYao}爻，${worldBranch}${worldRelative}，${worldSpirit}持世`);

  // 分析应爻
  const responseYao = liuyaoInfo.worldResponse.response;
  const responseBranch = liuyaoInfo.branches[responseYao - 1];
  const responseRelative = liuyaoInfo.relatives[responseYao - 1];
  const responseSpirit = liuyaoInfo.spirits[responseYao - 1];

  analysis.keyPoints.push(`应爻在第${responseYao}爻，${responseBranch}${responseRelative}，${responseSpirit}持应`);

  // 🎯 根据问题确定用神
  analysis.useGod = determineUseGod(question, liuyaoInfo);

  // 分析动爻
  if (changingYaos.length > 0) {
    changingYaos.forEach(yao => {
      const yaoBranch = liuyaoInfo.branches[yao - 1];
      const yaoRelative = liuyaoInfo.relatives[yao - 1];
      const yaoSpirit = liuyaoInfo.spirits[yao - 1];
      analysis.keyPoints.push(`第${yao}爻动，${yaoBranch}${yaoRelative}发动，${yaoSpirit}主事`);
    });
  } else {
    analysis.keyPoints.push('卦无动爻，以静卦断之');
  }

  return analysis;
}

// 🎯 根据问题类型确定用神
function determineUseGod(question, liuyaoInfo) {
  if (!question) return '未确定';

  const questionLower = question.toLowerCase();

  // 财运/投资问题 - 用神为妻财爻
  if (questionLower.includes('财') || questionLower.includes('钱') ||
      questionLower.includes('投资') || questionLower.includes('赚') ||
      questionLower.includes('收入') || questionLower.includes('经济')) {
    const caiYao = findYaoByRelative(liuyaoInfo, '妻财');
    return caiYao ? `妻财爻（第${caiYao}爻）` : '妻财爻不现';
  }

  // 事业/工作问题 - 用神为官鬼爻
  if (questionLower.includes('工作') || questionLower.includes('事业') ||
      questionLower.includes('升职') || questionLower.includes('跳槽') ||
      questionLower.includes('官') || questionLower.includes('职')) {
    const guanYao = findYaoByRelative(liuyaoInfo, '官鬼');
    return guanYao ? `官鬼爻（第${guanYao}爻）` : '官鬼爻不现';
  }

  // 学业/考试问题 - 用神为父母爻
  if (questionLower.includes('学') || questionLower.includes('考') ||
      questionLower.includes('读书') || questionLower.includes('文凭') ||
      questionLower.includes('教育') || questionLower.includes('毕业')) {
    const fumuYao = findYaoByRelative(liuyaoInfo, '父母');
    return fumuYao ? `父母爻（第${fumuYao}爻）` : '父母爻不现';
  }

  // 子女/生育问题 - 用神为子孙爻
  if (questionLower.includes('子女') || questionLower.includes('孩子') ||
      questionLower.includes('生育') || questionLower.includes('怀孕') ||
      questionLower.includes('小孩') || questionLower.includes('儿')) {
    const ziYao = findYaoByRelative(liuyaoInfo, '子孙');
    return ziYao ? `子孙爻（第${ziYao}爻）` : '子孙爻不现';
  }

  // 合伙/朋友问题 - 用神为兄弟爻
  if (questionLower.includes('合伙') || questionLower.includes('朋友') ||
      questionLower.includes('兄弟') || questionLower.includes('姐妹') ||
      questionLower.includes('同事') || questionLower.includes('伙伴')) {
    const xiongdiYao = findYaoByRelative(liuyaoInfo, '兄弟');
    return xiongdiYao ? `兄弟爻（第${xiongdiYao}爻）` : '兄弟爻不现';
  }

  // 婚姻/感情问题 - 男测妻财，女测官鬼（这里默认用妻财）
  if (questionLower.includes('婚') || questionLower.includes('恋') ||
      questionLower.includes('感情') || questionLower.includes('爱情') ||
      questionLower.includes('配偶') || questionLower.includes('对象')) {
    const caiYao = findYaoByRelative(liuyaoInfo, '妻财');
    return caiYao ? `妻财爻（第${caiYao}爻）` : '妻财爻不现';
  }

  // 默认情况 - 以世爻为用神
  const worldYao = liuyaoInfo.worldResponse.world;
  return `世爻（第${worldYao}爻）`;
}

// 根据六亲找到对应的爻位
function findYaoByRelative(liuyaoInfo, targetRelative) {
  for (let i = 0; i < 6; i++) {
    if (liuyaoInfo.relatives[i] === targetRelative) {
      return i + 1; // 返回爻位（1-6）
    }
  }
  return null;
}

// 计算变卦
function calculateChangedHexagram(originalLines, hexagramNumber) {
  // 这里需要检查是否有动爻，如果有则计算变卦
  // 暂时返回基本信息，后续可以完善
  return {
    number: hexagramNumber,
    name: getHexagramName(hexagramNumber) + '(变卦待计算)'
  };
}

// 计算互卦
function calculateMutualHexagram(originalLines) {
  // 互卦取2、3、4爻为下卦，3、4、5爻为上卦
  if (originalLines.length !== 6) return null;

  const lowerMutual = [originalLines[1], originalLines[2], originalLines[3]]; // 2、3、4爻
  const upperMutual = [originalLines[2], originalLines[3], originalLines[4]]; // 3、4、5爻

  const lowerTrigram = linesToTrigram(lowerMutual);
  const upperTrigram = linesToTrigram(upperMutual);
  const mutualNumber = getHexagramNumber(upperTrigram, lowerTrigram);

  return {
    number: mutualNumber,
    name: getHexagramName(mutualNumber),
    upperTrigram: upperTrigram,
    lowerTrigram: lowerTrigram
  };
}

// 计算空亡
function calculateVoidBranches(time) {
  // 根据日干支计算空亡
  // 这是一个简化版本，实际应该根据具体的日干支计算
  const voidPairs = [
    ['戌', '亥'], ['申', '酉'], ['午', '未'], ['辰', '巳'], ['寅', '卯'], ['子', '丑']
  ];

  // 暂时返回一个固定的空亡，后续可以根据实际时间计算
  const dayIndex = Math.floor(Date.now() / (1000 * 60 * 60 * 24)) % 6;
  return voidPairs[dayIndex];
}

module.exports = {
  WORLD_RESPONSE_POSITIONS,
  HEAVENLY_STEMS,
  EARTHLY_BRANCHES,
  FIVE_ELEMENTS_MAPPING,
  SIX_SPIRITS,
  SIX_RELATIVES,
  TRIGRAM_BRANCHES,
  generateLiuyaoInfo,
  analyzeLiuyao,
  calculateChangedHexagram,
  calculateMutualHexagram,
  calculateVoidBranches
};
