// 八字大运流年系统
// 基于传统子平八字理论

const { HEAVENLY_STEMS, EARTHLY_BRANCHES, YIN_YANG } = require('./bazi-calculator.js');
const { calculateTenGod } = require('./bazi-analysis.js');

// 二十四节气精确计算（简化版，用于起运计算）
const SOLAR_TERMS = {
  1: { name: '立春', approxDay: 4 },
  2: { name: '惊蛰', approxDay: 5 },
  3: { name: '清明', approxDay: 5 },
  4: { name: '立夏', approxDay: 5 },
  5: { name: '芒种', approxDay: 6 },
  6: { name: '小暑', approxDay: 7 },
  7: { name: '立秋', approxDay: 7 },
  8: { name: '白露', approxDay: 8 },
  9: { name: '寒露', approxDay: 8 },
  10: { name: '立冬', approxDay: 7 },
  11: { name: '大雪', approxDay: 7 },
  12: { name: '小寒', approxDay: 6 }
};

/**
 * 获取指定月份的节气信息
 * @param {number} year - 年份
 * @param {number} month - 月份
 * @returns {Object} 节气信息
 */
function getSolarTermsForMonth(year, month) {
  const solarTerm = SOLAR_TERMS[month];
  if (!solarTerm) {
    // 处理跨年情况
    const adjustedMonth = month > 12 ? month - 12 : (month < 1 ? month + 12 : month);
    const adjustedYear = month > 12 ? year + 1 : (month < 1 ? year - 1 : year);
    return getSolarTermsForMonth(adjustedYear, adjustedMonth);
  }

  // 简化的节气日期计算（实际应该更精确）
  const solarTermDate = new Date(year, month - 1, solarTerm.approxDay);

  return {
    current: {
      name: solarTerm.name,
      date: solarTermDate
    }
  };
}

/**
 * 精确计算起运时间（基于传统子平八字理论）
 * @param {Object} bazi - 八字信息
 * @param {boolean} isMale - 是否为男性
 * @returns {Object} 起运信息
 */
function calculateStartLuck(bazi, isMale) {
  console.log('🎯 [起运计算] 开始精确计算起运时间...');
  console.log('🎯 [起运计算] 输入参数:', {
    birthDate: bazi.birthDate,
    isMale,
    yearStem: bazi.year.stem
  });

  const birthDate = bazi.birthDate;
  const birthYear = birthDate.getFullYear();
  const birthMonth = birthDate.getMonth() + 1;
  const birthDay = birthDate.getDate();
  const yearStem = bazi.year.stem;
  const yearYinYang = YIN_YANG[yearStem];

  console.log('🎯 [起运计算] 年干阴阳:', yearStem, yearYinYang);

  // 阳男阴女顺行，阴男阳女逆行（传统起运法则）
  const isForward = (isMale && yearYinYang === '阳') || (!isMale && yearYinYang === '阴');
  console.log('🎯 [起运计算] 起运方向:', isForward ? '顺行' : '逆行');

  // 获取当月的节气信息
  const solarTerms = getSolarTermsForMonth(birthYear, birthMonth);
  console.log('🎯 [起运计算] 当月节气:', solarTerms);

  // 计算到目标节气的天数差
  let daysDiff;
  let targetSolarTerm;
  let targetDate;

  if (isForward) {
    // 阳男阴女顺行：数至未来节（下一个节气）
    if (birthDate < solarTerms.current.date) {
      // 出生在当月节气之前，目标是当月节气
      targetSolarTerm = solarTerms.current;
      targetDate = solarTerms.current.date;
    } else {
      // 出生在当月节气之后，目标是下月节气
      const nextMonthTerms = getSolarTermsForMonth(birthYear, birthMonth === 12 ? 1 : birthMonth + 1);
      targetSolarTerm = nextMonthTerms.current;
      targetDate = nextMonthTerms.current.date;
    }
    daysDiff = Math.ceil((targetDate - birthDate) / (1000 * 60 * 60 * 24));
  } else {
    // 阴男阳女逆行：数至上一个节（上一个节气）
    if (birthDate > solarTerms.current.date) {
      // 出生在当月节气之后，目标是当月节气
      targetSolarTerm = solarTerms.current;
      targetDate = solarTerms.current.date;
      daysDiff = Math.ceil((birthDate - targetDate) / (1000 * 60 * 60 * 24));
    } else {
      // 出生在当月节气之前，目标是上月节气
      const prevMonthTerms = getSolarTermsForMonth(birthYear, birthMonth === 1 ? 12 : birthMonth - 1);
      targetSolarTerm = prevMonthTerms.current;
      targetDate = prevMonthTerms.current.date;
      daysDiff = Math.ceil((birthDate - targetDate) / (1000 * 60 * 60 * 24));
    }
  }

  console.log('🎯 [起运计算] 目标节气:', targetSolarTerm.name, targetDate);
  console.log('🎯 [起运计算] 天数差:', daysDiff);

  // 传统起运算法：三天为一年，一天为四个月
  const startAgeYears = Math.floor(daysDiff / 3);
  const remainingDays = daysDiff % 3;
  const startAgeMonths = Math.floor(remainingDays * 4);
  const startAgeDays = Math.floor((remainingDays * 4 - startAgeMonths) * 30);

  console.log('🎯 [起运计算] 起运年龄计算:', {
    总天数: daysDiff,
    起运年: startAgeYears,
    起运月: startAgeMonths,
    起运日: startAgeDays
  });

  // 计算具体的起运日期
  const startYear = birthYear + startAgeYears;
  const startMonth = birthMonth + startAgeMonths;
  const startDay = birthDay + startAgeDays;

  // 处理月份和日期溢出
  let finalStartYear = startYear;
  let finalStartMonth = startMonth;
  let finalStartDay = startDay;

  if (finalStartMonth > 12) {
    finalStartYear += Math.floor((finalStartMonth - 1) / 12);
    finalStartMonth = ((finalStartMonth - 1) % 12) + 1;
  }

  // 简化日期处理（避免复杂的月份天数计算）
  if (finalStartDay > 30) {
    finalStartMonth += Math.floor(finalStartDay / 30);
    finalStartDay = finalStartDay % 30;
    if (finalStartDay === 0) finalStartDay = 30;
  }

  if (finalStartMonth > 12) {
    finalStartYear += Math.floor((finalStartMonth - 1) / 12);
    finalStartMonth = ((finalStartMonth - 1) % 12) + 1;
  }

  const startAge = startAgeYears + (startAgeMonths / 12) + (startAgeDays / 365);

  console.log('🎯 [起运计算] 最终结果:', {
    起运年龄: `${startAgeYears}岁${startAgeMonths}个月${startAgeDays}天`,
    起运日期: `${finalStartYear}年${finalStartMonth}月${finalStartDay}日`,
    起运方向: isForward ? '顺行' : '逆行'
  });

  return {
    startAge: Math.round(startAge * 100) / 100, // 保留两位小数
    startAgeYears: startAgeYears,
    startAgeMonths: startAgeMonths,
    startAgeDays: startAgeDays,
    startYear: finalStartYear,
    startMonth: finalStartMonth,
    startDay: finalStartDay,
    direction: isForward ? '顺行' : '逆行',
    isForward: isForward,
    daysDiff: daysDiff,
    targetSolarTerm: targetSolarTerm.name,
    targetDate: targetDate.toISOString().split('T')[0],
    startDateString: `${finalStartYear}年${finalStartMonth}月${finalStartDay}日`
  };
}



/**
 * 计算大运
 * @param {Object} bazi - 八字信息
 * @param {Object} startLuck - 起运信息
 * @param {number} periods - 计算几个大运周期，默认8个
 * @returns {Array} 大运数组
 */
function calculateDayun(bazi, startLuck, periods = 8) {
  const monthStem = bazi.month.stem;
  const monthBranch = bazi.month.branch;
  const isForward = startLuck.isForward;
  
  const stemIndex = HEAVENLY_STEMS.indexOf(monthStem);
  const branchIndex = EARTHLY_BRANCHES.indexOf(monthBranch);
  
  const dayunList = [];
  
  for (let i = 0; i < periods; i++) {
    let currentStemIndex, currentBranchIndex;
    
    if (isForward) {
      // 顺行：天干地支都向后推
      currentStemIndex = (stemIndex + i + 1) % 10;
      currentBranchIndex = (branchIndex + i + 1) % 12;
    } else {
      // 逆行：天干地支都向前推
      currentStemIndex = (stemIndex - i - 1 + 10) % 10;
      currentBranchIndex = (branchIndex - i - 1 + 12) % 12;
    }
    
    // 大运从起运开始，每10年一个周期
    const startAge = startLuck.startAge + i * 10; // 从起运年龄开始计算
    const endAge = startAge + 9;
    const startYear = startLuck.startYear + i * 10;
    const endYear = startYear + 9;
    
    const luckStem = HEAVENLY_STEMS[currentStemIndex];
    const luckBranch = EARTHLY_BRANCHES[currentBranchIndex];
    const tenGod = calculateTenGod(bazi.day.stem, luckStem);

    dayunList.push({
      period: i + 1,
      stem: luckStem,
      branch: luckBranch,
      startAge: Math.round(startAge),
      endAge: Math.round(endAge),
      startYear: Math.round(startYear),
      endYear: Math.round(endYear),
      ganzhi: `${luckStem}${luckBranch}`,
      tenGod: tenGod,
      analysis: analyzeLuckPeriod(tenGod, luckStem, luckBranch)
    });
  }
  
  return dayunList;
}

/**
 * 分析大运期间的运势特点
 * @param {string} tenGod - 十神
 * @param {string} luckStem - 大运天干
 * @param {string} luckBranch - 大运地支
 * @returns {string} 运势分析
 */
function analyzeLuckPeriod(tenGod, luckStem, luckBranch) {
  const luckAnalysis = {
    '正官': '利于仕途发展，工作顺利，地位提升，但需注意压力和责任',
    '七杀': '事业有突破，权威显现，但需防小人，注意健康和情绪控制',
    '正财': '财运亨通，投资有利，感情稳定，适合置业和理财',
    '偏财': '横财机会多，投机运佳，但财来财去，需谨慎理财',
    '正印': '学业进步，贵人相助，利于考试升职，文化事业发达',
    '偏印': '技艺精进，偏门学问有成，但易孤独，需注意健康',
    '食神': '才华发挥，享受生活，子女运佳，适合创作和表演',
    '伤官': '聪明才智显现，创新能力强，但易冲动，需控制情绪',
    '比肩': '朋友助力，合作机会多，自立能力强，但需防竞争',
    '劫财': '破财之象，需防小人和争夺，谨慎投资和合作'
  };

  const baseAnalysis = luckAnalysis[tenGod] || '运势平平，需综合分析';
  return `${tenGod}大运：${baseAnalysis}`;
}

/**
 * 计算流年
 * @param {number} startYear - 开始年份
 * @param {number} years - 计算多少年，默认10年
 * @returns {Array} 流年数组
 */
function calculateLiunian(startYear, years = 10) {
  const liunianList = [];
  
  for (let i = 0; i < years; i++) {
    const year = startYear + i;
    
    // 计算该年的天干地支
    const stemIndex = (year - 4) % 10; // 甲子年为公元4年
    const branchIndex = (year - 4) % 12;
    
    liunianList.push({
      year: year,
      stem: HEAVENLY_STEMS[stemIndex],
      branch: EARTHLY_BRANCHES[branchIndex],
      ganzhi: `${HEAVENLY_STEMS[stemIndex]}${EARTHLY_BRANCHES[branchIndex]}`
    });
  }
  
  return liunianList;
}

/**
 * 分析大运流年与命局的关系
 * @param {Object} bazi - 八字信息
 * @param {Object} dayun - 大运信息
 * @param {Object} liunian - 流年信息
 * @param {Object} useGodAnalysis - 用神分析
 * @returns {Object} 大运流年分析
 */
function analyzeDayunLiunian(bazi, dayun, liunian, useGodAnalysis) {
  const analysis = {
    dayun: analyzeSinglePeriod(bazi, dayun, useGodAnalysis, '大运'),
    liunian: analyzeSinglePeriod(bazi, liunian, useGodAnalysis, '流年'),
    combined: ''
  };
  
  // 大运流年组合分析
  const dayunGood = analysis.dayun.isGood;
  const liunianGood = analysis.liunian.isGood;
  
  if (dayunGood && liunianGood) {
    analysis.combined = '大运流年皆吉，运势极佳，宜积极进取';
  } else if (dayunGood && !liunianGood) {
    analysis.combined = '大运吉而流年不利，整体尚可，需要谨慎';
  } else if (!dayunGood && liunianGood) {
    analysis.combined = '大运不利但流年较好，可有小的转机';
  } else {
    analysis.combined = '大运流年皆不利，需要谨慎保守，避免冒险';
  }
  
  return analysis;
}

/**
 * 分析单个时期（大运或流年）
 * @param {Object} bazi - 八字信息
 * @param {Object} period - 时期信息（大运或流年）
 * @param {Object} useGodAnalysis - 用神分析
 * @param {string} type - 类型（'大运'或'流年'）
 * @returns {Object} 分析结果
 */
function analyzeSinglePeriod(bazi, period, useGodAnalysis, type) {
  const periodStem = period.stem;
  const periodBranch = period.branch;
  const useGod = useGodAnalysis.useGod;
  const avoidGod = useGodAnalysis.avoidGod;
  
  let score = 0;
  let analysis = [];
  
  // 分析天干
  const stemElement = getElementFromStem(periodStem);
  if (useGod.includes(stemElement)) {
    score += 2;
    analysis.push(`${type}天干${periodStem}为用神，大吉`);
  } else if (avoidGod.includes(stemElement)) {
    score -= 2;
    analysis.push(`${type}天干${periodStem}为忌神，不利`);
  }
  
  // 分析地支
  const branchElement = getElementFromBranch(periodBranch);
  if (useGod.includes(branchElement)) {
    score += 1;
    analysis.push(`${type}地支${periodBranch}为用神，有利`);
  } else if (avoidGod.includes(branchElement)) {
    score -= 1;
    analysis.push(`${type}地支${periodBranch}为忌神，不利`);
  }
  
  // 与日主的关系
  const dayMaster = bazi.dayMaster;
  const relation = analyzeRelation(dayMaster, periodStem);
  analysis.push(`与日主关系：${relation}`);
  
  return {
    score: score,
    isGood: score > 0,
    analysis: analysis,
    ganzhi: `${periodStem}${periodBranch}`
  };
}

/**
 * 分析两个天干的关系
 * @param {string} stem1 - 天干1
 * @param {string} stem2 - 天干2
 * @returns {string} 关系描述
 */
function analyzeRelation(stem1, stem2) {
  // 简化的关系分析
  if (stem1 === stem2) {
    return '比肩，同类相助';
  }
  
  // 这里应该有更详细的天干关系分析
  // 包括合化、冲克等关系
  return '需要具体分析';
}

/**
 * 获取天干对应的五行
 */
function getElementFromStem(stem) {
  const elementMap = {
    '甲': '木', '乙': '木',
    '丙': '火', '丁': '火',
    '戊': '土', '己': '土',
    '庚': '金', '辛': '金',
    '壬': '水', '癸': '水'
  };
  return elementMap[stem];
}

/**
 * 获取地支对应的五行
 */
function getElementFromBranch(branch) {
  const elementMap = {
    '子': '水', '亥': '水',
    '寅': '木', '卯': '木',
    '巳': '火', '午': '火',
    '申': '金', '酉': '金',
    '辰': '土', '戌': '土', '丑': '土', '未': '土'
  };
  return elementMap[branch];
}

/**
 * 获取当前大运
 * @param {Array} dayunList - 大运列表
 * @param {number} currentAge - 当前年龄
 * @returns {Object} 当前大运
 */
function getCurrentDayun(dayunList, currentAge) {
  return dayunList.find(dayun => 
    currentAge >= dayun.startAge && currentAge <= dayun.endAge
  );
}

/**
 * 获取当前流年
 * @param {number} currentYear - 当前年份
 * @returns {Object} 当前流年
 */
function getCurrentLiunian(currentYear) {
  const stemIndex = (currentYear - 4) % 10;
  const branchIndex = (currentYear - 4) % 12;
  
  return {
    year: currentYear,
    stem: HEAVENLY_STEMS[stemIndex],
    branch: EARTHLY_BRANCHES[branchIndex],
    ganzhi: `${HEAVENLY_STEMS[stemIndex]}${EARTHLY_BRANCHES[branchIndex]}`
  };
}

/**
 * 预测未来运势
 * @param {Object} bazi - 八字信息
 * @param {boolean} isMale - 是否为男性
 * @param {number} currentAge - 当前年龄
 * @param {number} years - 预测年数
 * @returns {Object} 运势预测
 */
function predictFortune(bazi, isMale, currentAge, years = 5) {
  const startLuck = calculateStartLuck(bazi, isMale);
  const dayunList = calculateDayun(bazi, startLuck);
  const currentYear = new Date().getFullYear();
  const liunianList = calculateLiunian(currentYear, years);
  
  const predictions = [];
  
  liunianList.forEach(liunian => {
    const age = currentAge + (liunian.year - currentYear);
    const currentDayun = getCurrentDayun(dayunList, age);
    
    if (currentDayun) {
      // 这里需要用神分析，暂时简化
      const useGodAnalysis = { useGod: ['木'], avoidGod: ['金'] }; // 简化
      const analysis = analyzeDayunLiunian(bazi, currentDayun, liunian, useGodAnalysis);
      
      predictions.push({
        year: liunian.year,
        age: age,
        dayun: currentDayun.ganzhi,
        liunian: liunian.ganzhi,
        analysis: analysis.combined,
        score: analysis.dayun.score + analysis.liunian.score
      });
    }
  });
  
  return {
    predictions: predictions,
    dayunList: dayunList,
    currentDayun: getCurrentDayun(dayunList, currentAge)
  };
}

/**
 * 获取节气名称
 * @param {Date} solarTermDate - 节气日期
 * @returns {string} 节气名称
 */
function getSolarTermName(solarTermDate) {
  if (!solarTermDate) return '未知节气';

  const month = solarTermDate.getMonth() + 1;
  const day = solarTermDate.getDate();

  // 根据月份和日期大致判断节气
  if (month === 1) return day < 20 ? '小寒' : '大寒';
  if (month === 2) return day < 19 ? '立春' : '雨水';
  if (month === 3) return day < 21 ? '惊蛰' : '春分';
  if (month === 4) return day < 20 ? '清明' : '谷雨';
  if (month === 5) return day < 21 ? '立夏' : '小满';
  if (month === 6) return day < 22 ? '芒种' : '夏至';
  if (month === 7) return day < 23 ? '小暑' : '大暑';
  if (month === 8) return day < 23 ? '立秋' : '处暑';
  if (month === 9) return day < 23 ? '白露' : '秋分';
  if (month === 10) return day < 24 ? '寒露' : '霜降';
  if (month === 11) return day < 23 ? '立冬' : '小雪';
  if (month === 12) return day < 22 ? '大雪' : '冬至';

  return '节气';
}

module.exports = {
  calculateStartLuck,
  calculateDayun,
  calculateLiunian,
  analyzeDayunLiunian,
  getCurrentDayun,
  getCurrentLiunian,
  predictFortune,
  getSolarTermName
};
