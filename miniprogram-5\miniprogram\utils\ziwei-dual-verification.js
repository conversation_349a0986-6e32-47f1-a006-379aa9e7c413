// 紫微斗数双重验证系统
// 整合紫微斗数分析和终身卦分析，提供高精度的命理预测

// 导入相关模块
const { calculateLifeHexagram, verifyConsistency } = require('./life-hexagram-calculator.js');
const { predictSpecificTimes } = require('./time-prediction-engine.js');
const { comprehensiveZiweiAnalysis } = require('./ziwei-analysis.js');

/**
 * 紫微斗数双重验证分析系统
 * @param {Object} ziweiData - 紫微斗数信息
 * @param {Object} birthInfo - 出生信息
 * @param {string} question - 用户问题
 * @param {Object} currentInfo - 当前信息
 * @returns {Object} 双重验证结果
 */
function performZiweiDualVerification(ziweiData, birthInfo, question, currentInfo) {
  console.log('🔄 启动紫微斗数双重验证系统...');
  
  try {
    // 第一步：计算终身卦（隐藏式）
    const lifeHexagram = calculateLifeHexagram(birthInfo);
    console.log('✅ 终身卦计算完成:', lifeHexagram.originalHexagram.name);
    
    // 第二步：紫微斗数综合分析
    const ziweiAnalysis = comprehensiveZiweiAnalysis(ziweiData);
    console.log('✅ 紫微斗数分析完成');
    
    // 第三步：验证一致性（紫微斗数与终身卦）
    const consistency = verifyZiweiHexagramConsistency(ziweiData, lifeHexagram);
    console.log('✅ 一致性验证完成，得分:', consistency.consistency);
    
    // 第四步：时间预测分析
    const eventType = extractEventType(question);
    const timePrediction = predictSpecificTimesForZiwei(ziweiData, eventType, currentInfo, lifeHexagram);
    console.log('✅ 时间预测完成');
    
    // 第五步：综合分析结果
    const finalAnalysis = synthesizeZiweiAnalysis(ziweiData, lifeHexagram, ziweiAnalysis, timePrediction, consistency, question);
    console.log('✅ 综合分析完成');
    
    return {
      success: true,
      dualVerification: {
        ziweiAnalysis: ziweiAnalysis,
        lifeHexagram: lifeHexagram,
        consistency: consistency,
        timePrediction: timePrediction,
        finalAnalysis: finalAnalysis,
        confidence: calculateZiweiConfidence(consistency, timePrediction, ziweiAnalysis),
        timestamp: new Date().toISOString()
      },
      // 用户友好的结果（隐藏复杂计算过程）
      userResult: formatZiweiUserResult(finalAnalysis, timePrediction, consistency)
    };
    
  } catch (error) {
    console.error('❌ 紫微斗数双重验证系统错误:', error);
    return {
      success: false,
      error: error.message,
      fallbackResult: '系统分析中遇到问题，建议重新分析'
    };
  }
}

/**
 * 验证紫微斗数与终身卦的一致性
 * @param {Object} ziweiData - 紫微斗数数据
 * @param {Object} lifeHexagram - 终身卦数据
 * @returns {Object} 一致性验证结果
 */
function verifyZiweiHexagramConsistency(ziweiData, lifeHexagram) {
  let consistency = 60; // 基础分数
  const analysis = [];
  
  // 获取命宫主星
  const mingGong = ziweiData.chart['命宫'];
  const mainStars = mingGong.majorStars || [];
  
  // 五行一致性检查
  const ziweiElement = getZiweiMainElement(mainStars);
  const hexagramElement = getHexagramElement(lifeHexagram.originalHexagram.upperTrigram);
  
  if (ziweiElement === hexagramElement) {
    consistency += 20;
    analysis.push(`紫微主星五行与终身卦五行完全一致（${ziweiElement}）`);
  } else if (isElementCompatible(ziweiElement, hexagramElement)) {
    consistency += 10;
    analysis.push(`紫微主星五行与终身卦五行相生（${ziweiElement}生${hexagramElement}）`);
  } else {
    analysis.push(`紫微主星五行与终身卦五行存在差异（${ziweiElement}vs${hexagramElement}）`);
  }
  
  // 性格特征一致性
  const ziweiPersonality = getZiweiPersonalityTrait(mainStars);
  const hexagramPersonality = getHexagramPersonalityTrait(lifeHexagram);
  
  if (ziweiPersonality === hexagramPersonality) {
    consistency += 15;
    analysis.push(`性格特征高度一致（${ziweiPersonality}）`);
  } else if (isPersonalityCompatible(ziweiPersonality, hexagramPersonality)) {
    consistency += 8;
    analysis.push(`性格特征基本相符`);
  }
  
  // 格局与卦象一致性
  const ziweiPattern = getZiweiPattern(ziweiData);
  const hexagramPattern = getHexagramPattern(lifeHexagram);
  
  if (isPatternConsistent(ziweiPattern, hexagramPattern)) {
    consistency += 10;
    analysis.push(`命理格局与卦象格局相符`);
  }
  
  return {
    consistency: Math.min(95, consistency),
    analysis: analysis.join('；'),
    recommendation: consistency >= 80 ? '高度一致，分析可信度很高' : 
                   consistency >= 60 ? '基本一致，分析具有参考价值' : 
                   '存在差异，建议结合实际情况谨慎参考',
    details: {
      ziweiElement: ziweiElement,
      hexagramElement: hexagramElement,
      ziweiPersonality: ziweiPersonality,
      hexagramPersonality: hexagramPersonality
    }
  };
}

/**
 * 获取紫微主星的五行属性
 */
function getZiweiMainElement(mainStars) {
  const starElements = {
    '紫微': '土', '天机': '木', '太阳': '火', '武曲': '金', '天同': '水',
    '廉贞': '火', '天府': '土', '太阴': '水', '贪狼': '木', '巨门': '水',
    '天相': '水', '天梁': '土', '七杀': '金', '破军': '水'
  };
  
  for (const star of mainStars) {
    if (starElements[star]) {
      return starElements[star];
    }
  }
  return '土'; // 默认
}

/**
 * 获取卦象五行属性
 */
function getHexagramElement(trigram) {
  const elementMap = {
    '乾': '金', '兑': '金', '离': '火', '震': '木',
    '巽': '木', '坎': '水', '艮': '土', '坤': '土'
  };
  return elementMap[trigram] || '土';
}

/**
 * 检查五行是否相容
 */
function isElementCompatible(element1, element2) {
  const compatible = {
    '木': ['水', '火'],
    '火': ['木', '土'],
    '土': ['火', '金'],
    '金': ['土', '水'],
    '水': ['金', '木']
  };
  return compatible[element1] && compatible[element1].includes(element2);
}

/**
 * 获取紫微性格特征
 */
function getZiweiPersonalityTrait(mainStars) {
  if (mainStars.includes('紫微')) return '帝王';
  if (mainStars.includes('天机')) return '智慧';
  if (mainStars.includes('太阳')) return '光明';
  if (mainStars.includes('武曲')) return '刚毅';
  if (mainStars.includes('天同')) return '温和';
  if (mainStars.includes('廉贞')) return '复杂';
  if (mainStars.includes('天府')) return '稳重';
  if (mainStars.includes('太阴')) return '柔和';
  if (mainStars.includes('贪狼')) return '多变';
  if (mainStars.includes('巨门')) return '深沉';
  if (mainStars.includes('天相')) return '中庸';
  if (mainStars.includes('天梁')) return '慈祥';
  if (mainStars.includes('七杀')) return '威严';
  if (mainStars.includes('破军')) return '开创';
  return '平和';
}

/**
 * 获取卦象性格特征
 */
function getHexagramPersonalityTrait(lifeHexagram) {
  const upperTrigram = lifeHexagram.originalHexagram.upperTrigram;
  const personalityMap = {
    '乾': '刚毅', '兑': '喜悦', '离': '光明', '震': '动力',
    '巽': '柔顺', '坎': '智慧', '艮': '稳重', '坤': '温和'
  };
  return personalityMap[upperTrigram] || '平和';
}

/**
 * 检查性格是否相容
 */
function isPersonalityCompatible(trait1, trait2) {
  const compatibleTraits = {
    '帝王': ['威严', '刚毅', '光明'],
    '智慧': ['光明', '深沉', '稳重'],
    '刚毅': ['威严', '帝王', '开创'],
    '温和': ['柔和', '慈祥', '中庸'],
    '稳重': ['慈祥', '中庸', '智慧']
  };
  return compatibleTraits[trait1] && compatibleTraits[trait1].includes(trait2);
}

/**
 * 获取紫微格局
 */
function getZiweiPattern(ziweiData) {
  // 简化的格局判断
  const mingGong = ziweiData.chart['命宫'];
  const mainStars = mingGong.majorStars || [];
  
  if (mainStars.includes('紫微') && mainStars.includes('天府')) return '上格';
  if (mainStars.includes('紫微') && mainStars.includes('贪狼')) return '中格';
  if (mainStars.includes('武曲') && mainStars.includes('天府')) return '上格';
  return '中格';
}

/**
 * 获取卦象格局
 */
function getHexagramPattern(lifeHexagram) {
  const hexagramName = lifeHexagram.originalHexagram.name;
  // 根据卦名判断格局高低
  const highPatterns = ['乾为天', '坤为地', '水雷屯', '山水蒙'];
  const midPatterns = ['水天需', '天水讼', '地水师', '水地比'];
  
  if (highPatterns.includes(hexagramName)) return '上格';
  if (midPatterns.includes(hexagramName)) return '中格';
  return '下格';
}

/**
 * 检查格局是否一致
 */
function isPatternConsistent(ziweiPattern, hexagramPattern) {
  return ziweiPattern === hexagramPattern;
}

/**
 * 紫微斗数专用时间预测
 */
function predictSpecificTimesForZiwei(ziweiData, eventType, currentInfo, lifeHexagram) {
  // 基于紫微斗数的时间预测逻辑
  // 这里可以根据大限、流年等进行更精确的预测
  return predictSpecificTimes(ziweiData, eventType, currentInfo, lifeHexagram);
}

/**
 * 提取问题中的事件类型
 */
function extractEventType(question) {
  const eventKeywords = {
    '财运': ['财运', '赚钱', '发财', '收入', '投资', '股票', '生意'],
    '事业': ['事业', '工作', '升职', '跳槽', '创业', '官运', '仕途'],
    '婚姻': ['婚姻', '结婚', '感情', '恋爱', '配偶', '另一半', '对象'],
    '学业': ['学业', '考试', '升学', '学习', '读书', '文凭', '学历'],
    '健康': ['健康', '身体', '疾病', '医疗', '养生', '康复', '治疗'],
    '子女': ['子女', '孩子', '生育', '怀孕', '生子', '儿女', '后代'],
    '朋友': ['朋友', '人际', '合伙', '同事', '社交', '关系', '贵人']
  };
  
  for (const [type, keywords] of Object.entries(eventKeywords)) {
    if (keywords.some(keyword => question.includes(keyword))) {
      return type;
    }
  }
  
  return '综合运势';
}

/**
 * 综合分析结果
 */
function synthesizeZiweiAnalysis(ziweiData, lifeHexagram, ziweiAnalysis, timePrediction, consistency, question) {
  // 基础命理特征
  const coreTraits = analyzeZiweiCoreTraits(ziweiData, lifeHexagram);
  
  // 人生发展趋势
  const lifeTrend = analyzeZiweiLifeTrend(ziweiData, lifeHexagram);
  
  // 关键时间节点
  const keyTimePoints = extractKeyTimePoints(timePrediction);
  
  // 具体建议
  const recommendations = generateZiweiRecommendations(ziweiData, lifeHexagram, timePrediction, question);
  
  return {
    coreTraits: coreTraits,
    lifeTrend: lifeTrend,
    keyTimePoints: keyTimePoints,
    recommendations: recommendations,
    analysisMethod: '基于紫微斗数与梅花易数双重验证分析',
    knowledgeBase: '严格依据《紫微斗数全书》《斗数宣微》《梅花易数》等古籍理论'
  };
}

/**
 * 分析紫微核心特征
 */
function analyzeZiweiCoreTraits(ziweiData, lifeHexagram) {
  const traits = [];
  const mingGong = ziweiData.chart['命宫'];
  
  if (mingGong.majorStars && mingGong.majorStars.length > 0) {
    traits.push(`命宫主星：${mingGong.majorStars.join('、')}`);
  }
  
  if (ziweiData.wuxingJu) {
    traits.push(`五行局：${ziweiData.wuxingJu.ju}`);
  }
  
  if (lifeHexagram.analysis && lifeHexagram.analysis.lifeTheme) {
    traits.push(`命运主题：${lifeHexagram.analysis.lifeTheme}`);
  }
  
  return traits;
}

/**
 * 分析紫微人生趋势
 */
function analyzeZiweiLifeTrend(ziweiData, lifeHexagram) {
  const trends = [];
  
  // 基于命宫主星的趋势
  const mingGong = ziweiData.chart['命宫'];
  if (mingGong.majorStars && mingGong.majorStars.includes('紫微')) {
    trends.push('具有领导才能，适合管理或创业');
  }
  
  // 基于终身卦的趋势
  if (lifeHexagram.analysis && lifeHexagram.analysis.trendAnalysis) {
    trends.push(lifeHexagram.analysis.trendAnalysis.direction);
  }
  
  return trends;
}

/**
 * 提取关键时间节点
 */
function extractKeyTimePoints(timePrediction) {
  const timePoints = [];
  
  if (timePrediction.pastAnalysis && timePrediction.pastAnalysis.length > 0) {
    timePoints.push({
      type: '过去验证',
      events: timePrediction.pastAnalysis.map(event => ({
        time: `${event.year}年${event.season}`,
        description: event.description,
        probability: event.probability
      }))
    });
  }
  
  if (timePrediction.futurePredictons && timePrediction.futurePredictons.length > 0) {
    timePoints.push({
      type: '未来预测',
      events: timePrediction.futurePredictons.map(event => ({
        time: `${event.year}年${event.season}`,
        description: event.description,
        probability: event.probability
      }))
    });
  }
  
  return timePoints;
}

/**
 * 生成紫微具体建议
 */
function generateZiweiRecommendations(ziweiData, lifeHexagram, timePrediction, question) {
  const recommendations = [];
  
  // 基于命宫主星的建议
  const mingGong = ziweiData.chart['命宫'];
  if (mingGong.majorStars && mingGong.majorStars.includes('紫微')) {
    recommendations.push('建议从事管理、领导或政府相关工作');
  }
  
  // 基于终身卦的建议
  if (lifeHexagram.analysis && lifeHexagram.analysis.keyInsights) {
    lifeHexagram.analysis.keyInsights.forEach(insight => {
      if (insight.includes('建议') || insight.includes('宜') || insight.includes('应')) {
        recommendations.push(insight);
      }
    });
  }
  
  return recommendations;
}

/**
 * 计算紫微整体可信度
 */
function calculateZiweiConfidence(consistency, timePrediction, ziweiAnalysis) {
  let confidence = 65; // 基础可信度
  
  // 一致性加分
  confidence += consistency.consistency * 0.18;
  
  // 时间预测加分
  confidence += timePrediction.confidence * 0.12;
  
  // 紫微分析完整性加分
  if (ziweiAnalysis && ziweiAnalysis.mingGongAnalysis) {
    confidence += 8;
  }
  
  return Math.min(95, Math.round(confidence));
}

/**
 * 格式化用户友好结果
 */
function formatZiweiUserResult(finalAnalysis, timePrediction, consistency) {
  let result = '';
  
  // 核心特征
  result += '【命理特征】\n';
  finalAnalysis.coreTraits.forEach(trait => {
    result += `• ${trait}\n`;
  });
  result += '\n';
  
  // 发展趋势
  result += '【发展趋势】\n';
  finalAnalysis.lifeTrend.forEach(trend => {
    result += `• ${trend}\n`;
  });
  result += '\n';
  
  // 时间节点
  if (finalAnalysis.keyTimePoints.length > 0) {
    finalAnalysis.keyTimePoints.forEach(timePoint => {
      result += `【${timePoint.type}】\n`;
      timePoint.events.forEach(event => {
        result += `• ${event.time}：${event.description}（概率${event.probability}）\n`;
      });
      result += '\n';
    });
  }
  
  // 具体建议
  result += '【调理建议】\n';
  finalAnalysis.recommendations.forEach(rec => {
    result += `• ${rec}\n`;
  });
  result += '\n';
  
  // 验证说明
  result += '【分析说明】\n';
  result += `• 双重验证一致性得分：${consistency.consistency}分\n`;
  result += `• ${finalAnalysis.analysisMethod}\n`;
  result += `• ${finalAnalysis.knowledgeBase}\n`;
  
  return result;
}

module.exports = {
  performZiweiDualVerification,
  verifyZiweiHexagramConsistency,
  extractEventType,
  synthesizeZiweiAnalysis,
  formatZiweiUserResult
};
