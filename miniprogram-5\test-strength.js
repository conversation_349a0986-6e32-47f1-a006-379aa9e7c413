// 测试庙旺利陷系统

const { analyzeStarStrength } = require('./miniprogram/utils/ziwei-enhanced.js');

console.log('🎯 测试庙旺利陷系统');

// 测试各种星曜在不同宫位的强弱
const testCases = [
  { star: '紫微', gong: 0, expected: '庙' },   // 紫微在子宫
  { star: '紫微', gong: 4, expected: '旺' },   // 紫微在辰宫
  { star: '太阳', gong: 4, expected: '旺' },   // 太阳在巳宫
  { star: '太阳', gong: 5, expected: '庙' },   // 太阳在午宫
  { star: '太阳', gong: 11, expected: '陷' },  // 太阳在亥宫
  { star: '太阴', gong: 11, expected: '庙' },  // 太阴在亥宫
  { star: '太阴', gong: 5, expected: '陷' },   // 太阴在午宫
  { star: '武曲', gong: 2, expected: '庙' },   // 武曲在寅宫
  { star: '天府', gong: 2, expected: '庙' },   // 天府在寅宫
  { star: '贪狼', gong: 4, expected: '旺' },   // 贪狼在辰宫
];

console.log('\n=== 庙旺利陷测试结果 ===');
testCases.forEach(testCase => {
  const result = analyzeStarStrength(testCase.star, testCase.gong);
  const gongNames = ['子', '丑', '寅', '卯', '辰', '巳', '午', '未', '申', '酉', '戌', '亥'];
  const status = result === testCase.expected ? '✅' : '❌';
  
  console.log(`${status} ${testCase.star}在${gongNames[testCase.gong]}宫: ${result} (期望: ${testCase.expected})`);
});

// 测试所有主星的完整庙旺利陷表
console.log('\n=== 完整庙旺利陷表测试 ===');
const majorStars = ['紫微', '天机', '太阳', '武曲', '天同', '廉贞', '天府', '太阴', '贪狼', '巨门', '天相', '天梁', '七杀', '破军'];
const gongNames = ['子', '丑', '寅', '卯', '辰', '巳', '午', '未', '申', '酉', '戌', '亥'];

majorStars.forEach(star => {
  console.log(`\n${star}星庙旺利陷:`);
  const strengths = [];
  for (let i = 0; i < 12; i++) {
    const strength = analyzeStarStrength(star, i);
    strengths.push(`${gongNames[i]}:${strength}`);
  }
  console.log(strengths.join(' | '));
});

console.log('\n🎯 庙旺利陷系统测试完成');
