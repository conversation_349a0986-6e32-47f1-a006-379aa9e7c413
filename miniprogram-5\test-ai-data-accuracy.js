// 测试传递给AI的信息准确性
console.log('🎯 开始测试四个模块传递给AI的信息准确性...\n');

// 导入相关模块
const { buildUserPrompt } = require('./miniprogram/utils/ai-service.js');

console.log('=== 测试1: 八字模块传递给AI的信息 ===');

// 模拟八字数据（包含修复后的起运信息）
const mockBaziContext = {
  bazi: {
    year: { stem: '庚', branch: '午' },
    month: { stem: '辛', branch: '巳' },
    day: { stem: '甲', branch: '子' },
    hour: { stem: '己', branch: '巳' },
    dayMaster: '甲木',
    dayMasterElement: '木',
    pattern: '身弱用印',
    useGod: '水木',
    nayin: '路旁土',
    strength: '身弱'
  },
  currentDayun: {
    ganzhi: '壬午',
    startAge: 8,
    endAge: 17
  },
  currentLiunian: {
    ganzhi: '甲辰'
  },
  startLuckAge: 7,
  startLuckInfo: {
    startAgeYears: 7,
    startAgeMonths: 8,
    startAgeDays: 0,
    startDateString: '1998年1月15日',
    direction: '顺行',
    targetSolarTerm: '小暑'
  },
  wuxingData: { 金: 2, 木: 1, 水: 1, 火: 2, 土: 0 },
  wuxingAnalysis: '金旺木弱，需要水来调候',
  birthInfo: {
    year: 1990,
    month: 5,
    day: 15,
    hour: 10,
    minute: 30,
    isMale: true
  }
};

try {
  const baziPrompt = buildUserPrompt('我的财运如何？', mockBaziContext, '财运', 'bazi');
  console.log('✅ 八字AI提示词构建成功');
  console.log('📋 关键信息检查:');
  console.log('- 包含起运年龄:', baziPrompt.includes('起运年龄'));
  console.log('- 包含起运信息:', baziPrompt.includes('起运信息'));
  console.log('- 包含起运日期:', baziPrompt.includes('起运日期'));
  console.log('- 包含起运方式:', baziPrompt.includes('起运方式'));
  console.log('- 包含节气信息:', baziPrompt.includes('节气'));
  console.log('- 包含大运信息:', baziPrompt.includes('当前大运'));
  console.log('- 包含五行分布:', baziPrompt.includes('五行分布'));
} catch (error) {
  console.log('❌ 八字AI提示词构建失败:', error.message);
}

console.log('\n=== 测试2: 紫微斗数模块传递给AI的信息 ===');

// 模拟紫微斗数数据（包含修复后的大限信息）
const mockZiweiContext = {
  ziwei: {
    mingGong: '紫微星',
    caibogong: '天府星',
    guanlugong: '武曲星',
    fuqigong: '天相星',
    pattern: '紫微天府格',
    currentDaxian: {
      palace: '命宫',
      startAge: 6,
      endAge: 15
    },
    startLuck: {
      startAge: 6,
      wuxingJu: '火六局',
      direction: '顺行'
    },
    currentAge: 25
  }
};

try {
  const ziweiPrompt = buildUserPrompt('我的事业运势如何？', mockZiweiContext, '事业', 'ziwei');
  console.log('✅ 紫微斗数AI提示词构建成功');
  console.log('📋 关键信息检查:');
  console.log('- 包含当前大限:', ziweiPrompt.includes('当前大限'));
  console.log('- 包含起运年龄:', ziweiPrompt.includes('起运年龄'));
  console.log('- 包含五行局:', ziweiPrompt.includes('五行局'));
  console.log('- 包含大限方向:', ziweiPrompt.includes('大限方向'));
  console.log('- 包含当前年龄:', ziweiPrompt.includes('当前年龄'));
  console.log('- 包含命宫主星:', ziweiPrompt.includes('命宫主星'));
} catch (error) {
  console.log('❌ 紫微斗数AI提示词构建失败:', error.message);
}

console.log('\n=== 测试3: 周易六爻模块传递给AI的信息 ===');

// 模拟六爻数据（包含完整装卦信息）
const mockYijingContext = {
  hexagram: {
    name: '天雷无妄',
    symbol: '☰☳',
    changingYaos: [2, 5],
    method: '铜钱摇卦',
    time: '2024年1月15日10:30',
    liuyaoInfo: {
      worldResponse: { world: 3, response: 6 },
      branches: ['戌', '申', '午', '辰', '寅', '子'],
      relatives: ['兄弟', '子孙', '妻财', '官鬼', '父母', '兄弟'],
      spirits: ['青龙', '朱雀', '勾陈', '螣蛇', '白虎', '玄武'],
      changedHexagram: { name: '天风姤' },
      mutualHexagram: { name: '山地剥' },
      voidBranches: ['戌', '亥']
    }
  }
};

try {
  const yijingPrompt = buildUserPrompt('投资股票能赚钱吗？', mockYijingContext, '财运', 'yijing');
  console.log('✅ 周易六爻AI提示词构建成功');
  console.log('📋 关键信息检查:');
  console.log('- 包含完整装卦:', yijingPrompt.includes('完整装卦信息'));
  console.log('- 包含世爻信息:', yijingPrompt.includes('世爻'));
  console.log('- 包含应爻信息:', yijingPrompt.includes('应爻'));
  console.log('- 包含六神信息:', yijingPrompt.includes('青龙'));
  console.log('- 包含六亲信息:', yijingPrompt.includes('兄弟'));
  console.log('- 包含变卦信息:', yijingPrompt.includes('变卦'));
  console.log('- 包含互卦信息:', yijingPrompt.includes('互卦'));
  console.log('- 包含空亡信息:', yijingPrompt.includes('空亡'));
} catch (error) {
  console.log('❌ 周易六爻AI提示词构建失败:', error.message);
}

console.log('\n=== 测试4: 梅花易数模块传递给AI的信息 ===');

// 模拟梅花易数数据
const mockMeihuaContext = {
  hexagram: {
    name: '火天大有',
    upper: { name: '离', symbol: '☲' },
    lower: { name: '乾', symbol: '☰' },
    change: 2,
    body: { name: '乾' },
    use: { name: '离' },
    mutual: { name: '泽山咸' },
    changed: { name: '火泽睽' },
    method: '时间起卦',
    time: '2024年1月15日10:30'
  }
};

try {
  const meihuaPrompt = buildUserPrompt('我的婚姻何时能成？', mockMeihuaContext, '婚姻', 'meihua');
  console.log('✅ 梅花易数AI提示词构建成功');
  console.log('📋 关键信息检查:');
  console.log('- 包含上卦信息:', meihuaPrompt.includes('上卦'));
  console.log('- 包含下卦信息:', meihuaPrompt.includes('下卦'));
  console.log('- 包含动爻信息:', meihuaPrompt.includes('动爻'));
  console.log('- 包含体卦信息:', meihuaPrompt.includes('体卦'));
  console.log('- 包含用卦信息:', meihuaPrompt.includes('用卦'));
  console.log('- 包含互卦信息:', meihuaPrompt.includes('互卦'));
  console.log('- 包含变卦信息:', meihuaPrompt.includes('变卦'));
  console.log('- 包含起卦时间:', meihuaPrompt.includes('起卦时间'));
} catch (error) {
  console.log('❌ 梅花易数AI提示词构建失败:', error.message);
}

console.log('\n🎉 测试完成！');
console.log('📝 修复要点总结:');
console.log('   1. 八字模块：添加了完整的起运信息传递');
console.log('   2. 紫微模块：添加了大限信息传递');
console.log('   3. 六爻模块：增强了装卦信息传递');
console.log('   4. 梅花模块：保持了完整的卦象信息传递');
console.log('   5. 所有模块都确保传递准确的时间和计算信息给AI');
