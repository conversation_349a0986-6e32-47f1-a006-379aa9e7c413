/* pages/index/index.wxss - 主界面样式 */

page {
  background: var(--paper-white);
  padding-bottom: 120rpx;
}

.index-container {
  min-height: 100vh;
  background: var(--paper-white);
}

/* 用户信息头部 - 水墨风格 */
.user-header {
  padding: 50rpx 40rpx;
  margin: 20rpx 30rpx 50rpx;
  border-radius: 24rpx;
  background: linear-gradient(135deg,
    var(--ancient-paper) 0%,
    var(--paper-white) 50%,
    var(--ancient-paper) 100%);
  box-shadow:
    0 12rpx 48rpx rgba(26, 26, 26, 0.08),
    inset 0 1rpx 0 rgba(255, 255, 255, 0.5);
  border: 3rpx solid rgba(212, 175, 55, 0.15);
  position: relative;
}

.user-header::before {
  content: '';
  position: absolute;
  top: -2rpx;
  left: -2rpx;
  right: -2rpx;
  bottom: -2rpx;
  background: linear-gradient(135deg,
    rgba(212, 175, 55, 0.3) 0%,
    transparent 50%,
    rgba(212, 175, 55, 0.3) 100%);
  border-radius: 26rpx;
  z-index: -1;
}

.user-info {
  display: flex;
  align-items: center;
  margin-bottom: 30rpx;
}

.user-avatar {
  width: 90rpx;
  height: 90rpx;
  border-radius: 50%;
  margin-right: 30rpx;
  overflow: hidden;
  background: linear-gradient(135deg, var(--ink-gray) 0%, var(--ink-light) 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  border: 3rpx solid rgba(212, 175, 55, 0.3);
  box-shadow: 0 6rpx 20rpx rgba(26, 26, 26, 0.15);
}

.avatar-img {
  width: 100%;
  height: 100%;
  border-radius: 50%;
}

.guest-avatar {
  background: linear-gradient(135deg, var(--ink-gray) 0%, var(--ink-light) 100%);
}

.guest-icon {
  color: white;
  font-size: 32rpx;
  font-weight: 500;
}

.user-details {
  flex: 1;
}

.user-name {
  font-size: 34rpx;
  color: var(--ink-black);
  font-weight: 500;
  margin-bottom: 12rpx;
  font-family: 'STSong', '华文宋体', serif;
}





/* 登录提示 */
.login-tip {
  margin-top: 20rpx;
}

.login-button {
  background: linear-gradient(135deg, var(--ink-gray) 0%, var(--ink-light) 100%);
  padding: 24rpx 36rpx;
  border-radius: 24rpx;
  text-align: center;
  box-shadow: 0 8rpx 24rpx rgba(102, 102, 102, 0.2);
  border: 2rpx solid rgba(212, 175, 55, 0.15);
}

.login-text {
  color: white;
  font-size: 28rpx;
  font-weight: 500;
  font-family: 'STKaiti', '楷体', serif;
}

/* 主标题区域 - 水墨风格 */
.main-title {
  padding: 40rpx 30rpx;
  margin-bottom: 60rpx;
  text-align: center;
  position: relative;
}

.main-title::before {
  content: '';
  position: absolute;
  top: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 300rpx;
  height: 100rpx;
  background: radial-gradient(ellipse at center,
    rgba(212, 175, 55, 0.08) 0%,
    transparent 70%);
  border-radius: 50%;
}

.title-text {
  font-size: 44rpx;
  color: var(--ink-black);
  font-weight: 500;
  margin-bottom: 30rpx;
  font-family: 'STSong', '华文宋体', serif;
  text-shadow: 0 2rpx 8rpx rgba(26, 26, 26, 0.1);
  position: relative;
  z-index: 2;
}

.title-decoration {
  width: 240rpx;
  height: 4rpx;
  margin: 0 auto;
  background: linear-gradient(90deg,
    transparent 0%,
    rgba(212, 175, 55, 0.3) 20%,
    var(--ancient-gold) 50%,
    rgba(212, 175, 55, 0.3) 80%,
    transparent 100%);
  border-radius: 2rpx;
  position: relative;
}

.title-decoration::before,
.title-decoration::after {
  content: '';
  position: absolute;
  top: 50%;
  width: 20rpx;
  height: 20rpx;
  background: var(--ancient-gold);
  border-radius: 50%;
  transform: translateY(-50%);
}

.title-decoration::before {
  left: -30rpx;
}

.title-decoration::after {
  right: -30rpx;
}

/* 功能区域标题 */
.section-header {
  padding: 0 30rpx;
  margin-bottom: 30rpx;
}

.section-title {
  font-size: 36rpx;
  color: var(--ink-black);
  font-weight: 500;
  font-family: 'STSong', '华文宋体', serif;
}

/* 功能模块列表 */
.function-list {
  padding: 0 30rpx;
  margin-bottom: 60rpx;
}

/* 功能项目 */
.function-item {
  position: relative;
  border-radius: 28rpx;
  overflow: hidden;
  margin-bottom: 32rpx;
  box-shadow:
    0 8rpx 32rpx rgba(0, 0, 0, 0.15),
    0 2rpx 8rpx rgba(0, 0, 0, 0.1),
    inset 0 1rpx 0 rgba(255, 255, 255, 0.1);
  background: white;
  border: 2rpx solid rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10rpx);
}

.item-background {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  opacity: 0.92;
  border-radius: 28rpx;
}

.item-content {
  position: relative;
  z-index: 2;
  padding: 40rpx 30rpx;
  display: flex;
  align-items: center;
  min-height: 140rpx;
}

/* 左侧图标区域 */
.item-icon-section {
  width: 120rpx;
  display: flex;
  justify-content: center;
  margin-right: 30rpx;
}

.item-icon {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  background:
    radial-gradient(circle at 30% 30%, rgba(255, 255, 255, 0.4) 0%, rgba(255, 255, 255, 0.15) 100%),
    rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  border: 2rpx solid rgba(255, 255, 255, 0.3);
  box-shadow:
    0 8rpx 24rpx rgba(0, 0, 0, 0.2),
    inset 0 1rpx 0 rgba(255, 255, 255, 0.4),
    inset 0 -1rpx 0 rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(8rpx);
}

.icon-text {
  color: white;
  font-size: 36rpx;
  font-weight: 600;
  font-family: 'STKaiti', '楷体', serif;
  text-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.3);
}

/* 中间信息区域 */
.item-info-section {
  flex: 1;
  margin-right: 30rpx;
}

.item-title {
  color: white;
  font-size: 36rpx;
  font-weight: 600;
  margin-bottom: 8rpx;
  font-family: 'STSong', '华文宋体', serif;
  text-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.3);
}

.item-subtitle {
  color: rgba(255, 255, 255, 0.85);
  font-size: 24rpx;
  margin-bottom: 12rpx;
  font-family: 'STKaiti', '楷体', serif;
}



/* 右侧描述区域 */
.item-description-section {
  width: 240rpx;
  text-align: right;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: flex-end;
}

.description-text {
  color: rgba(255, 255, 255, 0.9);
  font-size: 26rpx;
  line-height: 1.2;
  font-weight: 500;
  font-family: 'STKaiti', '楷体', serif;
  text-shadow: 0 1rpx 4rpx rgba(0, 0, 0, 0.2);
  background: rgba(0, 0, 0, 0.2);
  padding: 8rpx 12rpx;
  margin-bottom: 4rpx;
  border-radius: 8rpx;
  border: 1rpx solid rgba(255, 255, 255, 0.1);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}




