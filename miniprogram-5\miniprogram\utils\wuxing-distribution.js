// 五行分布计算工具
// 用于计算和分析八字中的五行力量分布

// 天干五行对应表
const TIANGAN_WUXING = {
  '甲': '木', '乙': '木',
  '丙': '火', '丁': '火', 
  '戊': '土', '己': '土',
  '庚': '金', '辛': '金',
  '壬': '水', '癸': '水'
};

// 地支五行对应表（本气）
const DIZHI_WUXING = {
  '子': '水', '丑': '土', '寅': '木', '卯': '木',
  '辰': '土', '巳': '火', '午': '火', '未': '土',
  '申': '金', '酉': '金', '戌': '土', '亥': '水'
};

// 地支藏干表（用于精确计算五行力量）
const DIZHI_CANGGAN = {
  '子': [{ gan: '癸', strength: 100 }],
  '丑': [{ gan: '己', strength: 60 }, { gan: '癸', strength: 30 }, { gan: '辛', strength: 10 }],
  '寅': [{ gan: '甲', strength: 60 }, { gan: '丙', strength: 30 }, { gan: '戊', strength: 10 }],
  '卯': [{ gan: '乙', strength: 100 }],
  '辰': [{ gan: '戊', strength: 60 }, { gan: '乙', strength: 30 }, { gan: '癸', strength: 10 }],
  '巳': [{ gan: '丙', strength: 60 }, { gan: '庚', strength: 30 }, { gan: '戊', strength: 10 }],
  '午': [{ gan: '丁', strength: 70 }, { gan: '己', strength: 30 }],
  '未': [{ gan: '己', strength: 60 }, { gan: '丁', strength: 30 }, { gan: '乙', strength: 10 }],
  '申': [{ gan: '庚', strength: 60 }, { gan: '壬', strength: 30 }, { gan: '戊', strength: 10 }],
  '酉': [{ gan: '辛', strength: 100 }],
  '戌': [{ gan: '戊', strength: 60 }, { gan: '辛', strength: 30 }, { gan: '丁', strength: 10 }],
  '亥': [{ gan: '壬', strength: 70 }, { gan: '甲', strength: 30 }]
};

// 五行颜色配置
const WUXING_COLORS = {
  '木': '#22c55e',  // 绿色
  '火': '#ef4444',  // 红色
  '土': '#f59e0b',  // 黄色
  '金': '#6b7280',  // 灰色
  '水': '#3b82f6'   // 蓝色
};

/**
 * 计算八字五行分布
 * @param {Object} bazi - 八字对象
 * @returns {Object} 五行分布数据
 */
function calculateWuxingDistribution(bazi) {
  const wuxingCount = {
    '木': 0,
    '火': 0,
    '土': 0,
    '金': 0,
    '水': 0
  };

  // 计算天干五行
  const tiangans = [bazi.year.stem, bazi.month.stem, bazi.day.stem, bazi.hour.stem];
  tiangans.forEach(gan => {
    const wuxing = TIANGAN_WUXING[gan];
    if (wuxing) {
      wuxingCount[wuxing] += 100; // 天干基础分值
    }
  });

  // 计算地支五行（包含藏干）
  const dizhis = [bazi.year.branch, bazi.month.branch, bazi.day.branch, bazi.hour.branch];
  dizhis.forEach(zhi => {
    const cangganList = DIZHI_CANGGAN[zhi];
    if (cangganList) {
      cangganList.forEach(canggan => {
        const wuxing = TIANGAN_WUXING[canggan.gan];
        if (wuxing) {
          wuxingCount[wuxing] += canggan.strength;
        }
      });
    }
  });

  // 计算总分和百分比
  const totalScore = Object.values(wuxingCount).reduce((sum, score) => sum + score, 0);
  const wuxingPercentage = {};
  
  Object.keys(wuxingCount).forEach(wuxing => {
    wuxingPercentage[wuxing] = totalScore > 0 ? 
      Math.round((wuxingCount[wuxing] / totalScore) * 100 * 10) / 10 : 0;
  });

  // 生成图表数据
  const chartData = Object.keys(wuxingCount).map(wuxing => ({
    name: wuxing,
    value: wuxingCount[wuxing],
    percentage: wuxingPercentage[wuxing],
    color: WUXING_COLORS[wuxing]
  })).filter(item => item.value > 0).sort((a, b) => b.value - a.value);

  return {
    distribution: wuxingCount,
    percentage: wuxingPercentage,
    chartData: chartData,
    totalScore: totalScore,
    dominantElement: chartData.length > 0 ? chartData[0].name : '未知',
    weakestElement: chartData.length > 0 ? chartData[chartData.length - 1].name : '未知'
  };
}

/**
 * 分析五行平衡状态
 * @param {Object} wuxingData - 五行分布数据
 * @returns {Object} 五行分析结果
 */
function analyzeWuxingBalance(wuxingData) {
  const { percentage } = wuxingData;
  const analysis = {
    balance: '平衡',
    description: '',
    suggestions: []
  };

  // 找出过强和过弱的五行
  const strongElements = [];
  const weakElements = [];
  
  Object.keys(percentage).forEach(element => {
    const percent = percentage[element];
    if (percent > 30) {
      strongElements.push({ element, percent });
    } else if (percent < 10 && percent > 0) {
      weakElements.push({ element, percent });
    }
  });

  // 判断平衡状态
  if (strongElements.length > 0) {
    analysis.balance = '偏强';
    analysis.description = `${strongElements.map(item => `${item.element}(${item.percent}%)`).join('、')}偏强`;
  }
  
  if (weakElements.length > 0) {
    const weakDesc = `${weakElements.map(item => `${item.element}(${item.percent}%)`).join('、')}偏弱`;
    analysis.description = analysis.description ? 
      `${analysis.description}，${weakDesc}` : weakDesc;
    if (analysis.balance === '平衡') {
      analysis.balance = '偏弱';
    }
  }

  if (strongElements.length > 0 && weakElements.length > 0) {
    analysis.balance = '失衡';
  }

  // 生成建议
  strongElements.forEach(item => {
    analysis.suggestions.push(`适当克制${item.element}行能量`);
  });
  
  weakElements.forEach(item => {
    analysis.suggestions.push(`需要补充${item.element}行能量`);
  });

  if (analysis.suggestions.length === 0) {
    analysis.suggestions.push('五行分布相对均衡，保持现状即可');
  }

  return analysis;
}

/**
 * 格式化五行分布显示文本
 * @param {Object} wuxingData - 五行分布数据
 * @returns {string} 格式化的显示文本
 */
function formatWuxingDistribution(wuxingData) {
  const { chartData } = wuxingData;
  
  return chartData.map(item => 
    `${item.name}：${item.percentage}%`
  ).join('  ');
}

module.exports = {
  calculateWuxingDistribution,
  analyzeWuxingBalance,
  formatWuxingDistribution,
  WUXING_COLORS,
  TIANGAN_WUXING,
  DIZHI_WUXING
};
