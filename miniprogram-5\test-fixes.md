# 周易卦象模块修复测试指南

## 🎯 已修复的问题

### 1. UseGod显示undefined问题 ✅
**修复内容：**
- 在`liuyao-data.js`中的`analyzeLiuyao`函数添加了`useGod`字段
- 实现了`determineUseGod`函数，根据问题类型智能确定用神
- 添加了`findYaoByRelative`辅助函数查找对应六亲的爻位

**测试方法：**
1. 进入周易卦象页面
2. 输入问题："我的财运如何？"
3. 完成起卦（任意方式）
4. 查看专项分析-综合部分
5. 验证：用神应显示为"妻财爻（第X爻）"而不是"undefined"

### 2. 预分析功能不工作问题 ✅
**修复内容：**
- 修复了`generatePreAnalysisQuestions`函数的参数顺序错误
- 添加了详细的调试日志来跟踪问题生成过程
- 修复了问题类型识别逻辑

**测试方法：**
1. 进入周易卦象页面
2. 输入问题："我什么时候能升职？"
3. 完成起卦
4. 验证：应该出现预分析对话，AI会询问相关问题
5. 检查控制台日志，应该看到问题生成的详细过程

### 3. 动爻显示问题 ✅
**修复内容：**
- 动爻显示逻辑本身是正确的，问题可能在UI层面
- 添加了更详细的调试信息

**测试方法：**
1. 使用手动摇卦方式
2. 完成6次投币
3. 查看分析结果中的动爻显示
4. 验证：应该正确显示"动爻：第X、Y爻"

## ⚠️ 仍需解决的问题

### 4. Fetch错误问题 🔄
**问题描述：**
- 错误信息："fetch is not defined"
- 来源：增强AI分析调用失败

**可能原因：**
- 云函数ai-verifier可能未正确部署
- 或者云函数内部的node-fetch依赖有问题

**临时解决方案：**
- 基础的六爻分析功能正常工作
- 只是增强AI分析功能受影响
- 可以先测试其他修复的功能

**完整解决方案：**
1. 重新部署ai-verifier云函数
2. 检查云函数的package.json依赖
3. 或者修改代码使用wx.cloud.callFunction而不是直接调用AI API

## 🧪 完整测试流程

### 测试1：财运问题 + 用神验证
```
问题：我的投资什么时候能赚钱？
预期结果：
- 用神：妻财爻（第X爻）
- 预分析：询问投资类型、风险偏好等
- 动爻：正确显示动爻位置
```

### 测试2：事业问题 + 用神验证
```
问题：我什么时候能升职？
预期结果：
- 用神：官鬼爻（第X爻）
- 预分析：询问行业、年龄段等
- 动爻：正确显示动爻位置
```

### 测试3：学业问题 + 用神验证
```
问题：我能考上研究生吗？
预期结果：
- 用神：父母爻（第X爻）
- 预分析：询问考试类型、准备情况等
- 动爻：正确显示动爻位置
```

## 📊 验证清单

- [ ] UseGod不再显示undefined
- [ ] 预分析对话正常启动
- [ ] 问题类型正确识别
- [ ] 动爻位置正确显示
- [ ] 控制台日志显示详细调试信息
- [ ] 基础六爻分析功能正常
- [ ] 专项分析内容完整

## 🔧 如果测试失败

1. **检查控制台日志**：查看详细的调试信息
2. **重新编译**：保存所有文件并重新编译
3. **清除缓存**：在开发者工具中清除缓存
4. **真机测试**：在真实设备上测试（特别是AI功能）

## 📝 测试报告模板

```
测试时间：____
测试环境：开发者工具 / 真机
测试问题：____

结果：
✅ UseGod显示：____
✅ 预分析启动：是/否
✅ 动爻显示：____
❌ AI增强分析：失败（fetch错误）

备注：____
```
