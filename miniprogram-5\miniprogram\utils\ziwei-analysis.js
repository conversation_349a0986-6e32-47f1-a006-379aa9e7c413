// 紫微斗数分析系统
// 基于传统紫微斗数理论

const { analyzeZiweiPatternsEnhanced, analyzeStarStrength } = require('./ziwei-enhanced.js');

const { TWELVE_PALACES, MAJOR_STARS, AUXILIARY_STARS, MALEFIC_STARS } = require('./ziwei-calculator.js');

// 星曜组合格局
const STAR_PATTERNS = {
  '紫微天府': { name: '紫府同宫', level: '上格', meaning: '帝王之相，富贵双全' },
  '紫微贪狼': { name: '紫贪同宫', level: '中格', meaning: '先贫后富，晚年发达' },
  '紫微七杀': { name: '紫杀同宫', level: '中格', meaning: '威权显赫，但多波折' },
  '紫微破军': { name: '紫破同宫', level: '下格', meaning: '开创力强，但不稳定' },
  '天机太阴': { name: '机月同梁', level: '上格', meaning: '聪明智慧，善于谋略' },
  '天同太阴': { name: '同阴朝斗', level: '上格', meaning: '温和富贵，安享天年' },
  '武曲天府': { name: '武府同宫', level: '上格', meaning: '财官双美，富贵之命' },
  '武曲贪狼': { name: '武贪同宫', level: '中格', meaning: '横发横破，财来财去' },
  '武曲七杀': { name: '武杀同宫', level: '中格', meaning: '将相之材，但多辛劳' }
};

// 宫位重要性权重
const PALACE_WEIGHTS = {
  '命宫': 10,
  '财帛宫': 8,
  '官禄宫': 8,
  '夫妻宫': 7,
  '福德宫': 6,
  '田宅宫': 6,
  '子女宫': 5,
  '疾厄宫': 5,
  '迁移宫': 4,
  '兄弟宫': 3,
  '奴仆宫': 3,
  '父母宫': 4
};

/**
 * 综合分析紫微命盘
 * @param {Object} ziweiData - 紫微斗数数据
 * @returns {Object} 综合分析结果
 */
function comprehensiveZiweiAnalysis(ziweiData) {
  console.log('🎯 开始综合紫微斗数分析（增强版）');
  const chart = ziweiData.chart;

  // 分析命宫
  const mingGongAnalysis = analyzeMingGong(chart['命宫']);
  console.log('🎯 命宫分析完成:', mingGongAnalysis);

  // 分析格局（使用增强算法）
  const patternAnalysis = analyzeZiweiPatternsEnhanced(chart);
  console.log('🎯 格局分析完成（增强版）:', patternAnalysis);

  // 分析星曜强弱（传统方法）
  const starStrengthAnalysis = analyzeStarStrengthTraditional(chart);
  console.log('🎯 星曜强弱分析完成:', starStrengthAnalysis);

  // 分析三方四正
  const sanfangsizhengAnalysis = analyzeSanfangsizheng(chart);
  console.log('🎯 三方四正分析完成:', sanfangsizhengAnalysis);

  // 分析各宫位
  const palaceAnalysis = analyzePalaces(chart);
  console.log('🎯 宫位分析完成:', palaceAnalysis);

  // 综合评分
  const overallScore = calculateOverallScore(chart);
  console.log('🎯 综合评分完成:', overallScore);

  const result = {
    mingGong: mingGongAnalysis,
    patterns: patternAnalysis,
    starStrength: starStrengthAnalysis,
    sanfangsizheng: sanfangsizhengAnalysis,
    palaces: palaceAnalysis,
    overallScore: overallScore,
    summary: generateSummary(mingGongAnalysis, patternAnalysis, overallScore)
  };

  console.log('🎯 综合紫微斗数分析完成（增强版）');
  return result;
}

/**
 * 分析命宫
 */
function analyzeMingGong(mingGong) {
  const analysis = {
    branch: mingGong.branch,
    majorStars: [],
    auxiliaryStars: [],
    maleficStars: [],
    strength: 0,
    nature: '',
    characteristics: []
  };
  
  // 分类星曜
  mingGong.stars.forEach(star => {
    if (star.type === 'major') {
      analysis.majorStars.push(star.name);
      analysis.strength += star.brightness;
    } else if (star.type === 'auxiliary') {
      analysis.auxiliaryStars.push(star.name);
      analysis.strength += star.brightness;
    } else if (star.type === 'malefic') {
      analysis.maleficStars.push(star.name);
      analysis.strength -= star.brightness;
    }
  });
  
  // 判断命宫性质
  if (analysis.majorStars.includes('紫微')) {
    analysis.nature = '帝王命格';
    analysis.characteristics.push('天生领导才能', '喜欢掌控全局', '有王者风范');
  } else if (analysis.majorStars.includes('天机')) {
    analysis.nature = '智慧命格';
    analysis.characteristics.push('聪明机智', '善于思考', '适合策划工作');
  } else if (analysis.majorStars.includes('太阳')) {
    analysis.nature = '贵人命格';
    analysis.characteristics.push('光明磊落', '乐于助人', '容易得到贵人相助');
  } else if (analysis.majorStars.includes('武曲')) {
    analysis.nature = '财富命格';
    analysis.characteristics.push('理财能力强', '适合经商', '财运较佳');
  } else {
    analysis.nature = '平常命格';
    analysis.characteristics.push('性格平和', '生活稳定', '需要后天努力');
  }
  
  return analysis;
}

/**
 * 分析格局
 */
function analyzePatterns(chart) {
  const patterns = [];
  const mingGong = chart['命宫'];
  
  // 检查命宫星曜组合
  const majorStarsInMing = mingGong.stars
    .filter(s => s.type === 'major')
    .map(s => s.name)
    .sort();
  
  // 检查是否有特殊格局
  if (majorStarsInMing.length >= 2) {
    const combination = majorStarsInMing.join('');
    if (STAR_PATTERNS[combination]) {
      patterns.push(STAR_PATTERNS[combination]);
    }
  }
  
  // 检查单星格局
  majorStarsInMing.forEach(star => {
    if (star === '紫微') {
      patterns.push({
        name: '紫微独坐',
        level: '上格',
        meaning: '独当一面，自立自强'
      });
    } else if (star === '天府') {
      patterns.push({
        name: '天府独坐',
        level: '上格',
        meaning: '保守稳重，财库丰厚'
      });
    }
  });
  
  // 检查辅星格局
  const auxiliaryStars = mingGong.stars
    .filter(s => s.type === 'auxiliary')
    .map(s => s.name);
  
  if (auxiliaryStars.includes('左辅') && auxiliaryStars.includes('右弼')) {
    patterns.push({
      name: '左右同宫',
      level: '上格',
      meaning: '得力助手多，事业顺利'
    });
  }
  
  if (auxiliaryStars.includes('文昌') && auxiliaryStars.includes('文曲')) {
    patterns.push({
      name: '昌曲同宫',
      level: '上格',
      meaning: '文采出众，利于考试'
    });
  }
  
  return patterns;
}

/**
 * 分析星曜强弱（传统方法）
 */
function analyzeStarStrengthTraditional(chart) {
  const analysis = {
    strongStars: [],
    weakStars: [],
    balancedStars: []
  };
  
  Object.keys(chart).forEach(palaceName => {
    const palace = chart[palaceName];
    palace.stars.forEach(star => {
      if (star.brightness >= 4) {
        analysis.strongStars.push({
          name: star.name,
          palace: palaceName,
          strength: star.brightness
        });
      } else if (star.brightness <= 2) {
        analysis.weakStars.push({
          name: star.name,
          palace: palaceName,
          strength: star.brightness
        });
      } else {
        analysis.balancedStars.push({
          name: star.name,
          palace: palaceName,
          strength: star.brightness
        });
      }
    });
  });
  
  return analysis;
}

/**
 * 分析三方四正
 */
function analyzeSanfangsizheng(chart) {
  // 三方四正：命宫、财帛宫、官禄宫、迁移宫
  const sanfangPalaces = ['命宫', '财帛宫', '官禄宫', '迁移宫'];
  const analysis = {
    totalStars: 0,
    majorStars: [],
    auxiliaryStars: [],
    maleficStars: [],
    strength: 0
  };
  
  sanfangPalaces.forEach(palaceName => {
    const palace = chart[palaceName];
    if (palace) {
      palace.stars.forEach(star => {
        analysis.totalStars++;
        if (star.type === 'major') {
          analysis.majorStars.push(star.name);
          analysis.strength += star.brightness;
        } else if (star.type === 'auxiliary') {
          analysis.auxiliaryStars.push(star.name);
          analysis.strength += star.brightness * 0.7;
        } else if (star.type === 'malefic') {
          analysis.maleficStars.push(star.name);
          analysis.strength -= star.brightness;
        }
      });
    }
  });
  
  // 判断三方四正强弱
  if (analysis.strength >= 15) {
    analysis.level = '强';
    analysis.description = '三方四正星曜有力，格局较高';
  } else if (analysis.strength >= 8) {
    analysis.level = '中';
    analysis.description = '三方四正星曜平衡，格局中等';
  } else {
    analysis.level = '弱';
    analysis.description = '三方四正星曜不足，需要后天努力';
  }
  
  return analysis;
}

/**
 * 分析各宫位
 */
function analyzePalaces(chart) {
  const analysis = {};
  
  Object.keys(chart).forEach(palaceName => {
    const palace = chart[palaceName];
    const palaceAnalysis = {
      stars: palace.stars.map(s => s.name),
      strength: 0,
      nature: '',
      advice: ''
    };
    
    // 计算宫位强度
    palace.stars.forEach(star => {
      if (star.type === 'major') {
        palaceAnalysis.strength += star.brightness;
      } else if (star.type === 'auxiliary') {
        palaceAnalysis.strength += star.brightness * 0.7;
      } else if (star.type === 'malefic') {
        palaceAnalysis.strength -= star.brightness;
      }
    });
    
    // 根据宫位给出建议
    palaceAnalysis.advice = getPalaceAdvice(palaceName, palaceAnalysis.strength, palace.stars);
    
    analysis[palaceName] = palaceAnalysis;
  });
  
  return analysis;
}

/**
 * 获取宫位建议
 */
function getPalaceAdvice(palaceName, strength, stars) {
  const starNames = stars.map(s => s.name);
  
  switch (palaceName) {
    case '命宫':
      return strength >= 8 ? '命格较高，天生条件好' : '需要后天努力提升自己';
    case '财帛宫':
      return starNames.includes('武曲') ? '财运极佳，适合投资理财' : 
             strength >= 6 ? '财运不错，稳健理财' : '需要开源节流';
    case '官禄宫':
      return starNames.includes('紫微') ? '适合管理职位，前途光明' :
             strength >= 6 ? '事业运佳，宜积极进取' : '需要踏实工作';
    case '夫妻宫':
      return strength >= 6 ? '感情运佳，婚姻美满' : '感情需要用心经营';
    default:
      return strength >= 6 ? '此宫位运势较好' : '此宫位需要注意';
  }
}

/**
 * 计算综合评分
 */
function calculateOverallScore(chart) {
  let totalScore = 0;
  let maxScore = 0;
  
  Object.keys(chart).forEach(palaceName => {
    const palace = chart[palaceName];
    const weight = PALACE_WEIGHTS[palaceName] || 1;
    let palaceScore = 0;
    
    palace.stars.forEach(star => {
      if (star.type === 'major') {
        palaceScore += star.brightness * 2;
      } else if (star.type === 'auxiliary') {
        palaceScore += star.brightness;
      } else if (star.type === 'malefic') {
        palaceScore -= star.brightness;
      }
    });
    
    totalScore += palaceScore * weight;
    maxScore += 10 * weight; // 假设最高分为10
  });
  
  const percentage = Math.max(0, Math.min(100, (totalScore / maxScore) * 100));
  
  return {
    score: Math.round(percentage),
    level: percentage >= 80 ? '优秀' : 
           percentage >= 60 ? '良好' : 
           percentage >= 40 ? '一般' : '需要努力',
    description: getScoreDescription(percentage)
  };
}

/**
 * 获取评分描述
 */
function getScoreDescription(score) {
  if (score >= 80) {
    return '命格优秀，天生条件好，容易获得成功';
  } else if (score >= 60) {
    return '命格良好，具备成功的基础，需要适当努力';
  } else if (score >= 40) {
    return '命格一般，需要通过后天努力来改善运势';
  } else {
    return '命格较弱，需要更多的努力和智慧来创造成功';
  }
}

/**
 * 生成综合总结
 */
function generateSummary(mingGongAnalysis, patternAnalysis, overallScore) {
  let summary = `您的命格属于${mingGongAnalysis.nature}，`;
  
  if (patternAnalysis.length > 0) {
    const topPattern = patternAnalysis[0];
    summary += `具有${topPattern.name}格局，${topPattern.meaning}。`;
  } else {
    summary += `星曜配置较为平常，需要后天努力。`;
  }
  
  summary += `综合评分${overallScore.score}分，${overallScore.description}`;

  return summary;
}

/**
 * 计算命主星
 * @param {number} year - 出生年份
 * @returns {string} 命主星
 */
function calculateMingZhu(year) {
  const mingZhuStars = [
    '贪狼', '巨门', '禄存', '文曲', '廉贞', '武曲',
    '破军', '武曲', '廉贞', '文曲', '禄存', '巨门'
  ];
  const yearBranch = (year - 4) % 12;
  return mingZhuStars[yearBranch];
}

/**
 * 计算身主星
 * @param {number} month - 出生月份
 * @param {number} hour - 出生时辰
 * @returns {string} 身主星
 */
function calculateShenZhu(month, hour) {
  const shenZhuStars = [
    '火星', '天相', '天梁', '天同', '文昌', '天机',
    '火星', '天相', '天梁', '天同', '文昌', '天机'
  ];
  const index = (month + hour - 2) % 12;
  return shenZhuStars[index];
}

/**
 * 计算五行局
 * @param {string} mingGongGanZhi - 命宫干支
 * @returns {Object} 五行局信息
 */
function calculateWuXingJu(mingGongGanZhi) {
  const wuxingJuMap = {
    '甲子': { wuxing: '水', ju: '水二局' },
    '乙丑': { wuxing: '金', ju: '金四局' },
    '丙寅': { wuxing: '火', ju: '火六局' },
    '丁卯': { wuxing: '火', ju: '火六局' },
    '戊辰': { wuxing: '木', ju: '木三局' },
    '己巳': { wuxing: '木', ju: '木三局' },
    '庚午': { wuxing: '土', ju: '土五局' },
    '辛未': { wuxing: '土', ju: '土五局' },
    '壬申': { wuxing: '金', ju: '金四局' },
    '癸酉': { wuxing: '金', ju: '金四局' },
    '甲戌': { wuxing: '火', ju: '火六局' },
    '乙亥': { wuxing: '水', ju: '水二局' }
  };

  return wuxingJuMap[mingGongGanZhi] || { wuxing: '土', ju: '土五局' };
}

/**
 * 分析星曜容貌特征
 * @param {Array} mingGongStars - 命宫星曜
 * @returns {string} 容貌特征描述
 */
function analyzeAppearance(mingGongStars) {
  let appearance = '';

  if (mingGongStars.includes('紫微')) {
    appearance += '面方色紫，中等身材，五官端正，丰满性感，容貌男俊女艳。';
  } else if (mingGongStars.includes('天机')) {
    appearance += '面长色青白，中等身材，眉清目秀，机敏聪慧，举止优雅。';
  } else if (mingGongStars.includes('太阳')) {
    appearance += '面方色红润，中等偏高身材，五官分明，精神饱满，威严大方。';
  } else if (mingGongStars.includes('武曲')) {
    appearance += '面方色白，中等身材，骨格清奇，刚毅果断，英武不凡。';
  } else if (mingGongStars.includes('天同')) {
    appearance += '面圆色白，中等身材，五官清秀，温和可亲，笑容满面。';
  } else if (mingGongStars.includes('廉贞')) {
    appearance += '面长色红，中等身材，五官端正，精明干练，气质不凡。';
  } else if (mingGongStars.includes('天府')) {
    appearance += '面方色黄白，中等偏胖身材，五官端庄，稳重大方，富态十足。';
  } else if (mingGongStars.includes('太阴')) {
    appearance += '面圆色白，中等身材，眉目如画，温柔秀丽，气质优雅。';
  } else if (mingGongStars.includes('贪狼')) {
    appearance += '面长色青白，中等身材，眉目传情，机灵活泼，魅力十足。';
  } else if (mingGongStars.includes('巨门')) {
    appearance += '面方色暗，中等身材，五官分明，严肃庄重，不苟言笑。';
  } else if (mingGongStars.includes('天相')) {
    appearance += '面方色白，中等身材，五官端正，温文尔雅，仪表堂堂。';
  } else if (mingGongStars.includes('天梁')) {
    appearance += '面方色黄，中等偏高身材，五官端庄，慈祥和蔼，长者风范。';
  } else if (mingGongStars.includes('七杀')) {
    appearance += '面方色青，中等身材，五官刚毅，威严肃杀，英气逼人。';
  } else if (mingGongStars.includes('破军')) {
    appearance += '面长色青白，中等身材，五官不定，变化多端，个性鲜明。';
  } else {
    appearance += '五官端正，中等身材，气质平和，容貌清秀。';
  }

  return appearance;
}

/**
 * 分析星曜性格特征
 * @param {Array} mingGongStars - 命宫星曜
 * @returns {string} 性格特征描述
 */
function analyzePersonality(mingGongStars) {
  let personality = '';

  if (mingGongStars.includes('紫微')) {
    personality += '性情较复杂，情绪化，性急好动，不耐静，易冲动，不拘小节，热情奔放，幽默风趣，擅交际应酬，善解人意，好施小恩小惠于人，察言观色，能化敌为友，重义气，守信用，有慈悲济人之心，关心别人。';
  } else if (mingGongStars.includes('天机')) {
    personality += '机智聪明，反应敏捷，善于思考，喜欢学习，求知欲强，多才多艺，适应力强，但容易多疑，缺乏耐心，做事虎头蛇尾。';
  } else if (mingGongStars.includes('太阳')) {
    personality += '性格开朗，热情大方，正直坦率，有正义感，喜欢帮助别人，但有时过于直接，容易得罪人，脾气急躁。';
  } else if (mingGongStars.includes('武曲')) {
    personality += '性格刚毅，意志坚强，做事果断，有责任心，重信用，但有时过于固执，不够灵活，人际关系较差。';
  } else if (mingGongStars.includes('天同')) {
    personality += '性格温和，心地善良，乐观开朗，容易满足，人缘好，但有时过于安逸，缺乏进取心，依赖性强。';
  } else if (mingGongStars.includes('廉贞')) {
    personality += '性格复杂，情绪多变，有时热情，有时冷漠，聪明机智，但容易钻牛角尖，疑心重，人际关系复杂。';
  } else {
    personality += '性格平和，为人诚实，做事认真，有责任心，但有时缺乏主见，容易受他人影响。';
  }

  return personality;
}

module.exports = {
  STAR_PATTERNS,
  comprehensiveZiweiAnalysis,
  calculateMingZhu,
  calculateShenZhu,
  calculateWuXingJu,
  analyzeAppearance,
  analyzePersonality
};
