// 简单测试文件，用于定位问题

console.log('开始简单测试...');

try {
  console.log('1. 测试life-hexagram-calculator...');
  const { calculateLifeHexagram } = require('./miniprogram/utils/life-hexagram-calculator.js');
  console.log('✓ life-hexagram-calculator 导入成功');
  
  console.log('2. 测试time-prediction-engine...');
  const { predictSpecificTimes } = require('./miniprogram/utils/time-prediction-engine.js');
  console.log('✓ time-prediction-engine 导入成功');
  
  console.log('3. 测试dual-verification-system...');
  const { performDualVerification } = require('./miniprogram/utils/dual-verification-system.js');
  console.log('✓ dual-verification-system 导入成功');
  
  console.log('4. 测试基本功能...');
  const birthInfo = { year: 1990, month: 2, day: 15, hour: 14, minute: 30 };
  const lifeHexagram = calculateLifeHexagram(birthInfo);
  console.log(`✓ 终身卦计算成功: ${lifeHexagram.originalHexagram.name}`);
  
  console.log('所有测试通过！');
  
} catch (error) {
  console.error('测试失败:', error.message);
  console.error('错误堆栈:', error.stack);
}
