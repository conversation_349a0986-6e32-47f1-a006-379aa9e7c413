// 紫微斗数大限计算系统
// 基于传统紫微斗数理论

/**
 * 计算紫微斗数大限起运信息
 * @param {Object} ziweiData - 紫微斗数数据
 * @param {boolean} isMale - 是否为男性
 * @param {string} yearStem - 出生年干
 * @returns {Object} 大限起运信息
 */
function calculateZiweiStartLuck(ziweiData, isMale, yearStem) {
  console.log('🔮 [紫微大限] 开始计算大限起运信息...');
  console.log('🔮 [紫微大限] 输入参数:', { 
    wuxingJu: ziweiData.wuxingJu, 
    isMale, 
    yearStem 
  });

  // 获取五行局数
  const wuxingJu = ziweiData.wuxingJu || '火六局';
  const juNumber = getJuNumber(wuxingJu);
  
  console.log('🔮 [紫微大限] 五行局:', wuxingJu, '局数:', juNumber);

  // 确定阴阳
  const YIN_YANG = {
    '甲': '阳', '乙': '阴', '丙': '阳', '丁': '阴', '戊': '阳',
    '己': '阴', '庚': '阳', '辛': '阴', '壬': '阳', '癸': '阴'
  };
  
  const yearYinYang = YIN_YANG[yearStem] || '阳';
  
  // 阳男阴女顺行，阴男阳女逆行
  const isForward = (isMale && yearYinYang === '阳') || (!isMale && yearYinYang === '阴');
  
  console.log('🔮 [紫微大限] 年干阴阳:', yearStem, yearYinYang);
  console.log('🔮 [紫微大限] 大限方向:', isForward ? '顺行' : '逆行');

  // 根据五行局确定起运年龄
  const startAge = juNumber;
  
  console.log('🔮 [紫微大限] 起运年龄:', startAge, '岁');

  return {
    startAge: startAge,
    juNumber: juNumber,
    wuxingJu: wuxingJu,
    direction: isForward ? '顺行' : '逆行',
    isForward: isForward,
    yearStem: yearStem,
    yearYinYang: yearYinYang
  };
}

/**
 * 获取五行局对应的局数
 * @param {string} wuxingJu - 五行局名称
 * @returns {number} 局数
 */
function getJuNumber(wuxingJu) {
  const juMap = {
    '水二局': 2,
    '木三局': 3,
    '金四局': 4,
    '土五局': 5,
    '火六局': 6
  };
  
  // 从纳音中提取局数
  if (wuxingJu.includes('水') || wuxingJu.includes('二')) return 2;
  if (wuxingJu.includes('木') || wuxingJu.includes('三')) return 3;
  if (wuxingJu.includes('金') || wuxingJu.includes('四')) return 4;
  if (wuxingJu.includes('土') || wuxingJu.includes('五')) return 5;
  if (wuxingJu.includes('火') || wuxingJu.includes('六')) return 6;
  
  return 6; // 默认火六局
}

/**
 * 计算紫微斗数大限列表
 * @param {Object} startLuck - 起运信息
 * @param {number} periods - 计算几个大限周期，默认8个
 * @returns {Array} 大限数组
 */
function calculateZiweiDaxian(startLuck, periods = 8) {
  console.log('🔮 [紫微大限] 开始计算大限列表...');
  
  const daxianList = [];
  const palaces = [
    '命宫', '父母宫', '福德宫', '田宅宫', '官禄宫', '仆役宫',
    '迁移宫', '疾厄宫', '财帛宫', '子女宫', '夫妻宫', '兄弟宫'
  ];
  
  for (let i = 0; i < periods; i++) {
    let palaceIndex;
    
    if (startLuck.isForward) {
      // 顺行：命宫 -> 父母宫 -> 福德宫 -> ...
      palaceIndex = i % 12;
    } else {
      // 逆行：命宫 -> 兄弟宫 -> 夫妻宫 -> ...
      palaceIndex = (12 - i) % 12;
    }
    
    const startAge = startLuck.startAge + i * 10;
    const endAge = startAge + 9;
    const palace = palaces[palaceIndex];
    
    daxianList.push({
      period: i + 1,
      palace: palace,
      startAge: startAge,
      endAge: endAge,
      analysis: analyzeDaxianPeriod(palace, startAge, endAge)
    });
  }
  
  console.log('🔮 [紫微大限] 大限列表计算完成:', daxianList);
  return daxianList;
}

/**
 * 分析大限周期特点
 * @param {string} palace - 大限宫位
 * @param {number} startAge - 起始年龄
 * @param {number} endAge - 结束年龄
 * @returns {string} 分析结果
 */
function analyzeDaxianPeriod(palace, startAge, endAge) {
  const palaceAnalysis = {
    '命宫': '主导个人发展，性格展现的重要时期',
    '父母宫': '与长辈关系，学业发展，权威认可的时期',
    '福德宫': '精神享受，福德积累，内心平静的时期',
    '田宅宫': '置业安家，财产积累，家庭稳定的时期',
    '官禄宫': '事业发展，职位提升，社会地位的时期',
    '仆役宫': '人际关系，下属支持，团队合作的时期',
    '迁移宫': '外出发展，环境变化，迁移变动的时期',
    '疾厄宫': '健康关注，身体调养，疾病防范的时期',
    '财帛宫': '财运发展，收入增长，理财投资的时期',
    '子女宫': '子女缘分，创作表现，桃花感情的时期',
    '夫妻宫': '婚姻感情，配偶关系，合作伙伴的时期',
    '兄弟宫': '兄弟朋友，平辈关系，合作竞争的时期'
  };
  
  return palaceAnalysis[palace] || '运势变化的重要时期';
}

/**
 * 获取当前大限
 * @param {Array} daxianList - 大限列表
 * @param {number} currentAge - 当前年龄
 * @returns {Object} 当前大限
 */
function getCurrentDaxian(daxianList, currentAge) {
  return daxianList.find(daxian => 
    currentAge >= daxian.startAge && currentAge <= daxian.endAge
  );
}

module.exports = {
  calculateZiweiStartLuck,
  calculateZiweiDaxian,
  getCurrentDaxian,
  getJuNumber
};
