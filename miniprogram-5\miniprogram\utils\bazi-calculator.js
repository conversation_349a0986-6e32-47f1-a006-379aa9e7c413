// 子平八字计算系统
// 基于传统子平八字理论实现精确的四柱排盘

// 天干地支
const HEAVENLY_STEMS = ['甲', '乙', '丙', '丁', '戊', '己', '庚', '辛', '壬', '癸'];
const EARTHLY_BRANCHES = ['子', '丑', '寅', '卯', '辰', '巳', '午', '未', '申', '酉', '戌', '亥'];

// 五行属性
const FIVE_ELEMENTS = {
  '甲': '木', '乙': '木',
  '丙': '火', '丁': '火', 
  '戊': '土', '己': '土',
  '庚': '金', '辛': '金',
  '壬': '水', '癸': '水',
  '子': '水', '亥': '水',
  '寅': '木', '卯': '木',
  '巳': '火', '午': '火',
  '申': '金', '酉': '金',
  '辰': '土', '戌': '土', '丑': '土', '未': '土'
};

// 阴阳属性
const YIN_YANG = {
  '甲': '阳', '乙': '阴', '丙': '阳', '丁': '阴', '戊': '阳', 
  '己': '阴', '庚': '阳', '辛': '阴', '壬': '阳', '癸': '阴',
  '子': '阳', '丑': '阴', '寅': '阳', '卯': '阴', '辰': '阳',
  '巳': '阴', '午': '阳', '未': '阴', '申': '阳', '酉': '阴', '戌': '阳', '亥': '阴'
};

// 节气数据（简化版，实际应该更精确）
const SOLAR_TERMS = [
  { name: '立春', month: 2, day: 4 },
  { name: '惊蛰', month: 3, day: 6 },
  { name: '清明', month: 4, day: 5 },
  { name: '立夏', month: 5, day: 6 },
  { name: '芒种', month: 6, day: 6 },
  { name: '小暑', month: 7, day: 7 },
  { name: '立秋', month: 8, day: 8 },
  { name: '白露', month: 9, day: 8 },
  { name: '寒露', month: 10, day: 8 },
  { name: '立冬', month: 11, day: 8 },
  { name: '大雪', month: 12, day: 7 },
  { name: '小寒', month: 1, day: 6 }
];

// 月令地支对应
const MONTH_BRANCHES = {
  1: '丑', 2: '寅', 3: '卯', 4: '辰', 5: '巳', 6: '午',
  7: '未', 8: '申', 9: '酉', 10: '戌', 11: '亥', 12: '子'
};

// 时辰对应地支
const HOUR_BRANCHES = {
  23: '子', 1: '丑', 3: '寅', 5: '卯', 7: '辰', 9: '巳',
  11: '午', 13: '未', 15: '申', 17: '酉', 19: '戌', 21: '亥'
};

/**
 * 计算四柱八字
 * @param {Date} birthDate - 出生日期时间
 * @param {boolean} isLunar - 是否为农历（暂时支持公历）
 * @returns {Object} 四柱八字信息
 */
function calculateBazi(birthDate, isLunar = false) {
  const year = birthDate.getFullYear();
  const month = birthDate.getMonth() + 1;
  const day = birthDate.getDate();
  const hour = birthDate.getHours();
  
  // 计算年柱
  const yearPillar = calculateYearPillar(year, month, day);
  
  // 计算月柱
  const monthPillar = calculateMonthPillar(year, month, day);
  
  // 计算日柱
  const dayPillar = calculateDayPillar(year, month, day);
  
  // 计算时柱
  const hourPillar = calculateHourPillar(dayPillar.stem, hour);
  
  return {
    year: yearPillar,
    month: monthPillar,
    day: dayPillar,
    hour: hourPillar,
    birthDate: birthDate,
    dayMaster: dayPillar.stem, // 日主
    elements: {
      year: FIVE_ELEMENTS[yearPillar.stem],
      month: FIVE_ELEMENTS[monthPillar.stem],
      day: FIVE_ELEMENTS[dayPillar.stem],
      hour: FIVE_ELEMENTS[hourPillar.stem]
    }
  };
}

/**
 * 计算年柱
 * 注意：八字以立春为年的分界，不是正月初一
 */
function calculateYearPillar(year, month, day) {
  let actualYear = year;
  
  // 如果在立春之前，年份要减1
  const lichun = getSolarTerm(year, '立春');
  if (month < lichun.month || (month === lichun.month && day < lichun.day)) {
    actualYear = year - 1;
  }
  
  // 计算天干地支（以甲子年为起点）
  const stemIndex = (actualYear - 4) % 10; // 甲子年为公元4年
  const branchIndex = (actualYear - 4) % 12;
  
  return {
    stem: HEAVENLY_STEMS[stemIndex],
    branch: EARTHLY_BRANCHES[branchIndex],
    year: actualYear
  };
}

/**
 * 计算月柱
 * 以节气为准，不是农历月份
 */
function calculateMonthPillar(year, month, day) {
  // 确定真正的月份（以节气为准）
  let actualMonth = month;
  
  // 简化处理：如果在月初节气之前，月份减1
  const currentMonthTerm = getSolarTerm(year, getMonthTerm(month));
  if (day < currentMonthTerm.day) {
    actualMonth = month === 1 ? 12 : month - 1;
  }
  
  // 月干的计算：甲己之年丙作首
  const yearStem = calculateYearPillar(year, month, day).stem;
  const monthStemIndex = getMonthStemIndex(yearStem, actualMonth);
  
  return {
    stem: HEAVENLY_STEMS[monthStemIndex],
    branch: MONTH_BRANCHES[actualMonth],
    month: actualMonth
  };
}

/**
 * 计算日柱
 * 使用万年历算法
 */
function calculateDayPillar(year, month, day) {
  // 简化的万年历算法（实际应该更精确）
  const baseDate = new Date(1900, 0, 31); // 1900年1月31日为甲子日
  const currentDate = new Date(year, month - 1, day);
  const daysDiff = Math.floor((currentDate - baseDate) / (1000 * 60 * 60 * 24));
  
  const stemIndex = daysDiff % 10;
  const branchIndex = daysDiff % 12;
  
  return {
    stem: HEAVENLY_STEMS[stemIndex < 0 ? stemIndex + 10 : stemIndex],
    branch: EARTHLY_BRANCHES[branchIndex < 0 ? branchIndex + 12 : branchIndex],
    day: day
  };
}

/**
 * 计算时柱
 * 根据日干和时辰确定
 */
function calculateHourPillar(dayStem, hour) {
  // 确定时辰地支
  const hourBranch = getHourBranch(hour);
  
  // 时干的计算：甲己还加甲
  const hourStemIndex = getHourStemIndex(dayStem, hourBranch);
  
  return {
    stem: HEAVENLY_STEMS[hourStemIndex],
    branch: hourBranch,
    hour: hour
  };
}

/**
 * 获取节气信息
 */
function getSolarTerm(year, termName) {
  const term = SOLAR_TERMS.find(t => t.name === termName);
  return term || { month: 2, day: 4 }; // 默认立春
}

/**
 * 获取月份对应的节气
 */
function getMonthTerm(month) {
  const termMap = {
    1: '小寒', 2: '立春', 3: '惊蛰', 4: '清明', 5: '立夏', 6: '芒种',
    7: '小暑', 8: '立秋', 9: '白露', 10: '寒露', 11: '立冬', 12: '大雪'
  };
  return termMap[month] || '立春';
}

/**
 * 计算月干索引
 * 甲己之年丙作首，乙庚之岁戊为头
 * 丙辛必定寻庚起，丁壬壬位顺行流
 * 更有戊癸何方觅，甲寅之上好追求
 */
function getMonthStemIndex(yearStem, month) {
  const yearStemIndex = HEAVENLY_STEMS.indexOf(yearStem);
  const baseMap = {
    0: 2, 1: 4, 2: 6, 3: 8, 4: 0, // 甲己丙，乙庚戊，丙辛庚，丁壬壬，戊癸甲
    5: 2, 6: 4, 7: 6, 8: 8, 9: 0
  };
  
  const baseIndex = baseMap[yearStemIndex];
  return (baseIndex + month - 1) % 10;
}

/**
 * 获取时辰地支
 */
function getHourBranch(hour) {
  if (hour >= 23 || hour < 1) return '子';
  if (hour >= 1 && hour < 3) return '丑';
  if (hour >= 3 && hour < 5) return '寅';
  if (hour >= 5 && hour < 7) return '卯';
  if (hour >= 7 && hour < 9) return '辰';
  if (hour >= 9 && hour < 11) return '巳';
  if (hour >= 11 && hour < 13) return '午';
  if (hour >= 13 && hour < 15) return '未';
  if (hour >= 15 && hour < 17) return '申';
  if (hour >= 17 && hour < 19) return '酉';
  if (hour >= 19 && hour < 21) return '戌';
  if (hour >= 21 && hour < 23) return '亥';
  return '子';
}

/**
 * 计算时干索引
 * 甲己还加甲，乙庚丙作初
 * 丙辛从戊起，丁壬庚子居
 * 戊癸何方发，壬子是真途
 */
function getHourStemIndex(dayStem, hourBranch) {
  const dayStemIndex = HEAVENLY_STEMS.indexOf(dayStem);
  const hourBranchIndex = EARTHLY_BRANCHES.indexOf(hourBranch);
  
  const baseMap = {
    0: 0, 1: 2, 2: 4, 3: 6, 4: 8, // 甲己甲，乙庚丙，丙辛戊，丁壬庚，戊癸壬
    5: 0, 6: 2, 7: 4, 8: 6, 9: 8
  };
  
  const baseIndex = baseMap[dayStemIndex];
  return (baseIndex + hourBranchIndex) % 10;
}

/**
 * 格式化八字显示
 */
function formatBazi(bazi) {
  return {
    // 原有的横向显示格式（保持兼容性）
    year: `${bazi.year.stem}${bazi.year.branch}`,
    month: `${bazi.month.stem}${bazi.month.branch}`,
    day: `${bazi.day.stem}${bazi.day.branch}`,
    hour: `${bazi.hour.stem}${bazi.hour.branch}`,
    dayMaster: bazi.dayMaster,

    // 新增的竖向显示格式（天干地支分离）
    yearStem: bazi.year.stem,
    yearBranch: bazi.year.branch,
    monthStem: bazi.month.stem,
    monthBranch: bazi.month.branch,
    dayStem: bazi.day.stem,
    dayBranch: bazi.day.branch,
    hourStem: bazi.hour.stem,
    hourBranch: bazi.hour.branch
  };
}

/**
 * 获取八字的五行分布
 */
function getElementsDistribution(bazi) {
  const elements = ['木', '火', '土', '金', '水'];
  const distribution = {};
  
  elements.forEach(element => {
    distribution[element] = 0;
  });
  
  // 统计天干地支的五行
  [bazi.year, bazi.month, bazi.day, bazi.hour].forEach(pillar => {
    const stemElement = FIVE_ELEMENTS[pillar.stem];
    const branchElement = FIVE_ELEMENTS[pillar.branch];
    
    distribution[stemElement]++;
    distribution[branchElement]++;
  });

  return distribution;
}

module.exports = {
  HEAVENLY_STEMS,
  EARTHLY_BRANCHES,
  FIVE_ELEMENTS,
  YIN_YANG,
  SOLAR_TERMS,
  MONTH_BRANCHES,
  HOUR_BRANCHES,
  calculateBazi,
  formatBazi,
  getElementsDistribution
};
