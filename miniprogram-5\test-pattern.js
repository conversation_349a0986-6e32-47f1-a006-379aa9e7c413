// 测试格局分析系统

const { calculateZiweiChart } = require('./miniprogram/utils/ziwei-calculator.js');
const { analyzeZiweiPatternsEnhanced } = require('./miniprogram/utils/ziwei-enhanced.js');

console.log('🎯 测试格局分析系统');

// 测试数据
const lunarInfo = {
  year: 1990,
  month: 5,
  day: 15,
  hour: 14,
  yearStem: '庚',
  yearBranch: '午',
  gender: '男'
};

try {
  // 计算紫微命盘
  const ziweiData = calculateZiweiChart(lunarInfo);
  console.log('\n=== 命盘基本信息 ===');
  console.log('命宫:', ziweiData.chart['命宫'].branch);
  console.log('命宫星曜:', ziweiData.chart['命宫'].stars.map(s => s.name));
  
  // 测试格局分析
  console.log('\n=== 格局分析测试 ===');
  const patterns = analyzeZiweiPatternsEnhanced(ziweiData.chart);
  console.log('发现格局数量:', patterns.length);
  patterns.forEach((pattern, index) => {
    console.log(`格局${index + 1}:`, pattern);
  });
  
  // 显示各宫位主星分布
  console.log('\n=== 各宫位主星分布 ===');
  Object.keys(ziweiData.chart).forEach(palace => {
    const majorStars = ziweiData.chart[palace].stars
      .filter(star => star.type === '甲级主星')
      .map(star => star.name);
    if (majorStars.length > 0) {
      console.log(`${palace}: ${majorStars.join('、')}`);
    }
  });
  
} catch (error) {
  console.error('测试失败:', error);
}

console.log('\n🎯 格局分析测试完成');
