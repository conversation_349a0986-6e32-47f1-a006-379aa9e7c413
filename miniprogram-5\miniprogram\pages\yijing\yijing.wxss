/* pages/yijing/yijing.wxss - 周易卦象（六爻）页面样式 */

/* 🚨 修复：使用微信小程序支持的选择器隐藏黑色圆圈 */
/* 隐藏可能的确认按钮和调试元素 */
.confirm-btn,
.confirm-button,
.floating-confirm,
.fixed-confirm,
.circle-confirm,
.black-circle,
.debug-circle,
.wx-debug,
.vconsole-panel,
.picker-mask,
.picker-indicator {
  display: none !important;
  visibility: hidden !important;
  opacity: 0 !important;
  pointer-events: none !important;
  z-index: -9999 !important;
  width: 0 !important;
  height: 0 !important;
}

/* 隐藏可能的浮动元素 */
.floating-element,
.fixed-element,
.overlay-element {
  display: none !important;
}

page {
  background: var(--paper-white);
  padding-bottom: 120rpx;
}

.yijing-container {
  min-height: 100vh;
  padding: 40rpx 30rpx;
  font-family: 'STSong', '华文宋体', serif;
}

/* 页面标题 */
.page-header {
  text-align: center;
  margin-bottom: 60rpx;
}

.header-title {
  font-size: 48rpx;
  color: var(--ink-black);
  font-weight: 600;
  margin-bottom: 16rpx;
  font-family: 'STKaiti', '楷体', serif;
}

.header-subtitle {
  font-size: 28rpx;
  color: var(--ink-gray);
  font-weight: 400;
}

/* 通用区块样式 */
.question-section,
.method-section {
  background: var(--paper-white);
  border-radius: 16rpx;
  padding: 32rpx;
  margin-bottom: 24rpx;
  box-shadow: 0 4rpx 16rpx rgba(26, 26, 26, 0.04);
}

.section-title {
  color: var(--ink-black);
  font-size: 32rpx;
  font-weight: 600;
  margin-bottom: 24rpx;
  letter-spacing: 1rpx;
}

.method-desc {
  padding: 24rpx;
  background: var(--ancient-paper);
  border-radius: 12rpx;
  border-left: 4rpx solid var(--ancient-gold);
}

.desc-text {
  color: var(--ink-black);
  font-size: 26rpx;
  line-height: 1.6;
}

/* 输入提示 */
.input-tip {
  color: var(--ink-gray);
  font-size: 24rpx;
  margin-top: 16rpx;
  text-align: center;
  line-height: 1.4;
}

/* 城市选择样式 */
.city-section {
  background: var(--paper-white);
  border-radius: 16rpx;
  padding: 32rpx;
  margin-bottom: 24rpx;
  box-shadow: 0 4rpx 16rpx rgba(26, 26, 26, 0.04);
}

.city-selection {
  margin-top: 20rpx;
}

.city-picker {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx;
  background: var(--paper-white);
  border-radius: 12rpx;
  border: 2rpx solid var(--ink-light);
}

.city-name {
  font-size: 28rpx;
  color: var(--ink-black);
  font-weight: 600;
}

/* 真太阳时信息样式 */
.solar-time-info {
  margin-top: 20rpx;
  padding: 20rpx;
  background: rgba(212, 175, 55, 0.1);
  border-radius: 12rpx;
  border: 1rpx solid var(--ancient-gold);
}

.solar-time-label {
  font-size: 22rpx;
  color: var(--ancient-gold);
  font-weight: 600;
  margin-bottom: 8rpx;
}

.solar-time-text {
  font-size: 20rpx;
  color: var(--ink-gray);
  line-height: 1.4;
  margin-bottom: 8rpx;
}

.solar-time-result {
  font-size: 24rpx;
  color: var(--ink-black);
  font-weight: 600;
  font-family: 'STKaiti', '楷体', serif;
}

/* 起卦方式选择 */
.method-selection {
  background: var(--paper-white);
  border-radius: 20rpx;
  padding: 32rpx;
  margin-bottom: 24rpx;
  box-shadow: 0 4rpx 16rpx rgba(26, 26, 26, 0.04);
}

.method-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20rpx;
  margin-top: 20rpx;
}

.method-item {
  background: var(--ancient-paper);
  border-radius: 12rpx;
  padding: 25rpx;
  text-align: center;
  border: 2rpx solid transparent;
  transition: all 0.3s ease;
  cursor: pointer;
}

.method-item.active {
  background: linear-gradient(135deg, var(--ink-black), var(--ink-gray));
  border-color: var(--ancient-gold);
  color: var(--paper-white);
}

.method-item:hover {
  transform: translateY(-2rpx);
  box-shadow: 0 8rpx 25rpx rgba(26, 26, 26, 0.1);
}

.method-icon {
  font-size: 48rpx;
  margin-bottom: 10rpx;
}

.method-name {
  font-size: 28rpx;
  font-weight: bold;
  margin-bottom: 8rpx;
  color: inherit;
}

.method-item .method-desc {
  font-size: 24rpx;
  color: var(--ink-gray);
  line-height: 1.4;
  padding: 0;
  background: none;
  border: none;
}

.method-item.active .method-desc {
  color: var(--ancient-paper);
}

/* 起卦参数输入 */
.params-section {
  background: var(--paper-white);
  border-radius: 20rpx;
  padding: 32rpx;
  margin-bottom: 24rpx;
  box-shadow: 0 4rpx 16rpx rgba(26, 26, 26, 0.04);
}

.hexagram-selector {
  margin-top: 20rpx;
}

.picker-display {
  background: var(--ancient-paper);
  border-radius: 8rpx;
  padding: 20rpx;
  text-align: center;
  font-size: 28rpx;
  color: var(--ink-black);
  border: 2rpx solid var(--ancient-gold);
}

/* 数字起卦和字数起卦区域 */
.number-section, .text-section {
  background: var(--paper-white);
  border-radius: 20rpx;
  padding: 32rpx;
  margin-bottom: 24rpx;
  box-shadow: 0 4rpx 16rpx rgba(26, 26, 26, 0.04);
}

.number-input, .text-input {
  background: var(--ancient-paper);
  border-radius: 8rpx;
  padding: 20rpx;
  font-size: 28rpx;
  color: var(--ink-black);
  border: 2rpx solid var(--ancient-gold);
  margin-top: 20rpx;
  width: 100%;
  box-sizing: border-box;
  min-height: 80rpx;
  line-height: 40rpx;
}



/* 投币过程 */
.throwing-section {
  background: var(--paper-white);
  border-radius: 20rpx;
  padding: 40rpx;
  margin-bottom: 40rpx;
  box-shadow: 0 8rpx 32rpx rgba(26, 26, 26, 0.06);
}

.throwing-header {
  text-align: center;
  margin-bottom: 40rpx;
}

.throwing-title {
  font-size: 32rpx;
  color: var(--ink-black);
  font-weight: 600;
  margin-bottom: 12rpx;
}

.throwing-progress {
  font-size: 24rpx;
  color: var(--ancient-gold);
  font-weight: 500;
}

.coin-animation {
  text-align: center;
  margin-bottom: 40rpx;
}

/* 投币结果 */
.coin-results {
  margin-top: 40rpx;
}

.results-title {
  font-size: 28rpx;
  color: var(--ink-black);
  font-weight: 600;
  margin-bottom: 24rpx;
  text-align: center;
}

.result-list {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.result-item {
  background: var(--ancient-paper);
  border-radius: 12rpx;
  padding: 20rpx;
  border-left: 4rpx solid var(--ink-light);
}

.result-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12rpx;
}

.result-yao {
  font-size: 26rpx;
  color: var(--ink-black);
  font-weight: 600;
}

.result-type {
  font-size: 24rpx;
  color: var(--ancient-gold);
  font-weight: 500;
}

.result-coins {
  display: flex;
  align-items: center;
  gap: 12rpx;
  margin-bottom: 12rpx;
}

.coin {
  background: var(--ink-light);
  color: var(--paper-white);
  padding: 4rpx 12rpx;
  border-radius: 16rpx;
  font-size: 20rpx;
  font-weight: 500;
}

.coin-total {
  font-size: 22rpx;
  color: var(--ink-gray);
  margin-left: 8rpx;
}

.result-symbol {
  font-size: 32rpx;
  color: var(--ink-black);
  font-family: 'Courier New', monospace;
  text-align: center;
  font-weight: 600;
}

/* 分析中状态 */
.analyzing-section {
  text-align: center;
  padding: 80rpx 40rpx;
}

/* 卦象显示 */
.hexagram-section {
  background: var(--paper-white);
  border-radius: 20rpx;
  padding: 40rpx;
  margin-bottom: 40rpx;
  box-shadow: 0 8rpx 32rpx rgba(26, 26, 26, 0.06);
}

.hexagram-header {
  text-align: center;
  margin-bottom: 40rpx;
  padding-bottom: 20rpx;
  border-bottom: 2rpx solid #f0f0f0;
}

.hexagram-title {
  font-size: 32rpx;
  color: var(--ink-black);
  font-weight: 600;
  margin-bottom: 12rpx;
}

.hexagram-question {
  font-size: 26rpx;
  color: var(--ancient-gold);
  margin-bottom: 8rpx;
  font-weight: 500;
}

.hexagram-time {
  font-size: 24rpx;
  color: var(--ink-gray);
}

/* 六爻显示 */
.hexagram-display {
  text-align: center;
}

.yaos-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16rpx;
  background: var(--ancient-paper);
  border-radius: 16rpx;
  padding: 32rpx;
  border: 2rpx solid var(--ink-light);
}

.yao-item {
  display: flex;
  align-items: center;
  width: 100%;
  padding: 16rpx;
  background: var(--paper-white);
  border-radius: 12rpx;
  box-shadow: 0 2rpx 8rpx rgba(26, 26, 26, 0.04);
}

.yao-position {
  font-size: 24rpx;
  color: var(--ink-gray);
  width: 80rpx;
  text-align: center;
  font-weight: 500;
}

.yao-symbol {
  flex: 1;
  font-size: 36rpx;
  color: var(--ink-black);
  font-family: 'Courier New', monospace;
  text-align: center;
  font-weight: 600;
  letter-spacing: 4rpx;
}

.yao-symbol.changing {
  color: var(--ancient-gold);
  animation: yaoGlow 2s ease-in-out infinite;
}

@keyframes yaoGlow {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.6; }
}

.yao-type {
  font-size: 22rpx;
  color: var(--ink-gray);
  width: 120rpx;
  text-align: center;
}

/* 动爻提示 */
.changing-yaos {
  text-align: center;
  margin-top: 24rpx;
  padding: 16rpx;
  background: rgba(212, 175, 55, 0.1);
  border-radius: 12rpx;
}

.changing-text {
  font-size: 26rpx;
  color: var(--ancient-gold);
  font-weight: 600;
}

/* 分析结果 */
.analysis-section {
  background: var(--paper-white);
  border-radius: 20rpx;
  padding: 40rpx;
  margin-bottom: 40rpx;
  box-shadow: 0 8rpx 32rpx rgba(26, 26, 26, 0.06);
}

.analysis-header {
  margin-bottom: 30rpx;
  padding-bottom: 20rpx;
  border-bottom: 2rpx solid #f0f0f0;
}

.analysis-title {
  font-size: 32rpx;
  color: var(--ink-black);
  font-weight: 600;
  text-align: center;
}

.analysis-content {
  line-height: 1.8;
}

.analysis-text {
  font-size: 28rpx;
  color: var(--ink-black);
  white-space: pre-line;
}

/* 底部操作按钮 */
.bottom-actions {
  margin-top: 40rpx;
}

/* 修复左下角黑圈问题 */
.yijing-container {
  position: relative;
  overflow: hidden;
}

.yijing-container::before,
.yijing-container::after {
  display: none !important;
}

/* 确保手工指定选择器可点击 */
.manual-section .picker-display {
  background: var(--ancient-paper);
  border-radius: 8rpx;
  padding: 20rpx;
  font-size: 28rpx;
  color: var(--ink-black);
  border: 2rpx solid var(--ancient-gold);
  margin-top: 20rpx;
  width: 100%;
  box-sizing: border-box;
  min-height: 80rpx;
  line-height: 40rpx;
  text-align: center;
  cursor: pointer;
}

/* 强制隐藏任何可能的黑色元素 */
view[style*="background: black"],
view[style*="background:black"],
view[style*="background-color: black"],
view[style*="background-color:black"],
view[style*="background: #000"],
view[style*="background:#000"],
view[style*="background-color: #000"],
view[style*="background-color:#000"] {
  display: none !important;
}

/* 隐藏可能的调试元素和浮动元素 */
.debug-panel,
.debug-button,
.floating-button,
.float-button,
[class*="debug"],
[class*="float"],
[class*="fixed"],
[id*="debug"],
[id*="float"] {
  display: none !important;
}

/* 隐藏所有可能的黑色形状元素（排除按钮组件） */
view[style*="border-radius: 50%"]:not(.ink-button),
view[style*="border-radius:50%"]:not(.ink-button),
view[style*="border-radius: 8px"]:not(.ink-button),
view[style*="border-radius:8px"]:not(.ink-button),
.circle:not(.ink-button),
.square:not(.ink-button) {
  display: none !important;
}

/* 隐藏微信开发者工具可能产生的元素 */
[class*="wx-"],
[id*="wx-"],
[class*="vconsole"],
[id*="vconsole"] {
  display: none !important;
}

/* 强力隐藏所有可能的黑色圆形元素 */
view[style*="position: fixed"],
view[style*="position:fixed"],
view[style*="position: absolute"][style*="bottom"],
view[style*="position:absolute"][style*="bottom"],
view[style*="z-index"][style*="border-radius"],
view[style*="background: black"],
view[style*="background:black"],
view[style*="background-color: black"],
view[style*="background-color:black"],
view[style*="background: #000"],
view[style*="background:#000"],
view[style*="background-color: #000"],
view[style*="background-color:#000"] {
  display: none !important;
  visibility: hidden !important;
  opacity: 0 !important;
}

/* 隐藏picker组件可能产生的圆形指示器 */
picker::after,
picker::before,
.picker-indicator,
.picker-mask,
.picker-column::after,
.picker-column::before,
[class*="picker"]::after,
[class*="picker"]::before {
  display: none !important;
}

/* 隐藏所有可能的浮动确认按钮 */
.confirm-button,
.floating-confirm,
.fixed-button,
[class*="confirm"][style*="position"],
[class*="float"][style*="position"],
[id*="confirm"][style*="position"],
[id*="float"][style*="position"] {
  display: none !important;
}

/* 隐藏常见元素的圆形伪元素 */
view::after,
view::before,
text::after,
text::before,
image::after,
image::before {
  border-radius: 0 !important;
  background: transparent !important;
}

/* 确保按钮保持适当的圆角 */
.ink-button,
button,
input,
textarea {
  border-radius: 8rpx !important;
}



/* 手动投币按钮样式 */
.manual-throw-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 40rpx 0;
}

.throw-tip {
  margin-top: 20rpx;
  font-size: 24rpx;
  color: var(--ink-gray);
  text-align: center;
}

/* 卦象选择弹窗样式 */
.hexagram-list-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1000;
}

.modal-mask {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
}

.modal-content {
  position: absolute;
  top: 20%;
  left: 5%;
  width: 90%;
  max-height: 60%;
  background: var(--paper-white);
  border: 2rpx solid var(--ancient-gold);
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.2);
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1rpx solid var(--ink-light);
}

.modal-title {
  font-size: 32rpx;
  font-weight: bold;
  color: var(--ink-black);
}

.modal-close {
  font-size: 40rpx;
  color: var(--ink-gray);
  cursor: pointer;
}

.hexagram-list {
  max-height: 600rpx;
}

.hexagram-item {
  display: flex;
  align-items: center;
  padding: 20rpx 30rpx;
  border-bottom: 1rpx solid var(--ink-light);
  cursor: pointer;
}

.hexagram-item:hover {
  background: var(--paper-light);
}

.hexagram-number {
  width: 80rpx;
  font-size: 24rpx;
  color: var(--ink-gray);
}

.hexagram-name {
  flex: 1;
  font-size: 28rpx;
  color: var(--ink-black);
  margin-left: 20rpx;
}

.hexagram-symbols {
  font-size: 24rpx;
  color: var(--ink-gray);
  font-family: monospace;
}

/* 如果黑色圆圈无法完全隐藏，将其移到页面中央 */
view[style*="position: fixed"][style*="bottom: 0"],
view[style*="position:fixed"][style*="bottom:0"],
view[style*="position: fixed"][style*="left: 0"],
view[style*="position:fixed"][style*="left:0"] {
  left: 50% !important;
  bottom: 50% !important;
  transform: translate(-50%, 50%) !important;
  z-index: 9999 !important;
  background: rgba(0, 0, 0, 0.8) !important;
  color: white !important;
  padding: 20rpx !important;
  border-radius: 50% !important;
  width: 80rpx !important;
  height: 80rpx !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  font-size: 24rpx !important;
}

/* 为移到中央的确认按钮添加文字提示 */
view[style*="position: fixed"][style*="bottom: 50%"]::after {
  content: '确认' !important;
  display: block !important;
  text-align: center !important;
  line-height: 1 !important;
}

/* 🚨 调试元素遮罩层 - 覆盖左下角区域 */
.debug-element-mask {
  position: fixed;
  left: 0;
  bottom: 0;
  width: 200rpx;
  height: 200rpx;
  background: transparent;
  z-index: 99999;
  pointer-events: none;
}

/* 额外的调试元素隐藏 */
.debug-element-mask::before {
  content: '';
  position: absolute;
  left: 0;
  bottom: 0;
  width: 100rpx;
  height: 100rpx;
  background: var(--paper-white);
  border-radius: 50%;
  z-index: 100000;
}

/* 预分析对话面板 */
.conversation-panel {
  background: linear-gradient(135deg, #f8f6f0 0%, #f0ede5 100%);
  border-radius: 24rpx;
  margin: 32rpx 24rpx;
  padding: 32rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
  border: 2rpx solid var(--ancient-gold);
}

.conversation-header {
  text-align: center;
  margin-bottom: 32rpx;
  padding-bottom: 24rpx;
  border-bottom: 2rpx solid var(--ancient-gold);
}

.conversation-title {
  display: block;
  font-size: 36rpx;
  font-weight: 700;
  color: var(--ancient-gold);
  margin-bottom: 12rpx;
}

.conversation-subtitle {
  display: block;
  font-size: 26rpx;
  color: #666;
  line-height: 1.5;
}

.conversation-history {
  max-height: 400rpx;
  overflow-y: auto;
  margin-bottom: 24rpx;
}

.message-item {
  margin-bottom: 24rpx;
  display: flex;
  flex-direction: column;
}

.message-role {
  font-size: 24rpx;
  font-weight: 600;
  margin-bottom: 8rpx;
}

.message-role.assistant {
  color: var(--ancient-gold);
}

.message-role.user {
  color: #2c5aa0;
  text-align: right;
}

.message-content {
  background: white;
  padding: 16rpx 20rpx;
  border-radius: 16rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
  font-size: 28rpx;
  line-height: 1.6;
}

.current-question {
  background: linear-gradient(135deg, #fff9e6 0%, #fff3d3 100%);
  border: 2rpx solid var(--ancient-gold);
  border-radius: 16rpx;
  padding: 24rpx;
  margin-bottom: 24rpx;
}

.question-text {
  font-size: 30rpx;
  font-weight: 600;
  color: #333;
  line-height: 1.6;
  margin-bottom: 16rpx;
}

.question-knowledge {
  background: rgba(255, 255, 255, 0.8);
  border-radius: 12rpx;
  padding: 16rpx;
  border-left: 4rpx solid var(--ancient-gold);
}

.knowledge-label {
  font-size: 24rpx;
  color: var(--ancient-gold);
  font-weight: 600;
}

.knowledge-text {
  font-size: 24rpx;
  color: #666;
  line-height: 1.5;
}

.conversation-input-area {
  display: flex;
  align-items: center;
  gap: 16rpx;
  margin-bottom: 24rpx;
  padding: 16rpx;
  background: white;
  border-radius: 16rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}

.conversation-input {
  flex: 1;
  font-size: 28rpx;
  padding: 12rpx 16rpx;
  border: 2rpx solid #e0e0e0;
  border-radius: 12rpx;
  background: #fafafa;
}

.typing-indicator {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 16rpx;
  padding: 24rpx;
  color: #666;
  font-size: 26rpx;
}

.typing-dots {
  display: flex;
  gap: 8rpx;
}

.dot {
  width: 8rpx;
  height: 8rpx;
  background: var(--ancient-gold);
  border-radius: 50%;
  animation: typingDot 1.4s infinite ease-in-out;
}

.dot:nth-child(1) { animation-delay: -0.32s; }
.dot:nth-child(2) { animation-delay: -0.16s; }

@keyframes typingDot {
  0%, 80%, 100% {
    transform: scale(0);
    opacity: 0.5;
  }
  40% {
    transform: scale(1);
    opacity: 1;
  }
}

.conversation-actions {
  text-align: center;
  padding-top: 16rpx;
  border-top: 1rpx solid #e0e0e0;
}

/* 完整六爻装卦表样式 */
.hexagram-info {
  text-align: center;
  margin-bottom: 30rpx;
  padding: 20rpx;
  background: rgba(139, 69, 19, 0.05);
  border-radius: 12rpx;
}

.hexagram-name {
  font-size: 36rpx;
  font-weight: bold;
  color: var(--ink-dark);
  margin-bottom: 10rpx;
}

.hexagram-method {
  font-size: 24rpx;
  color: var(--ink-medium);
}

.liuyao-table {
  background: var(--paper-white);
  border-radius: 12rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 12rpx rgba(139, 69, 19, 0.1);
  margin-bottom: 30rpx;
}

.table-header {
  display: flex;
  background: var(--ink-dark);
  color: var(--paper-white);
  font-size: 24rpx;
  font-weight: bold;
  padding: 20rpx 0;
}

.table-header .col-position { width: 15%; }
.table-header .col-branch { width: 15%; }
.table-header .col-relative { width: 15%; }
.table-header .col-spirit { width: 15%; }
.table-header .col-symbol { width: 20%; }
.table-header .col-status { width: 20%; }

.table-header > view {
  text-align: center;
}

.yao-row {
  display: flex;
  border-bottom: 1rpx solid rgba(139, 69, 19, 0.1);
  padding: 25rpx 0;
  font-size: 26rpx;
  align-items: center;
}

.yao-row:last-child {
  border-bottom: none;
}

.yao-row > view {
  text-align: center;
  color: var(--ink-dark);
}

.col-position { width: 15%; font-weight: bold; }
.col-branch { width: 15%; color: var(--ink-medium); }
.col-relative { width: 15%; color: #d4691e; }
.col-spirit { width: 15%; color: #8b4513; }
.col-symbol { width: 20%; font-size: 32rpx; font-weight: bold; }
.col-status { width: 20%; }

.col-symbol.changing {
  color: #ff6b35;
  animation: pulse 1.5s infinite;
}

.changing-mark {
  color: #ff6b35;
  font-weight: bold;
  margin-right: 8rpx;
}

.world-mark {
  color: #2e8b57;
  font-weight: bold;
  margin-right: 8rpx;
}

.response-mark {
  color: #4169e1;
  font-weight: bold;
}

/* 卦象分析信息 */
.hexagram-analysis-info {
  display: flex;
  flex-wrap: wrap;
  gap: 20rpx;
  margin-bottom: 30rpx;
  padding: 25rpx;
  background: rgba(139, 69, 19, 0.05);
  border-radius: 12rpx;
}

.analysis-item {
  display: flex;
  align-items: center;
  min-width: 200rpx;
}

.analysis-item .label {
  font-size: 26rpx;
  color: var(--ink-medium);
  margin-right: 10rpx;
}

.analysis-item .value {
  font-size: 26rpx;
  color: var(--ink-dark);
  font-weight: bold;
}

/* 变卦和互卦信息 */
.derived-hexagrams {
  padding: 25rpx;
  background: rgba(139, 69, 19, 0.03);
  border-radius: 12rpx;
  border: 1rpx solid rgba(139, 69, 19, 0.1);
}

.derived-hexagrams .section-title {
  font-size: 28rpx;
  font-weight: bold;
  color: var(--ink-dark);
  margin-bottom: 20rpx;
  text-align: center;
}

.derived-item {
  display: flex;
  align-items: center;
  margin-bottom: 15rpx;
  padding: 15rpx;
  background: var(--paper-white);
  border-radius: 8rpx;
}

.derived-item:last-child {
  margin-bottom: 0;
}

.derived-label {
  font-size: 26rpx;
  color: var(--ink-medium);
  margin-right: 15rpx;
  min-width: 80rpx;
}

.derived-value {
  font-size: 26rpx;
  color: var(--ink-dark);
  font-weight: bold;
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.6; }
}