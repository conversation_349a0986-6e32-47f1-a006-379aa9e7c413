// 传统命理信息计算工具
// 包括纳音、生肖、星座、本命佛、命卦等

// 六十甲子纳音表
const NAYIN_TABLE = {
  '甲子': '海中金', '乙丑': '海中金',
  '丙寅': '炉中火', '丁卯': '炉中火',
  '戊辰': '大林木', '己巳': '大林木',
  '庚午': '路旁土', '辛未': '路旁土',
  '壬申': '剑锋金', '癸酉': '剑锋金',
  '甲戌': '山头火', '乙亥': '山头火',
  '丙子': '涧下水', '丁丑': '涧下水',
  '戊寅': '城头土', '己卯': '城头土',
  '庚辰': '白蜡金', '辛巳': '白蜡金',
  '壬午': '杨柳木', '癸未': '杨柳木',
  '甲申': '泉中水', '乙酉': '泉中水',
  '丙戌': '屋上土', '丁亥': '屋上土',
  '戊子': '霹雳火', '己丑': '霹雳火',
  '庚寅': '松柏木', '辛卯': '松柏木',
  '壬辰': '长流水', '癸巳': '长流水',
  '甲午': '砂中金', '乙未': '砂中金',
  '丙申': '山下火', '丁酉': '山下火',
  '戊戌': '平地木', '己亥': '平地木',
  '庚子': '壁上土', '辛丑': '壁上土',
  '壬寅': '金箔金', '癸卯': '金箔金',
  '甲辰': '覆灯火', '乙巳': '覆灯火',
  '丙午': '天河水', '丁未': '天河水',
  '戊申': '大驿土', '己酉': '大驿土',
  '庚戌': '钗钏金', '辛亥': '钗钏金',
  '壬子': '桑柘木', '癸丑': '桑柘木',
  '甲寅': '大溪水', '乙卯': '大溪水',
  '丙辰': '沙中土', '丁巳': '沙中土',
  '戊午': '天上火', '己未': '天上火',
  '庚申': '石榴木', '辛酉': '石榴木',
  '壬戌': '大海水', '癸亥': '大海水'
};

// 十二生肖
const ZODIAC_ANIMALS = ['鼠', '牛', '虎', '兔', '龙', '蛇', '马', '羊', '猴', '鸡', '狗', '猪'];

// 十二星座
const CONSTELLATIONS = [
  { name: '摩羯座', start: [12, 22], end: [1, 19] },
  { name: '水瓶座', start: [1, 20], end: [2, 18] },
  { name: '双鱼座', start: [2, 19], end: [3, 20] },
  { name: '白羊座', start: [3, 21], end: [4, 19] },
  { name: '金牛座', start: [4, 20], end: [5, 20] },
  { name: '双子座', start: [5, 21], end: [6, 21] },
  { name: '巨蟹座', start: [6, 22], end: [7, 22] },
  { name: '狮子座', start: [7, 23], end: [8, 22] },
  { name: '处女座', start: [8, 23], end: [9, 22] },
  { name: '天秤座', start: [9, 23], end: [10, 23] },
  { name: '天蝎座', start: [10, 24], end: [11, 22] },
  { name: '射手座', start: [11, 23], end: [12, 21] }
];

// 本命佛对照表
const BENMING_BUDDHA = {
  '鼠': '千手观音菩萨',
  '牛': '虚空藏菩萨',
  '虎': '虚空藏菩萨',
  '兔': '文殊菩萨',
  '龙': '普贤菩萨',
  '蛇': '普贤菩萨',
  '马': '大势至菩萨',
  '羊': '大日如来',
  '猴': '大日如来',
  '鸡': '不动尊菩萨',
  '狗': '阿弥陀佛',
  '猪': '阿弥陀佛'
};

// 八卦命卦对照表（简化版）
const MING_GUA = {
  1: '坎命（东四命）',
  2: '坤命（西四命）',
  3: '震命（东四命）',
  4: '巽命（东四命）',
  6: '乾命（西四命）',
  7: '兑命（西四命）',
  8: '艮命（西四命）',
  9: '离命（东四命）'
};

/**
 * 计算纳音
 * @param {string} stem - 天干
 * @param {string} branch - 地支
 * @returns {string} 纳音
 */
function calculateNayin(stem, branch) {
  const ganzhi = stem + branch;
  return NAYIN_TABLE[ganzhi] || '未知';
}

/**
 * 计算生肖
 * @param {number} year - 年份
 * @returns {string} 生肖
 */
function calculateZodiac(year) {
  // 1900年是鼠年
  const baseYear = 1900;
  const index = (year - baseYear) % 12;
  return ZODIAC_ANIMALS[index];
}

/**
 * 计算星座
 * @param {number} month - 月份
 * @param {number} day - 日期
 * @returns {string} 星座
 */
function calculateConstellation(month, day) {
  for (let constellation of CONSTELLATIONS) {
    const { name, start, end } = constellation;
    
    // 处理跨年的星座（如摩羯座）
    if (start[0] > end[0]) {
      if ((month === start[0] && day >= start[1]) || 
          (month === end[0] && day <= end[1])) {
        return name;
      }
    } else {
      if ((month === start[0] && day >= start[1]) || 
          (month === end[0] && day <= end[1]) ||
          (month > start[0] && month < end[0])) {
        return name;
      }
    }
  }
  return '未知';
}

/**
 * 计算本命佛
 * @param {string} zodiac - 生肖
 * @returns {string} 本命佛
 */
function calculateBenmingBuddha(zodiac) {
  return BENMING_BUDDHA[zodiac] || '未知';
}

/**
 * 计算命卦（简化算法）
 * @param {number} year - 出生年份
 * @param {boolean} isMale - 是否为男性
 * @returns {string} 命卦
 */
function calculateMingGua(year, isMale) {
  const lastTwoDigits = year % 100;
  let sum = Math.floor(lastTwoDigits / 10) + (lastTwoDigits % 10);
  
  // 如果和大于9，继续相加
  while (sum > 9) {
    sum = Math.floor(sum / 10) + (sum % 10);
  }
  
  let guaNumber;
  if (isMale) {
    guaNumber = 11 - sum;
    if (guaNumber === 5) guaNumber = 2; // 男性没有5数
  } else {
    guaNumber = sum + 4;
    if (guaNumber > 9) guaNumber -= 9;
    if (guaNumber === 5) guaNumber = 8; // 女性没有5数
  }
  
  return MING_GUA[guaNumber] || '未知';
}

/**
 * 获取生肖吉祥物
 * @param {string} zodiac - 生肖
 * @param {number} year - 当前年份
 * @returns {string} 吉祥物描述
 */
function getZodiacAmulet(zodiac, year = 2025) {
  const amulets = {
    '鼠': `属鼠${year}朱砂吉祥物`,
    '牛': `属牛${year}朱砂吉祥物`,
    '虎': `属虎${year}朱砂吉祥物`,
    '兔': `属兔${year}朱砂吉祥物`,
    '龙': `属龙${year}朱砂吉祥物`,
    '蛇': `属蛇${year}朱砂吉祥物`,
    '马': `属马${year}朱砂吉祥物`,
    '羊': `属羊${year}朱砂吉祥物`,
    '猴': `属猴${year}朱砂吉祥物`,
    '鸡': `属鸡${year}朱砂吉祥物`,
    '狗': `属狗${year}朱砂吉祥物`,
    '猪': `属猪${year}朱砂吉祥物`
  };
  
  return amulets[zodiac] || `属${zodiac}${year}朱砂吉祥物`;
}

module.exports = {
  calculateNayin,
  calculateZodiac,
  calculateConstellation,
  calculateBenmingBuddha,
  calculateMingGua,
  getZodiacAmulet,
  NAYIN_TABLE,
  ZODIAC_ANIMALS,
  CONSTELLATIONS,
  BENMING_BUDDHA,
  MING_GUA
};
