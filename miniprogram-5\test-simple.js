// 简化测试优化功能

console.log('🎯 测试增强算法');

// 测试紫微星定位算法
const { calculateZiweiPositionEnhanced } = require('./miniprogram/utils/ziwei-enhanced.js');

console.log('\n=== 测试紫微星定位算法 ===');

// 测试案例1：火六局3日生
const result1 = calculateZiweiPositionEnhanced(3, 6);
console.log('火六局3日生，紫微星位置:', result1);

// 测试案例2：水二局15日生
const result2 = calculateZiweiPositionEnhanced(15, 2);
console.log('水二局15日生，紫微星位置:', result2);

// 测试案例3：金四局1日生
const result3 = calculateZiweiPositionEnhanced(1, 4);
console.log('金四局1日生，紫微星位置:', result3);

// 测试十四主星安星
const { placeFourteenMajorStarsEnhanced } = require('./miniprogram/utils/ziwei-enhanced.js');

console.log('\n=== 测试十四主星安星 ===');
const majorStars = placeFourteenMajorStarsEnhanced(result1);
console.log('十四主星分布:', majorStars);

// 测试星曜强弱判断
const { analyzeStarStrength } = require('./miniprogram/utils/ziwei-enhanced.js');

console.log('\n=== 测试星曜强弱判断 ===');
const strength1 = analyzeStarStrength('紫微', 0); // 子宫
const strength2 = analyzeStarStrength('太阳', 4); // 巳宫
const strength3 = analyzeStarStrength('太阴', 11); // 亥宫

console.log('紫微在子宫强弱:', strength1);
console.log('太阳在巳宫强弱:', strength2);
console.log('太阴在亥宫强弱:', strength3);

// 测试八字格局分析
console.log('\n=== 测试八字格局分析 ===');
const testBazi = {
  year: { stem: '庚', branch: '午' },
  month: { stem: '辛', branch: '巳' },
  day: { stem: '甲', branch: '子' },
  hour: { stem: '乙', branch: '亥' },
  dayMaster: '甲'
};

const { analyzeTenGods } = require('./miniprogram/utils/bazi-analysis.js');
const tenGodsResult = analyzeTenGods(testBazi);
console.log('十神分析结果:', tenGodsResult);

console.log('\n🎯 简化测试完成');
