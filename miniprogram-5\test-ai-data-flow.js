// 测试AI数据传递完整性
console.log('=== 测试AI数据传递完整性 ===');

// 模拟完整的卦象数据结构（基于修复后的格式）
const mockCompleteHexagram = {
  question: '今年投资股票能赚钱吗？',
  originalLines: [1, 0, 1, 1, 0, 1],
  changedLines: [1, 1, 1, 1, 1, 1],
  changingYaos: [2, 5],
  yaos: [
    { yaoSymbol: '━━━', yaoType: '阳爻', isChanging: false, throw: 1 },
    { yaoSymbol: '━ ━', yaoType: '阴爻', isChanging: true, throw: 2 },
    { yaoSymbol: '━━━', yaoType: '阳爻', isChanging: false, throw: 3 },
    { yaoSymbol: '━━━', yaoType: '阳爻', isChanging: false, throw: 4 },
    { yaoSymbol: '━ ━', yaoType: '阴爻', isChanging: true, throw: 5 },
    { yaoSymbol: '━━━', yaoType: '阳爻', isChanging: false, throw: 6 }
  ],
  liuyaoInfo: {
    hexagramNumber: 19,
    hexagramName: '地火明夷',
    upperTrigram: 2,
    lowerTrigram: 3,
    branches: ['卯', '丑', '亥', '卯', '丑', '亥'],
    worldResponse: { world: 6, response: 3 },
    spirits: ['朱雀', '勾陈', '螣蛇', '白虎', '玄武', '青龙'],
    relatives: ['父母', '子孙', '官鬼', '父母', '子孙', '官鬼'],
    originalLines: [1, 0, 1, 1, 0, 1],
    changedHexagram: { number: 36, name: '地火明夷变地天泰', upperTrigram: 2, lowerTrigram: 1 },
    mutualHexagram: { number: 34, name: '水泽节', upperTrigram: 2, lowerTrigram: 6 },
    voidBranches: ['申', '酉']
  },
  time: '2025/6/30 12:32:00',
  timeObject: new Date(2025, 5, 30, 12, 32, 0),
  changedHexagram: { number: 36, name: '地火明夷变地天泰' },
  mutualHexagram: { number: 34, name: '水泽节' },
  voidBranches: ['申', '酉'],
  method: '时间起卦(2025年6月30日12时)'
};

console.log('\n--- 测试数据完整性验证 ---');

// 测试1: 基本数据结构完整性
console.log('✅ 测试1: 基本数据结构完整性');
const requiredFields = [
  'question', 'originalLines', 'changedLines', 'changingYaos', 'yaos', 
  'liuyaoInfo', 'time', 'timeObject', 'method', 'changedHexagram', 
  'mutualHexagram', 'voidBranches'
];

let missingFields = [];
requiredFields.forEach(field => {
  if (!mockCompleteHexagram.hasOwnProperty(field) || mockCompleteHexagram[field] === null) {
    missingFields.push(field);
  }
});

if (missingFields.length === 0) {
  console.log('✅ 所有必需字段都存在');
} else {
  console.log('❌ 缺失字段:', missingFields);
}

// 测试2: liuyaoInfo完整性
console.log('\n✅ 测试2: liuyaoInfo完整性');
const liuyaoRequiredFields = [
  'hexagramNumber', 'hexagramName', 'upperTrigram', 'lowerTrigram',
  'branches', 'worldResponse', 'spirits', 'relatives', 'originalLines',
  'changedHexagram', 'mutualHexagram', 'voidBranches'
];

let missingLiuyaoFields = [];
liuyaoRequiredFields.forEach(field => {
  if (!mockCompleteHexagram.liuyaoInfo.hasOwnProperty(field) || mockCompleteHexagram.liuyaoInfo[field] === null) {
    missingLiuyaoFields.push(field);
  }
});

if (missingLiuyaoFields.length === 0) {
  console.log('✅ liuyaoInfo所有必需字段都存在');
} else {
  console.log('❌ liuyaoInfo缺失字段:', missingLiuyaoFields);
}

// 测试3: 数组长度验证
console.log('\n✅ 测试3: 数组长度验证');
const arrayFields = [
  { name: 'originalLines', expected: 6 },
  { name: 'changedLines', expected: 6 },
  { name: 'yaos', expected: 6 },
  { name: 'liuyaoInfo.branches', expected: 6 },
  { name: 'liuyaoInfo.spirits', expected: 6 },
  { name: 'liuyaoInfo.relatives', expected: 6 },
  { name: 'liuyaoInfo.originalLines', expected: 6 }
];

arrayFields.forEach(field => {
  const fieldPath = field.name.split('.');
  let value = mockCompleteHexagram;
  fieldPath.forEach(path => {
    value = value[path];
  });
  
  if (Array.isArray(value) && value.length === field.expected) {
    console.log(`✅ ${field.name}: 长度正确 (${value.length})`);
  } else {
    console.log(`❌ ${field.name}: 长度错误 (期望${field.expected}, 实际${value ? value.length : 'undefined'})`);
  }
});

// 测试4: 模拟AI prompt构建
console.log('\n✅ 测试4: AI prompt构建模拟');

function simulateBuildPrompt(question, hexagramContext) {
  const hexagram = hexagramContext.hexagram || hexagramContext;
  
  // 检查关键信息
  const checks = {
    '问题存在': !!question,
    '卦象存在': !!hexagram,
    '装卦信息存在': !!hexagram.liuyaoInfo,
    '地支存在': !!(hexagram.liuyaoInfo && hexagram.liuyaoInfo.branches),
    '六亲存在': !!(hexagram.liuyaoInfo && hexagram.liuyaoInfo.relatives),
    '六神存在': !!(hexagram.liuyaoInfo && hexagram.liuyaoInfo.spirits),
    '世应存在': !!(hexagram.liuyaoInfo && hexagram.liuyaoInfo.worldResponse),
    '动爻存在': !!(hexagram.changingYaos && hexagram.changingYaos.length >= 0),
    '时间对象存在': !!hexagram.timeObject,
    '变卦存在': !!hexagram.changedHexagram,
    '互卦存在': !!hexagram.mutualHexagram,
    '空亡存在': !!hexagram.voidBranches
  };
  
  console.log('AI prompt构建检查:');
  Object.entries(checks).forEach(([key, value]) => {
    console.log(`  ${value ? '✅' : '❌'} ${key}`);
  });
  
  return Object.values(checks).every(v => v);
}

const promptBuildSuccess = simulateBuildPrompt(mockCompleteHexagram.question, { hexagram: mockCompleteHexagram });
console.log(`\nAI prompt构建结果: ${promptBuildSuccess ? '✅ 成功' : '❌ 失败'}`);

// 测试5: 不同起卦方法的数据结构一致性
console.log('\n✅ 测试5: 起卦方法数据结构一致性');

const methods = ['铜钱摇卦', '时间起卦', '数字起卦', '字数起卦', '手工指定', '电脑摇卦'];
methods.forEach(method => {
  const testHexagram = { ...mockCompleteHexagram, method: method };
  const isConsistent = requiredFields.every(field => testHexagram.hasOwnProperty(field));
  console.log(`  ${isConsistent ? '✅' : '❌'} ${method}: 数据结构${isConsistent ? '一致' : '不一致'}`);
});

console.log('\n--- 修复效果预期 ---');
console.log('🎯 修复后应该实现:');
console.log('- AI分析不再显示"卦象信息不全"');
console.log('- 所有起卦方法都传递完整的装卦信息');
console.log('- AI能够获取主卦、变卦、互卦、空亡等完整信息');
console.log('- AI能够获取准确的阳历和阴历时间信息');
console.log('- AI分析基于真实卦象数据，不再使用"假设"');

console.log('\n=== AI数据传递完整性测试完成 ===');
